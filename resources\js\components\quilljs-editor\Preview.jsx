import React from 'react';
import DOMPurify from 'dompurify';
import './EditorStyles.css';

const Preview = ({ content }) => {
  // 1. Sanitize the HTML content to prevent XSS attacks
  const sanitizedContent = DOMPurify.sanitize(content);
  console.log('Preview content:',content);
  console.log(sanitizedContent);

  return (
    // 2. Apply the same styling class used for the editor's output
    <div 
      className="ql-snow" // Use the theme class for base styles
    >
      <div 
        className="ql-editor" // Use the editor class for content styles
        dangerouslySetInnerHTML={{ __html: sanitizedContent }} 
      />
    </div>
  );
};

export default Preview;