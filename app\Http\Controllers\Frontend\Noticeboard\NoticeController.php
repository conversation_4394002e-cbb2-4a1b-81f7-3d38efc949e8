<?php

namespace App\Http\Controllers\Frontend\Noticeboard;

use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use App\Http\Resources\NoticeboardResource;
use Inertia\Inertia;

class NoticeController extends Controller
{
    //
    public function index()
    {
        $noticeBoardData = NoticeboardResource::collection(Noticeboard::latest()->paginate(5));
        return Inertia::render('public/noticeboard/index', [
            'noticeBoardData' => $noticeBoardData,
        ]);
    }

    public function show($id, $slug)
    {
        $noticeDataItem = Noticeboard::where('id', $id)->where('slug', $slug)->first();
        $noticeData = NoticeboardResource::make($noticeDataItem);
        return inertia('public/noticeboard/show', [
            'noticeDataItem' => $noticeData,
        ]);
    }
}

