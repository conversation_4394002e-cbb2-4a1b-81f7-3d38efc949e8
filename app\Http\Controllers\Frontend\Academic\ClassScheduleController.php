<?php

namespace App\Http\Controllers\Frontend\Academic;

use App\Http\Controllers\Controller;
use App\Models\ClassSchedule;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ClassScheduleController extends Controller
{
    //
    public function index(Request $request)
    {
        // Get all class schedules from database
        $schedules = ClassSchedule::orderBy('department')
            ->orderBy('batch')
            ->get();

        // Group by department and format for frontend with proper accordion names
        $classSchedule = $schedules->groupBy('department')->map(function ($departmentSchedules, $department) {
            return [
                'id' => crc32($department), // Generate a unique ID for each department
                'dept' => $department,
                'schedule' => $departmentSchedules->map(function ($schedule) {
                    // Create accordion name: "CSE-1 (2019-2020)"
                    $accordionName = $schedule->department . '-' . $schedule->batch . ' (' . $schedule->session . ')';

                    // Validate if the embed link is a proper Google Calendar link
                    $isValidEmbedLink = $this->isValidGoogleCalendarLink($schedule->scheduleEmbedLink);

                    return [
                        'batch' => $accordionName, // Use formatted name instead of just batch
                        'mediaType' => 'embed',
                        'link' => $schedule->scheduleEmbedLink,
                        'session' => $schedule->session,
                        'isValidLink' => $isValidEmbedLink,
                        'dept' => $schedule->department, // Keep original dept for filtering
                    ];
                })->values()->toArray()
            ];
        })->values()->toArray();

        return Inertia::render('public/academic/class-schedule/index', [
            'classSchedule' => $classSchedule,
        ]);
    }

    /**
     * Validate if the provided link is a valid Google Calendar embed link
     */
    private function isValidGoogleCalendarLink($link)
    {
        if (empty($link) || !is_string($link)) {
            return false;
        }

        // Check if it's a valid URL and contains Google Calendar embed pattern
        return filter_var($link, FILTER_VALIDATE_URL) &&
               (strpos($link, 'google.com/calendar/embed') !== false ||
                strpos($link, 'calendar.google.com/calendar/embed') !== false);
    }

   

}
