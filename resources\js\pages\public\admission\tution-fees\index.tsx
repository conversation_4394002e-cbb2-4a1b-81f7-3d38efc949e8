import { Head } from '@inertiajs/react';
import { AlertTriangle, ChevronDown, ChevronRight, Download, Highlighter, Check } from 'lucide-react';
import { useState } from 'react';
import Back from '../../common/back/Back';
import { TuitionFeesData } from '@/types';

// Define a placeholder for your Back component if needed

// Define the type for each accordion item for type safety

const TuitionFeesPage = ({ tuitionFees }: { tuitionFees: TuitionFeesData[] }) => {
    const [activeIndex, setActiveIndex] = useState<number | null>(null); // Open the first item by default

    

    // Reordered and updated key points
    const keyPoints = [
        { text: 'Merit-based scholarship available', icon: Check },
        { text: 'Waiver on admission available', icon: Highlighter },
        { text: 'Tuition fee structure is subject to change', icon: AlertTriangle },
        { text: 'National University Fees are excluded', icon: AlertTriangle },
    ];

    const toggleItem = (index: number) => {
        setActiveIndex(activeIndex === index ? null : index);
    };

    // Function to handle the download
    const handleDownload = (imageSrc: string | null, title: string) => {
        if (!imageSrc) {
            return; // Handle the case where imageSrc is null or undefined
        } else {
            const link = document.createElement('a');
            link.href = imageSrc;
            const fileName = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.jpg`;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }        
    };

    return (
        <>
            <Head title="Tuition Fees" />
            <Back title="Tuition Fees" />
            <div className="container mx-auto max-w-5xl px-4 py-24 md:w-[95%]">
                <h3 className="text-2xl font-semibold text-[#001234]">NIST Tuition Fees Structure:</h3>

                {/* Restyled Disclaimer Section with Green Theme */}
                <div className="my-6 rounded-lg border border-green-300 bg-green-50 p-4">
                    <ul className="space-y-2">
                        {keyPoints.map((point, index) => {
                            const Icon = point.icon;
                            return (
                                <li key={index} className="flex items-center text-green-800">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-green-700 text-white">
                                        <Icon size={18} />
                                    </span>
                                    <span className="pl-2 font-medium">{point.text}</span>
                                </li>
                            );
                        })}
                    </ul>
                </div>

                <div className="space-y-2.5">
                    {tuitionFees.map((item) => (
                        <div key={item.id} className="overflow-hidden rounded-md bg-[#2c3e50] text-white">
                            <button
                                onClick={() => toggleItem(item.id)}
                                className="flex w-full cursor-pointer items-center justify-between p-4 text-left text-sm font-medium transition-colors select-none hover:bg-[#34495e] md:text-base"
                                aria-expanded={activeIndex === item.id}
                            >
                                <span>{item.program_name}</span>
                                <span>{activeIndex === item.id ? <ChevronDown /> : <ChevronRight />}</span>
                            </button>
                            <div
                                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                                    activeIndex === item.id ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
                                }`}
                            >
                                <div className="bg-[#ecf0f1] p-4">
                                    <img src={item.image} alt={`${item.program_name} Fee Structure`} className="h-auto w-full" />
                                    <div className="mt-4 text-right">
                                        <button
                                            onClick={() => handleDownload(item.image, item.program_name)}
                                            className="inline-flex items-center gap-2 rounded-md bg-green-700 px-4 py-2 text-sm font-semibold text-white shadow-md transition-colors hover:bg-green-800 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none"
                                        >
                                            <Download size={16} />
                                            Download
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default TuitionFeesPage;
