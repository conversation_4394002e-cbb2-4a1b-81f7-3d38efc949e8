# Confirmation Modal Usage Guide

This guide shows how to implement the reusable confirmation modal across all admin sections for delete, edit, create, and save operations.

## Import and Setup

```tsx
import ConfirmationModal, { useConfirmationModal } from '@/components/ui/confirmation-modal';

export default function YourComponent() {
    const { showConfirmation, ConfirmationModal } = useConfirmationModal();
    
    // Your component logic here
    
    return (
        <div>
            {/* Your component JSX */}
            
            {/* Add this at the end of your JSX */}
            <ConfirmationModal />
        </div>
    );
}
```

## Usage Examples

### 1. Delete Confirmation
```tsx
const handleDelete = (id: number) => {
    showConfirmation({
        title: 'Delete Item',
        description: 'Are you sure you want to delete this item? This action cannot be undone.',
        confirmText: 'Delete',
        variant: 'destructive',
        onConfirm: async () => {
            router.delete(route('admin.items.destroy', id));
        }
    });
};
```

### 2. Edit/Save Confirmation
```tsx
const handleSave = () => {
    showConfirmation({
        title: 'Save Changes',
        description: 'Are you sure you want to save these changes?',
        confirmText: 'Save Changes',
        variant: 'default',
        onConfirm: async () => {
            // Your save logic here
            router.post(route('admin.items.update', id), formData);
        }
    });
};
```

### 3. Create Confirmation
```tsx
const handleCreate = () => {
    showConfirmation({
        title: 'Create Item',
        description: 'Are you sure you want to create this item?',
        confirmText: 'Create',
        variant: 'default',
        onConfirm: async () => {
            router.post(route('admin.items.store'), formData);
        }
    });
};
```

## Admin Sections to Update

Apply the confirmation modal to these sections:

### 1. Noticeboard (`resources/js/pages/admin/noticeboard/`)
- Delete confirmation for notice deletion
- Save confirmation for notice creation/editing

### 2. News (`resources/js/pages/admin/news/`)
- Delete confirmation for news deletion
- Save confirmation for news creation/editing

### 3. Admission (`resources/js/pages/admin/admission/`)
- Delete confirmation for all admission-related items
- Save confirmation for admission posts, spots, etc.

### 4. Schedule Manager (`resources/js/pages/admin/schedule/`)
- Delete confirmation for schedule deletion
- Save confirmation for schedule creation/editing

### 5. Faculty Profile (`resources/js/pages/admin/faculty/`)
- Delete confirmation for faculty deletion
- Save confirmation for faculty creation/editing

### 6. Governing Body (`resources/js/pages/admin/governingbody/`)
- Delete confirmation for governing body member deletion
- Save confirmation for member creation/editing

### 7. File Upload Section
- Delete confirmation for file deletion
- Upload confirmation for file uploads

### 8. Access Control List
- Delete confirmation for permission/role deletion
- Save confirmation for permission/role changes

## Implementation Steps

1. **Import the modal and hook** at the top of your component
2. **Initialize the hook** in your component function
3. **Replace existing confirm() calls** with showConfirmation()
4. **Add the ConfirmationModal component** at the end of your JSX
5. **Test the implementation** to ensure proper functionality

## Benefits

- **Consistent UI**: All confirmations look and behave the same
- **Better UX**: More professional and accessible than browser confirm()
- **Customizable**: Easy to customize messages and button text
- **Accessible**: Proper ARIA labels and keyboard navigation
- **Responsive**: Works well on all screen sizes

## Example Implementation

Here's a complete example for a typical admin component:

```tsx
import React from 'react';
import { router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import ConfirmationModal, { useConfirmationModal } from '@/components/ui/confirmation-modal';

export default function AdminComponent({ items }) {
    const { showConfirmation, ConfirmationModal } = useConfirmationModal();

    const handleDelete = (id: number, title: string) => {
        showConfirmation({
            title: 'Delete Item',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
            onConfirm: async () => {
                router.delete(route('admin.items.destroy', id));
            }
        });
    };

    const handleSave = (formData: any) => {
        showConfirmation({
            title: 'Save Changes',
            description: 'Are you sure you want to save these changes?',
            confirmText: 'Save',
            variant: 'default',
            onConfirm: async () => {
                router.post(route('admin.items.store'), formData);
            }
        });
    };

    return (
        <div>
            {/* Your component content */}
            {items.map(item => (
                <div key={item.id}>
                    <span>{item.title}</span>
                    <Button 
                        variant="destructive" 
                        onClick={() => handleDelete(item.id, item.title)}
                    >
                        Delete
                    </Button>
                </div>
            ))}
            
            {/* Add the confirmation modal */}
            <ConfirmationModal />
        </div>
    );
}
```

This approach ensures consistent user experience across all admin sections while maintaining clean, reusable code.
