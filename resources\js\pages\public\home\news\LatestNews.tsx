import { News } from '@/types';
import { Link } from '@inertiajs/react'; // Using Inertia's Link for SPA navigation
import { CalendarDays, User } from 'lucide-react';


// Define the type for a blog item for type safety
interface BlogItem {
    id: number;
    cover: string;
    title: string;
    type: string;
    date: string;
    desc: string;
}

interface NewsData {
    data: News[];
}

// Dummy data for demonstration


// Placeholder for your Heading component
const Heading = ({ subtitle, title }: { subtitle: string; title: string }) => (
    <div className="mb-12 text-center">
        <h3 className="text-lg font-medium text-orange-500 uppercase">{subtitle}</h3>
        <h2 className="text-4xl font-bold text-gray-800">{title}</h2>
    </div>
);

const LatestNews = ({ blogs }: { blogs: NewsData }) => {
    return (
        <section className="bg-gray-50 pt-24 pb-12">
            <div className="container mx-auto px-4">
                <Heading subtitle="NEWS" title="Latest News & Events" />

                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {blogs.data.slice(0, 3).map((val) => (
                        <div key={val.id} className="overflow-hidden rounded-lg bg-white shadow-lg">
                            <div className="h-64 overflow-hidden hover:scale-105 transition-transform duration-300 ease-in-out">
                                <img src={val.image} alt={val.title} className="h-full w-full object-cover" />
                            </div>
                            <div className="p-8">
                                <div className="flex items-center justify-between text-sm">
                                    <span className="flex items-center gap-2 font-medium text-green-600 uppercase">
                                        <User size={16} />
                                        <label className="text-gray-500">{'NIST'}</label>
                                    </span>
                                    <span className="flex items-center gap-2 font-medium text-green-600 uppercase">
                                        <CalendarDays size={16} />
                                        <label className="text-gray-500">{val.created_at}</label>
                                    </span>
                                </div>

                                <Link href={`/news/${val.id}/${val.slug}`}>
                                    <h1 className="my-5 cursor-pointer text-xl leading-tight font-semibold text-[#001234] transition-colors duration-300 hover:text-green-600">
                                        {val.title}
                                    </h1>
                                </Link>

                                <p className="text-gray-600">{val.excerpt??'Read More'.substring(0, 100)} ...</p>
                            </div>
                        </div>
                    ))}
                </div>
                <div className="mt-8 flex justify-center">
                    <Link href="/news" className="rounded-4xl bg-green-600 px-4 py-4 text-sm text-center font-medium w-50 h-14 text-white hover:bg-green-700 md:text-base">
                        SEE ALL NEWS
                    </Link>
                </div>
            </div>
        </section>
    );
};

export default LatestNews;
