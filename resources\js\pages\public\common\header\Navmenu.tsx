import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { FaBars, FaEnvelope, FaPhone, FaTimes, FaUser, FaGraduationCap, FaTachometerAlt } from 'react-icons/fa';
import { FiChevronDown } from 'react-icons/fi';

// Define the types for the navigation links for better code quality
interface SubMenuLink {
    text: string;
    href: string;
}

interface NavLink {
    text: string;
    href: string;
    hasDropdown?: boolean;
    subMenu?: SubMenuLink[];
}

const Navmenu = () => {
    // Type the usePage hook for autocompletion and type safety
    const { auth } = usePage<SharedData>().props;
    const { url } = usePage();

    // State for the main mobile menu visibility
    const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
    // State to manage which dropdown is open in the mobile menu (can be a string or null)
    const [openDropdown, setOpenDropdown] = useState<string | null>(null);
    // State for mobile menu animation
    const [isMenuAnimating, setIsMenuAnimating] = useState<boolean>(false);

    // Strongly-typed navigation data
    const navLinks: NavLink[] = [
        { text: 'Home', href: '/' },
        {
            text: 'About',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'About Us', href: '/about-us' },
                { text: 'At A Glance', href: '/at-a-glance' },
                { text: 'Principal Message', href: '/principal-message' },
                { text: 'Governing Body', href: '/governing-body' },
            ],
        },
        {
            text: 'Admission',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'Admission Info', href: '/admission-info' },
                { text: 'Apply Online', href: '/application-form' },
                { text: 'Tuition Fees', href: '/tuition-fees' },
            ],
        },
        {
            text: 'Courses',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'Computer Science and Engineering (CSE)', href: '/courses-cse' },
                { text: 'Electronics and Communication Engineering (ECE)', href: '/courses-ece' },
                { text: 'Bachelor of Business Administration (BBA)', href: '/courses-bba' },
                { text: 'Biochemistry & Molecular Biology (BMB)', href: '/courses-bmb' },
            ],
        },
        {
            text: 'Academic',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'Class Schedule', href: '/class-schedule' },
                { text: 'Faculty Schedule', href: '/faculty-schedule' },
                { text: 'Faculty Member', href: '/faculty-member' },
            ],
        },
        {
            text: 'Noticeboard',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'All Notice', href: '/noticeboard' },
                { text: 'NU Notice', href: 'https://www.nu.ac.bd/notice-general.php' },
                { text: 'Results', href: '/results' },
            ],
        },
        {
            text: 'Campus',
            href: '#',
            hasDropdown: true,
            subMenu: [
                { text: 'News & Event', href: '/news' },
                { text: 'Campus Life', href: '/campus-life' },
                { text: 'Achievement', href: '/achievement' },
            ],
        },
        { text: 'Contact', href: '/contact' },
    ];

    // Handle mobile menu open with animation and delay
    const handleMenuOpen = () => {
        setIsMenuOpen(true);
        // Add delay before starting the slide animation
        setTimeout(() => {
            setIsMenuAnimating(true);
        }, 50); // Small delay for smoother animation start
    };

    // Handle mobile menu close with animation
    const handleMenuClose = () => {
        setIsMenuAnimating(false);
        setTimeout(() => {
            setIsMenuOpen(false);
            setOpenDropdown(null); // Close all dropdowns when menu closes
        }, 350); // Slightly longer to ensure animation completes
    };

    // Toggles which mobile dropdown is open, with the argument explicitly typed as a string
    const handleDropdownToggle = (dropdownName: string) => {
        setOpenDropdown(openDropdown === dropdownName ? null : dropdownName);
    };

    // Close menu when clicking on a link
    const handleLinkClick = () => {
        handleMenuClose();
    };

    // Reset animation state when menu closes completely
    useEffect(() => {
        if (!isMenuOpen) {
            setIsMenuAnimating(false);
        }
    }, [isMenuOpen]);

    // Prevent body scroll when menu is open
    useEffect(() => {
        if (isMenuOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        // Cleanup on unmount
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isMenuOpen]);

    return (
        <>
            {/* ======================= Top Bar ======================= */}
            <div className="hidden justify-center bg-[#0a4727] py-1 text-bold text-gray-200 lg:flex">
                <div className="flex w-full max-w-7xl items-center justify-between px-4">
                    <div className="flex items-center gap-x-4">
                        <a href="tel:01713493163" className="flex items-center gap-x-2 transition-colors hover:text-white">
                            <FaPhone className="text-[#FFD43B]" />
                            <span>01713493163</span>
                        </a>
                        <span className="text-gray-200">|</span>
                        <a href="tel:01811458853" className="flex items-center gap-x-2 transition-colors hover:text-white">
                            <FaPhone className="text-[#FFD43B]" />
                            <span>01811458853</span>
                        </a>
                        <span className="text-gray-200">|</span>
                        <a href="mailto:<EMAIL>" className="flex items-center gap-x-2 transition-colors hover:text-white">
                            <FaEnvelope className="text-[#FFD43B]" />
                            <span><EMAIL></span>
                        </a>
                    </div>
                    <div className="flex items-center gap-x-6">
                        <nav className="flex items-center justify-end gap-4">
                            {auth.user ? (
                                <Link
                                    href={route('admin.dashboard')}
                                    className="flex items-center gap-x-2 rounded-sm border border-gray-200 px-5 py-1.5 text-sm leading-normal text-gray-200 transition-all duration-300 hover:text-[#FFD43B] hover:border-[#FFD43B]"
                                >
                                    <FaTachometerAlt className="text-[#FFD43B]" />
                                    <span>Dashboard</span>
                                </Link>
                            ) : (
                                <Link
                                    href={route('login')}
                                    className="flex items-center rounded-sm gap-x-2 border border-transparent px-5 py-1.5 text-sm leading-normal text-[#fff] transition-all duration-300 hover:text-[#FFD43B] hover:border-[#FFD43B]"
                                >
                                    <FaUser className="text-[#FFD43B]" />
                                    <span>Login</span>
                                </Link>
                            )}
                        </nav>

                        <Link
                            href="/application-form"
                            className="flex items-center gap-x-2 rounded-md border border-transparent bg-[#fdc800] px-4 py-2 text-sm font-semibold text-[#0a4727] uppercase transition-all duration-300 hover:border-[#FFBC42] hover:bg-[#0a4727] hover:text-[#FFBC42] hover:scale-105"
                        >
                            <FaGraduationCap />
                            <span>Apply Online</span>
                        </Link>
                    </div>
                </div>
            </div>

            {/* ======================= Main Navigation ======================= */}
            <header className="sticky top-0 z-40 bg-white shadow-md">
                <nav className="mx-auto flex w-full max-w-7xl items-center justify-between px-4 py-1">
                    <Link href="/">
                        <img src="/images/NIST-logo.png" alt="NIST Logo" className="h-18 w-auto" />
                    </Link>

                    {/* Desktop Menu with Hover Dropdowns */}
                    <ul className="hidden items-center gap-x-6 lg:flex">
                        {navLinks.map((link) => (
                            <li key={link.text} className="group relative">
                                <Link
                                    href={link.href}
                                    className={`flex items-center gap-x-1 py-4 font-semibold text-gray-600 transition-colors hover:text-[#F59E0B] ${url === link.href ? 'text-[#F59E0B]' : ''}`}
                                >
                                    {link.text}
                                    {link.hasDropdown && <FiChevronDown size={14} />}
                                </Link>
                                {link.hasDropdown && link.subMenu && (
                                    <ul className="absolute top-full left-0 mt-0 hidden w-56 rounded-b-md bg-white py-2 shadow-lg transition-all duration-300 group-hover:block">
                                        {link.subMenu.map((subLink) => (
                                            <li key={subLink.text}>
                                                <Link
                                                    href={subLink.href}
                                                    className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 hover:text-[#F59E0B]"
                                                >
                                                    {subLink.text}
                                                </Link>
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </li>
                        ))}
                    </ul>

                    {/* Hamburger Button */}
                    <div className="lg:hidden">
                        <button
                            onClick={handleMenuOpen}
                            className="text-3xl text-gray-700 transition-transform duration-200 hover:scale-110 active:scale-95"
                        >
                            <FaBars />
                        </button>
                    </div>
                </nav>
            </header>

            {/* ======================= Mobile Menu Drawer ======================= */}
            {isMenuOpen && (
                <>
                    {/* Backdrop */}
                    <div
                        className={`fixed inset-0 z-40 bg-black transition-opacity duration-400 ease-out lg:hidden ${
                            isMenuAnimating ? 'opacity-50' : 'opacity-0'
                        }`}
                        onClick={handleMenuClose}
                    />

                    {/* Mobile Menu */}
                    <div className={`fixed inset-y-0 right-0 z-50 w-80 max-w-[85vw] bg-[#0A1828] text-white shadow-2xl transform transition-all duration-400 ease-out lg:hidden ${
                        isMenuAnimating
                            ? 'translate-x-0 opacity-100'
                            : 'translate-x-full opacity-90'
                    }`}>
                        {/* Drawer Header */}
                        <div className="flex justify-end border-b border-gray-700 p-4">
                            <button
                                onClick={handleMenuClose}
                                className="text-2xl transition-transform duration-200 hover:scale-110 active:scale-95"
                            >
                                <FaTimes />
                            </button>
                        </div>

                        {/* Menu List */}
                        <div className="flex-grow overflow-y-auto">
                            <ul className="p-4">
                                {navLinks.map((link, index) => (
                                    <li
                                        key={link.text}
                                        className={`border-b border-gray-700 transform transition-all duration-300 ${
                                            isMenuAnimating
                                                ? 'translate-x-0 opacity-100'
                                                : 'translate-x-8 opacity-0'
                                        }`}
                                        style={{
                                            transitionDelay: isMenuAnimating ? `${index * 50}ms` : '0ms'
                                        }}
                                    >
                                        {link.hasDropdown && link.subMenu ? (
                                            <div>
                                                <button
                                                    onClick={() => handleDropdownToggle(link.text)}
                                                    className="flex w-full items-center justify-between py-3 font-semibold uppercase transition-all duration-200 hover:text-[#FFD43B] hover:bg-gray-800 hover:px-2 rounded"
                                                >
                                                    {link.text}
                                                    <span className={`transition-transform duration-300 ease-out ${
                                                        openDropdown === link.text ? 'rotate-180' : 'rotate-0'
                                                    }`}>
                                                        <FiChevronDown />
                                                    </span>
                                                </button>
                                                <div className={`overflow-hidden transition-all duration-400 ease-out ${
                                                    openDropdown === link.text
                                                        ? 'max-h-96 opacity-100 mt-2'
                                                        : 'max-h-0 opacity-0 mt-0'
                                                }`}>
                                                    <ul className="pb-2 pl-4 space-y-1">
                                                        {link.subMenu.map((subLink, subIndex) => (
                                                            <li
                                                                key={subLink.text}
                                                                className={`transform transition-all duration-300 ${
                                                                    openDropdown === link.text
                                                                        ? 'translate-x-0 opacity-100'
                                                                        : 'translate-x-4 opacity-0'
                                                                }`}
                                                                style={{
                                                                    transitionDelay: openDropdown === link.text ? `${subIndex * 50}ms` : '0ms'
                                                                }}
                                                            >
                                                                <Link
                                                                    href={subLink.href}
                                                                    onClick={handleLinkClick}
                                                                    className="flex items-center gap-x-3 py-2 px-2 text-gray-300 transition-all duration-200 hover:text-[#FFD43B] hover:bg-gray-800 hover:translate-x-1 rounded"
                                                                >
                                                                    <span className="text-xs text-[#FFD43B]">◦</span>
                                                                    {subLink.text}
                                                                </Link>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            </div>
                                        ) : (
                                            <Link
                                                href={link.href}
                                                onClick={handleLinkClick}
                                                className="block py-3 font-semibold uppercase transition-all duration-200 hover:text-[#FFD43B] hover:bg-gray-800 hover:px-2 rounded"
                                            >
                                                {link.text}
                                            </Link>
                                        )}
                                    </li>
                                ))}
                            </ul>

                            {/* Mobile Apply Online Button */}
                            <div className="border-t border-gray-700 p-4">
                                <div
                                    className={`transform transition-all duration-400 ${
                                        isMenuAnimating
                                            ? 'translate-x-0 opacity-100'
                                            : 'translate-x-8 opacity-0'
                                    }`}
                                    style={{
                                        transitionDelay: isMenuAnimating ? `${navLinks.length * 50 + 100}ms` : '0ms'
                                    }}
                                >
                                    <Link
                                        href="/application-form"
                                        onClick={handleLinkClick}
                                        className="flex items-center gap-x-3 w-full rounded-md bg-[#fdc800] px-4 py-3 text-sm font-semibold text-[#0a4727] transition-all duration-300 hover:bg-[#0a4727] hover:text-[#FFD43B] hover:scale-105 shadow-lg"
                                    >
                                        <FaGraduationCap />
                                        <span>Apply Online</span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
};

export default Navmenu;
