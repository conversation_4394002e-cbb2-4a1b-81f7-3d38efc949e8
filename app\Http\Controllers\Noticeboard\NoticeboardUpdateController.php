<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use App\Models\EditorUpload;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

class NoticeboardUpdateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, Noticeboard $noticeboard)
    {
        //
        $data = $request->validate([
            'title' => 'required',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image',
            'attachment' => 'nullable|file',
            'published_at' => 'nullable|date',
            'draft_token' => 'nullable|string',
        ]);

        $data['slug'] = str($data['title'])->slug();
        $data['image'] = $noticeboard->image;
        $data['attachment'] = $noticeboard->attachment;
        $data['content'] = str_replace(url('/'), '', $data['content'] );


        if ($request->input('remove_image')) {
            if ($noticeboard->image) {
                Storage::disk('public')->delete($noticeboard->image);
            }
            $data['image'] = null;
        } elseif ($request->hasFile('image')) {
            if ($noticeboard->image) {
                Storage::disk('public')->delete($noticeboard->image);
            }
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/notices/images');
        }

        // --- Handle Attachment ---
        if ($request->input('remove_attachment')) {
            if ($noticeboard->attachment) {
                Storage::disk('public')->delete($noticeboard->attachment);
            }
            $data['attachment'] = null;
        } elseif ($request->hasFile('attachment')) {
            if ($noticeboard->attachment) {
                Storage::disk('public')->delete($noticeboard->attachment);
            }
            $data['attachment'] = storeSanitizedFile($request->file('attachment'), 'uploads/notices/attachments');
        }

        $noticeboard->update($data);

        // Handle editor uploads if draft_token is provided
        if ($request->has('draft_token') && $request->input('draft_token')) {
            // Extract image paths actually referenced in content
            $pathsInContent = $this->extractStoragePathsFromHtml($noticeboard->content);
            $pathsInContent = array_values(array_unique($pathsInContent));

            $attachedIds = [];

            // (A) Attach draft uploads
            if (!empty($pathsInContent)) {
                $drafts = EditorUpload::where('draft_token', $request->input('draft_token'))
                    ->whereIn('path', $pathsInContent)
                    ->get();

                foreach ($drafts as $upload) {
                    $noticeboard->editorUploads()->syncWithoutDetaching([$upload->id]);
                    $upload->update(['draft_token' => null]);
                    $attachedIds[] = $upload->id;
                }
            }

            // (B) Keep existing uploads that are still referenced in content
            $existingUploads = $noticeboard->editorUploads()
                ->whereIn('path', $pathsInContent)
                ->get();

            foreach ($existingUploads as $upload) {
                $attachedIds[] = $upload->id;
            }

            // (C) Remove stale uploads that are no longer in content
            $currentIds = $noticeboard->editorUploads()->pluck('editor_uploads.id')->all();
            $staleIds = array_diff($currentIds, $attachedIds);

            if (!empty($staleIds)) {
                $staleUploads = EditorUpload::whereIn('id', $staleIds)->get();
                foreach ($staleUploads as $upload) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
                $noticeboard->editorUploads()->detach($staleIds);
            }
        }

        return redirect(route('admin.noticeboard.index'))->with('success', 'Notice updated successfully');
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn ($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn ($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn ($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo.jpg" → "foo.jpg"
            ->values()
            ->all();
    }
}
