import React, { useState } from 'react'
import Head from "./Head"
import { Link } from "@inertiajs/react" // Changed import to Inertia.js
import "./header.css"

const Header = () => {
  const [click, setClick] = useState(false)

  return (
    <>
      <Head />
      <header>
        <nav className='flexSB'>
          <ul className={click ? "mobile-nav" : "flexSB "} onClick={() => setClick(false)}>
            {/* Converted all 'to' props to 'href' for Inertia */}
            <li>
              <Link href='/'>Home</Link>
            </li>
            <li>
              <Link href='/courses'>All Courses</Link>
            </li>
            <li>
              <Link href='/about'>About</Link>
            </li>
            <li>
              <Link href='/governing-body'>Governing Body</Link>
            </li>
            <li>
              <Link href='/academic'>Academic</Link>
            </li>
            <li>
              <Link href='/journal'>Admission</Link>
            </li>
            <li>
              <Link href='/news'>News</Link>
            </li>
            <li>
              <Link href='/contact'>Contact</Link>
            </li>
          </ul>
          <div className='start'>
            <div className='button'>NIST</div>
          </div>
          <button className='toggle' onClick={() => setClick(!click)}>
            {click ? <i className='fa fa-times'> </i> : <i className='fa fa-bars'></i>}
          </button>
        </nav>
      </header>
    </>
  )
}

export default Header