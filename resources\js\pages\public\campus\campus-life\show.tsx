import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Calendar, Tag } from 'lucide-react';
import Back from '../../common/back/Back';

// Define the TypeScript interface for a gallery item
interface GalleryItem {
  id: number;
  title: string;
  description: string | null;
  category: string;
  image: string;
  created_at: string;
  updated_at: string;
}

interface GalleryShowPageProps {
  gallery: GalleryItem;
}

const categoryLabels: Record<string, string> = {
  all_activity: 'All Activity',
  events: 'Events',
  cse: 'CSE',
  ece: 'ECE',
  bba: 'BBA',
  bmb: 'BMB',
  excellent_results: 'Excellent Results',
};

const GalleryShowPage = ({ gallery }: GalleryShowPageProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <>
      <Head title={gallery.title} />
      <Back title='Campus Life' />
      
      <section className="py-24">
        <div className="mx-auto w-full px-4 sm:w-[90%] lg:w-[80%] xl:w-[70%]">
          {/* Back Button */}
          <div className="mb-6">
            <Link 
              href="/campus-life" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft size={20} />
              <span>Back to Campus Life</span>
            </Link>
          </div>

          {/* Image Container */}
          <div className="mb-8">
            <div className="relative overflow-hidden rounded-lg shadow-2xl">
              <img 
                src={gallery.image} 
                alt={gallery.title} 
                className="w-full h-auto max-h-[70vh] object-contain bg-gray-100" 
              />
            </div>
          </div>

          {/* Content */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Title */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {gallery.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 mb-6 text-sm text-gray-600">
              {/* Date */}
              <div className="flex items-center gap-2">
                <Calendar size={16} className="text-blue-600" />
                <span>{formatDate(gallery.created_at)}</span>
              </div>

              {/* Category */}
              <div className="flex items-center gap-2">
                <Tag size={16} className="text-green-600" />
                <span className="bg-gray-100 px-3 py-1 rounded-full text-xs font-medium">
                  {categoryLabels[gallery.category] || gallery.category}
                </span>
              </div>
            </div>

            {/* Description */}
            {gallery.description && (
              <div className="prose max-w-none">
                <div className="text-gray-700 leading-relaxed text-lg">
                  {gallery.description.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-4 last:mb-0">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex flex-wrap gap-4">
                <Link 
                  href="/campus-life" 
                  className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <ArrowLeft size={18} />
                  Back to Gallery
                </Link>
                
                <Link 
                  href={`/campus-life?category=${gallery.category}`}
                  className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  <Tag size={18} />
                  View More in {categoryLabels[gallery.category]}
                </Link>
              </div>
            </div>
          </div>

          {/* Additional Image Info */}
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>Image uploaded on {formatDate(gallery.created_at)}</p>
            {gallery.updated_at !== gallery.created_at && (
              <p>Last updated on {formatDate(gallery.updated_at)}</p>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default GalleryShowPage;
