import React, { useState, useEffect } from 'react';
import { Head, router, usePage } from '@inertiajs/react';
import { toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import AdmissionSpotTab from './tabs/admission-spot-tab';
import TuitionFeesTab from './tabs/tuition-fees-tab';
import AdmissionPostTab from './tabs/admission-post-tab';
import AdmissionPromoImageTab from './tabs/admission-promo-image-tab';
import HomepageAdTab from './tabs/homepage-ad-tab';
import AdmissionModalTab from './tabs/admission-modal-tab';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admission Management',
        href: '/admission',
    },
];

interface Flash {
    success?: string;
    danger?: string;
}

interface AdmissionSpotData {
    id: number;
    campaign_name: string;
    session: string;
    program_name: string;
    application_start_date: string;
    application_end_date: string;
    admission_test_date: string | null;
    session_start_date: string | null;
    active_status: 'unpublished' | 'published';
    date_created: string;
}

interface TuitionFeeData {
    id: number;
    image: string;
    program_name: string;
    department: string;
}

interface AdmissionPostData {
    id: number;
    title: string;
    admission_circular_content: string;
    active_status: 'unpublished' | 'published';
    published_date: string;
}

interface AdmissionPromoImageData {
    id: number;
    left_banner_image: string;
    right_banner_image_top: string;
    right_banner_image_top_caption: string | null;
    right_banner_image_top_subtitle: string | null;
    right_banner_image_bottom: string;
    right_banner_image_bottom_caption: string | null;
    right_banner_image_bottom_subtitle: string | null;
    status: 'active' | 'inactive';
}

interface HomepageAdData {
    id: number;
    ad_title: string | null;
    program_info_title: string | null;
    session: string | null;
    offer_title: string | null;
    offer_text: string | null;
    active_status: 'active' | 'inactive';
}

interface AdmissionModalData {
    id: number;
    campaign_name: string | null;
    title: string | null;
    active_status: 'active' | 'inactive';
    published_date: string;
    image: string;
}

interface PaginatedAdmissionSpots {
    data: AdmissionSpotData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedTuitionFees {
    data: TuitionFeeData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedAdmissionPosts {
    data: AdmissionPostData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedAdmissionPromoImages {
    data: AdmissionPromoImageData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedHomepageAds {
    data: HomepageAdData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedAdmissionModals {
    data: AdmissionModalData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface AdmissionIndexProps {
    admissionSpots: PaginatedAdmissionSpots;
    tuitionFees: PaginatedTuitionFees;
    admissionPosts: PaginatedAdmissionPosts;
    admissionPromoImages: PaginatedAdmissionPromoImages;
    homepageAds: PaginatedHomepageAds;
    admissionModals: PaginatedAdmissionModals;
    activeTab: string;
}

export default function AdmissionIndex({
    admissionSpots,
    tuitionFees,
    admissionPosts,
    admissionPromoImages,
    homepageAds,
    admissionModals,
    activeTab,
}: AdmissionIndexProps) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const [currentTab, setCurrentTab] = useState(activeTab);

    const handleTabChange = (value: string) => {
        setCurrentTab(value);
        router.get(route('admin.admission.index'), { tab: value }, { 
            preserveState: true, 
            replace: true 
        });
    };

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Admission Management" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-2 md:p-4">
                <div className="rounded border p-4 md:p-6 shadow-xl">
                    <div className="mb-5">
                        <h1 className="text-xl md:text-2xl font-bold">Admission Management</h1>
                        <p className="text-sm md:text-base text-gray-600">Manage all admission-related content and settings</p>
                    </div>

                    <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
                        <TabsList className="flex w-full flex-col gap-2 h-auto p-2 md:grid md:grid-cols-6 md:gap-0 md:h-10 md:p-1">
                            <TabsTrigger value="admission_spot" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Admission Spot
                            </TabsTrigger>
                            <TabsTrigger value="tuition_fees" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Tuition Fees
                            </TabsTrigger>
                            <TabsTrigger value="admission_post" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Admission Post
                            </TabsTrigger>
                            <TabsTrigger value="admission_promo_image" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Promo Images
                            </TabsTrigger>
                            <TabsTrigger value="homepage_ad" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Homepage Ad
                            </TabsTrigger>
                            <TabsTrigger value="admission_modal" className="w-full justify-center text-sm md:text-xs lg:text-sm">
                                Admission Modal
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="admission_spot" className="mt-4 md:mt-6">
                            <AdmissionSpotTab data={admissionSpots} />
                        </TabsContent>

                        <TabsContent value="tuition_fees" className="mt-4 md:mt-6">
                            <TuitionFeesTab data={tuitionFees} />
                        </TabsContent>

                        <TabsContent value="admission_post" className="mt-4 md:mt-6">
                            <AdmissionPostTab data={admissionPosts} />
                        </TabsContent>

                        <TabsContent value="admission_promo_image" className="mt-4 md:mt-6">
                            <AdmissionPromoImageTab data={admissionPromoImages} />
                        </TabsContent>

                        <TabsContent value="homepage_ad" className="mt-4 md:mt-6">
                            <HomepageAdTab data={homepageAds} />
                        </TabsContent>

                        <TabsContent value="admission_modal" className="mt-4 md:mt-6">
                            <AdmissionModalTab data={admissionModals} />
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
