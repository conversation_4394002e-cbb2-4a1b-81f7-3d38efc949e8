<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admission_spots', function (Blueprint $table) {
            $table->id();
            $table->string('campaign_name');
            $table->string('session');
            $table->string('program_name');
            $table->date('application_start_date');
            $table->date('application_end_date');
            $table->date('admission_test_date')->nullable();
            $table->date('session_start_date')->nullable();
            $table->enum('active_status', ['unpublished', 'published'])->default('unpublished');
            $table->date('date_created');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admission_spots');
    }
};
