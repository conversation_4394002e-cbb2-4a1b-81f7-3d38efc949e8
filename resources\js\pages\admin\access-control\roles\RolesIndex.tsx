import { type PaginatedData, type Role, type Permission } from '@/types';
import { Link, router, useForm, usePage } from '@inertiajs/react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { useConfirmation } from '@/contexts/confirmation-context';

// Shadcn/UI Components
import Pagination from '@/components/pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Icons
import { CustomModalForm } from '@/components/custom-modal-form';
import { CustomTable } from '@/components/custom-table';
import { RolesModalFormConfig } from '@/config/forms/roles-modal-form';
import { RolesTableConfig } from '@/config/tables/roles-table';
import debounce from 'lodash/debounce';
import { Plus, Search } from 'lucide-react';

// --- TYPE DEFINITIONS ---

interface Flash {
    success?: string;
    danger?: string;
}

interface RolesPageProps {
    permissions: PaginatedData<Role>;
    filters: {
        search?: string;
    };
}

//new

interface LinkProps {
    active: boolean;
    label: string;
    url: string;
}

//permission data prop

interface RolesPagination {
    data: Role[];
    links: LinkProps[];
    from: number;
    to: number;
    total: number;
}

interface FilterProps {
    search: string;
    perPage: string;
}

interface IndexProps {
    roles: PaginatedData<Role>;
    filters: {
        search?: string;
    };
}

type PageProps = {
  permissions: {
    data: Permission[];
  };
};

// Breadcrumbs for the AppLayout

export default function RolesIndex({ roles, filters }: IndexProps) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const { showConfirmation } = useConfirmation();
    console.log('role data:', roles);
    const [modalOpen, setModalOpen] = useState(false);
    const [mode, setMode] = useState<'create' | 'view' | 'edit'>('create');
    const [selectedCategory, setSelectedCategory] = useState<any>(null);
    const { permissionRole } = usePage<PageProps>().props;

    console.log("perm",permissionRole);
    const { data, setData, errors, processing, reset, post } = useForm<{
        label: string;
        description: string;
        permissions: string[];
        _method: string;
    }>({
        label: '',
        description: '',
        permissions: [],
        _method: 'POST',
    });

    // Effect for showing toast notifications for success/error messages
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    // --- SEARCH FUNCTIONALITY ---

    // Debounced search handler
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.access-control.permissions.index'), { search: query }, { preserveState: true, replace: true });
        }, 300),
    ).current;

    // Cleanup debounce on unmount
    useEffect(() => {
        return () => {
            handleSearch.cancel();
        };
    }, [handleSearch]);

    //new config

    // Handle Delete
    const handleDelete = async (route: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Role',
            description: 'Are you sure you want to delete this role? This action cannot be undone.',
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route, {
                preserveScroll: true,
                onSuccess: (response: { props: any }) => {
                    const successMessage = response.props.flash?.success || 'Something went wrong, please try again.';
                    toast.success(successMessage);
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message || 'Something went wrong, please try again.';
                    toast.error(errorMessage);
                    closeModal();
                },
            });
        }
    };

    // Handle Submit
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Edit mode
        if (mode === 'edit' && selectedCategory) {
            data._method = 'PUT';

            post(route('admin.roles.update', selectedCategory.id), {
                forceFormData: true,
                onSuccess: (response: { props: any }) => {
                    const successMessage = response.props.flash?.success || 'Something went wrong, please try again.';
                    toast.success(successMessage);
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message || 'Something went wrong, please try again.';
                    toast.error(errorMessage);
                },
            });
        } else {
            post(route('admin.roles.store'), {
                onSuccess: (response: { props: any }) => {
                    const successMessage = response.props.flash?.success || 'Something went wrong, please try again.';
                    toast.success(successMessage);
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message || 'Something went wrong, please try again.';
                    toast.error(errorMessage);
                },
            });
        }
    };

    // Closing modal
    const closeModal = () => {
        setMode('create');
        setSelectedCategory(null);
        reset();
        setModalOpen(false);
    };

    // Handle Modal Toggle
    const handleModalToggle = (open: boolean) => {
        setModalOpen(open);

        if (!open) {
            setMode('create');
            setSelectedCategory(null);
            reset();
        }
    };

    // Open Modal
    const openModal = (mode: 'create' | 'view' | 'edit', category?: any) => {
        setMode(mode);

        if (category) {
            Object.entries(category).forEach(([key, value]) => {
                if (key === 'permissions' && Array.isArray(value)) {
                    setData(
                        'permissions',
                        value.map((permission: any) => permission.name),
                    );
                } else {
                    setData(key as keyof typeof data, (value as string | null) ?? '');
                }
            });

            // Setting image preview
            setSelectedCategory(category);
        } else {
            reset();
        }

        setModalOpen(true);
    };


    return (
        <>
            <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
                <div className="mb-4 flex items-center justify-between gap-4">
                    <div className="relative w-full max-w-sm">
                        <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="search"
                            placeholder="Search by permission name..."
                            className="pl-8"
                            defaultValue={filters.search}
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>
                    {/* Custom Modal Form Component */}
                    <div className="ml-auto mr-2">
                        <CustomModalForm
                            addButton={RolesModalFormConfig.addButton}
                            title={mode === 'view' ? 'View Role' : mode === 'edit' ? 'Update Role' : RolesModalFormConfig.title}
                            description={RolesModalFormConfig.description}
                            fields={RolesModalFormConfig.fields}
                            buttons={RolesModalFormConfig.buttons}
                            data={data}
                            setData={setData}
                            errors={errors}
                            processing={processing}
                            handleSubmit={handleSubmit}
                            open={modalOpen}
                            onOpenChange={handleModalToggle}
                            mode={mode}
                            extraData={permissionRole}
                        />
                    </div>
                </div>

                <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                    

                    {/* Custom Table component */}
                    <CustomTable
                        columns={RolesTableConfig.columns}
                        actions={RolesTableConfig.actions}
                        data={roles.data}
                        from={roles.meta.from}
                        onDelete={handleDelete}
                        onView={(category) => openModal('view', category)}
                        onEdit={(category) => openModal('edit', category)}
                        isModal={true}
                    />
                </div>

                <div className="mt-4">
                    <Pagination meta={roles.meta} />
                </div>
            </div>
        </>
    );
}
