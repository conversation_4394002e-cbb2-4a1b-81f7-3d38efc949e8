import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User, type Permission } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface UserPermissionsProps {
    user: User;
    allPermissions: Permission[];
    userPermissions: number[];
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Access Control',
        href: '/admin/access-control',
    },
    {
        title: 'User Permissions',
        href: '#',
    },
];

export default function UserPermissions({ user, allPermissions, userPermissions }: UserPermissionsProps) {
    const { data, setData, put, processing, errors } = useForm({
        permissions: userPermissions,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.access-control.users.permissions.update', user.id));
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        if (checked) {
            setData('permissions', [...data.permissions, permissionId]);
        } else {
            setData('permissions', data.permissions.filter(id => id !== permissionId));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`User Permissions - ${user.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Card className="mx-auto w-full max-w-2xl">
                    <CardHeader>
                        <CardTitle>Assign Permissions to: {user.name}</CardTitle>
                        <CardDescription>
                            Select the specific permissions you want to assign to this user. These permissions will be in addition to any permissions granted through roles.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid gap-4">
                                <Label className="text-base font-medium">Available Permissions</Label>
                                {allPermissions.length === 0 ? (
                                    <p className="text-sm text-muted-foreground">No permissions available. Create permissions first.</p>
                                ) : (
                                    <div className="grid gap-3 max-h-96 overflow-y-auto">
                                        {allPermissions.map((permission) => (
                                            <div key={permission.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`permission-${permission.id}`}
                                                    checked={data.permissions.includes(permission.id)}
                                                    onCheckedChange={(checked) => handlePermissionChange(permission.id, checked as boolean)}
                                                />
                                                <Label 
                                                    htmlFor={`permission-${permission.id}`}
                                                    className="text-sm font-normal cursor-pointer"
                                                >
                                                    {permission.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {errors.permissions && <p className="text-sm text-red-600">{errors.permissions}</p>}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('admin.access-control.index', { tab: 'users' })}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Saving...' : 'Save Permissions'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
