import InputError from '@/components/input-error';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { News, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Content } from '@tiptap/react';
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { ChangeEvent, FormEventHandler, useState } from 'react';
import '../text-editor/RichTextStyles.css';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'News',
        href: route('admin.news.index'),
    },
    {
        title: 'Edit News',
        href: route('admin.news.edit', { news: 'placeholder' }),
    },
];

type PageProps = {
    draftToken: string;
};

export default function NewsEdit({ currentNews }: { currentNews: News }) {
    const [title, setTitle] = useState<string>(currentNews.title);
    const [content, setContent] = useState<string>(currentNews.content ?? '');
    const [image, setImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [processing, setProcessing] = useState<boolean>(false);
    const { errors, draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);
    const [meta_tag_title, setMetaTagTitle] = useState<string | null>(currentNews.meta_tag_title);
    const [meta_tag_description, setMetaTagDescription] = useState<string | null>(currentNews.meta_tag_description);
    const [excerpt, setExcerpt] = useState<string>(currentNews.excerpt ?? '');
    const [activeStatus, setActiveStatus] = useState<'active' | 'inactive'>(currentNews.active_status);
    const [publishedAt, setPublishedAt] = useState<Date>(new Date(currentNews.published_at));

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        setProcessing(true);
        router.post(
            route('admin.news.update', currentNews.id),
            {
                _method: 'put',
                title,
                content,
                image,
                meta_tag_title,
                meta_tag_description,
                excerpt,
                active_status: activeStatus,
                published_at: publishedAt,
                draft_token: draftToken,
            },
            {
                onFinish: () => setProcessing(false),
            },
        );
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImage(file);
            setImagePreview(URL.createObjectURL(file));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit News" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit News</div>
                        <Button>
                            <Link href={route('admin.news.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form className="space-y-6" onSubmit={submit}>
                                <div className="grid gap-6">
                                    <div className="grid gap-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            type="text"
                                            value={title}
                                            onChange={(e) => setTitle(e.target.value)}
                                            placeholder="Enter news title"
                                        />
                                        <InputError message={errors.title} />
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="image">Image</Label>
                                        <Input id="image" type="file" accept="image/*" onChange={handleFileChange} />

                                        <div className="flex space-x-4">
                                            <div>
                                                <p className="mb-2 text-sm text-gray-600">Current Image:</p>
                                                <img src={currentNews.image} alt={currentNews.title} className="h-20 w-20 rounded object-cover" />
                                            </div>
                                            {imagePreview && (
                                                <div>
                                                    <p className="mb-2 text-sm text-gray-600">New Image:</p>
                                                    <img src={imagePreview} alt="Preview" className="h-20 w-20 rounded object-cover" />
                                                </div>
                                            )}
                                        </div>
                                        <InputError message={errors.image} />
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="publishedAt">Published Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        'w-full justify-start text-left font-normal',
                                                        !publishedAt && 'text-muted-foreground',
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedAt ? format(publishedAt, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedAt}
                                                    onSelect={(date) => {
                                                        if (date) setPublishedAt(date);
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.publishedAt} />
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="active_status">Status *</Label>
                                        <Select value={activeStatus} onValueChange={(value: 'active' | 'inactive') => setActiveStatus(value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                                <SelectItem value="active">Active</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.active_status} />
                                    </div>
                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="content">Content *</Label>
                                        <MinimalTiptapEditor
                                            value={currentNews.content}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setContent(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                        <InputError message={errors.content} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="content">Content Preview</Label>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: value }} />
                                        </div>
                                    </div>
                                    <hr />

                                    <div className="grid gap-2">
                                        <h2>
                                            <b>Edit SEO Content</b>
                                        </h2>
                                    </div>
                                    <hr />
                                    <div className="grid gap-2">
                                        <Label htmlFor="excerpt">Summary (Excerpt)</Label>
                                        <Textarea
                                            id="excerpt"
                                            value={excerpt ?? ''}
                                            onChange={(e) => setExcerpt(e.target.value)}
                                            placeholder="Enter news excerpt"
                                            rows={3}
                                        />
                                        <InputError message={errors.excerpt} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="meta_tag_title">Meta Tag Title</Label>
                                        <Input
                                            id="meta_tag_title"
                                            type="text"
                                            value={meta_tag_title || ''}
                                            onChange={(e) => setMetaTagTitle(e.target.value)}
                                            placeholder="Enter meta tag title"
                                        />
                                        <InputError message={errors.meta_tag_title} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="meta_tag_description">Meta Tag Description</Label>
                                        <Textarea
                                            id="meta_tag_description"
                                            value={meta_tag_description || ''}
                                            onChange={(e) => setMetaTagDescription(e.target.value)}
                                            placeholder="Enter meta tag description"
                                            rows={3}
                                        />
                                        <InputError message={errors.meta_tag_description} />
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline">
                                            <Link href={route('admin.news.index')} prefetch>
                                                Cancel
                                            </Link>
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            {processing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Update News
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
