import InputError from '@/components/input-error';
import Pagination from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import StatusToggle from '@/components/ui/status-toggle';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import DatePicker from '@/components/ui/date-picker-input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { router, useForm } from '@inertiajs/react';
import { format } from 'date-fns';
import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';

interface AdmissionModalData {
    id: number;
    campaign_name: string | null;
    title: string | null;
    active_status: 'active' | 'inactive';
    published_date: string;
    image: string;
}

interface PaginatedData {
    data: AdmissionModalData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface AdmissionModalTabProps {
    data: PaginatedData;
}

export default function AdmissionModalTab({ data }: AdmissionModalTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<AdmissionModalData | null>(null);
    const [viewingItem, setViewingItem] = useState<AdmissionModalData | null>(null);

    const {
        data: formData,
        setData,
        post,
        processing,
        errors,
        reset,
    } = useForm({
        campaign_name: '',
        title: '',
        active_status: 'inactive' as 'active' | 'inactive',
        published_date: '',
        image: null as File | null,
    });

    const handleCreate = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.admission.modals.store'), {
            onSuccess: () => {
                setIsCreateOpen(false);
                reset();
            },
        });
    };

    const handleEdit = (item: AdmissionModalData) => {
        setEditingItem(item);
        setData({
            campaign_name: item.campaign_name || '',
            title: item.title || '',
            active_status: item.active_status,
            published_date: item.published_date,
            image: null,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(
                route('admin.admission.modals.update', editingItem.id),
                {
                    _method: 'put',
                    ...formData,
                },
                {
                    onSuccess: () => {
                        setIsEditOpen(false);
                        setEditingItem(null);
                        reset();
                    },
                },
            );
        }
    };
    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Admission Modal',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.modals.destroy', id));
        }
    };

    const handleView = (item: AdmissionModalData) => {
        setViewingItem(item);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Admission Modals</h2>
                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Admission Modal
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Create Admission Modal</DialogTitle>
                            <DialogDescription className="sr-only">Form to create a new admission modal</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="campaign_name">Campaign Name</Label>
                                    <Input
                                        id="campaign_name"
                                        value={formData.campaign_name}
                                        onChange={(e) => setData('campaign_name', e.target.value)}
                                        placeholder="Enter campaign name"
                                    />
                                    <InputError message={errors.campaign_name} />
                                </div>
                                <div>
                                    <Label htmlFor="title">Title</Label>
                                    <Input
                                        id="title"
                                        value={formData.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="Enter modal title"
                                    />
                                    <InputError message={errors.title} />
                                </div>
                                <div>
                                    <Label>Published Date *</Label>
                                    <DatePicker
                                        required
                                        value={formData.published_date}
                                        onChange={(date) => setData('published_date', date ? format(date, 'dd-MMM-yyyy') : '')}
                                    />

                                    <InputError message={errors.published_date} />
                                </div>
                                <div>
                                    <Label htmlFor="image">Image *</Label>
                                    <Input id="image" type="file" accept="image/*" onChange={(e) => setData('image', e.target.files?.[0] || null)} />
                                    <InputError message={errors.image} />
                                </div>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Create
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Image</TableHead>
                                <TableHead>Campaign</TableHead>
                                <TableHead>Title</TableHead>
                                <TableHead>Published Date</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        {item.image ? (
                                            <img src={item.image} alt={item.title || 'Modal Image'} className="h-16 w-16 rounded object-cover" />
                                        ) : (
                                            <div className="flex h-16 w-16 items-center justify-center rounded bg-gray-200">
                                                <span className="text-xs text-gray-500">No Image</span>
                                            </div>
                                        )}
                                    </TableCell>
                                    <TableCell className="font-medium">{item.campaign_name || 'N/A'}</TableCell>
                                    <TableCell>{item.title || 'N/A'}</TableCell>
                                    <TableCell>{item.published_date}</TableCell>
                                    <TableCell>
                                        <StatusToggle
                                            id={item.id}
                                            currentStatus={item.active_status}
                                            updateRoute={route('admin.admission.modals.update', item.id)}
                                            activeLabel="Active"
                                            inactiveLabel="Inactive"
                                            confirmTitle="Change Modal Status"
                                            confirmDescription="Are you sure you want to change the status of this admission modal?"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.title || item.campaign_name || 'Admission Modal')}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit Admission Modal</DialogTitle>
                        <DialogDescription className="sr-only">Form to edit admission modal</DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit_campaign_name">Campaign Name</Label>
                                <Input
                                    id="edit_campaign_name"
                                    value={formData.campaign_name}
                                    onChange={(e) => setData('campaign_name', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_title">Title</Label>
                                <Input id="edit_title" value={formData.title} onChange={(e) => setData('title', e.target.value)} />
                            </div>
                            <div>
                                <Label>Published Date *</Label>
                                <DatePicker
                                    value={formData.published_date} // e.g. "26-Oct-2018"
                                    required
                                    onChange={(date) => setData('published_date', date ? format(date, 'dd-MMM-yyyy') : '')}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_active_status">Status *</Label>
                                <Select
                                    value={formData.active_status}
                                    onValueChange={(value) => setData('active_status', value as 'active' | 'inactive')}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="edit_image">Image</Label>
                            <Input id="edit_image" type="file" accept="image/*" onChange={(e) => setData('image', e.target.files?.[0] || null)} />
                            {editingItem?.image && (
                                <div className="mt-2">
                                    <img src={editingItem.image} alt="Current" className="h-20 w-20 rounded object-cover" />
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>View Admission Modal</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Campaign Name</Label>
                                    <p className="text-sm font-medium">{viewingItem.campaign_name || 'N/A'}</p>
                                </div>
                                <div>
                                    <Label>Title</Label>
                                    <p className="text-sm font-medium">{viewingItem.title || 'N/A'}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Published Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.published_date)}</p>
                                </div>
                                <div>
                                    <Label>Status</Label>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        viewingItem.active_status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {viewingItem.active_status === 'active' ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <Label>Image</Label>
                                {viewingItem.image ? (
                                    <img
                                        src={viewingItem.image}
                                        alt={viewingItem.title || 'Modal Image'}
                                        className="w-1/2 max-w-md h-auto rounded border object-cover mt-2"
                                    />
                                ) : (
                                    <div className="w-32 h-32 bg-gray-200 rounded flex items-center justify-center mt-2">
                                        <span className="text-sm text-gray-500">No Image</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}