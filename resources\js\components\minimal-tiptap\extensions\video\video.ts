import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import { VideoViewBlock } from './components/video-view-block'

export interface VideoOptions {
  HTMLAttributes: Record<string, any>
  allowedProviders: string[]
  width: number
  height: number
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    video: {
      setVideo: (options: { src: string; width?: number; height?: number }) => ReturnType
    }
  }
}

export const Video = Node.create<VideoOptions>({
  name: 'video',

  addOptions() {
    return {
      HTMLAttributes: {},
      allowedProviders: ['youtube', 'vimeo', 'dailymotion'],
      width: 560,
      height: 315,
    }
  },

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: element => element.getAttribute('src'),
        renderHTML: attributes => {
          if (!attributes.src) {
            return {}
          }
          return { src: attributes.src }
        },
      },
      width: {
        default: this.options.width,
        parseHTML: element => element.getAttribute('width'),
        renderHTML: attributes => {
          return { width: attributes.width }
        },
      },
      height: {
        default: this.options.height,
        parseHTML: element => element.getAttribute('height'),
        renderHTML: attributes => {
          return { height: attributes.height }
        },
      },
      title: {
        default: null,
        parseHTML: element => element.getAttribute('title'),
        renderHTML: attributes => {
          if (!attributes.title) {
            return {}
          }
          return { title: attributes.title }
        },
      },
      align: {
        default: 'center',
        parseHTML: element => element.getAttribute('data-align') || 'center',
        renderHTML: attributes => {
          if (!attributes.align) {
            return {}
          }
          return { 'data-align': attributes.align }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'iframe[src]',
        getAttrs: element => {
          const src = (element as HTMLElement).getAttribute('src')
          if (!src) return false
          
          // Check if it's a video URL from allowed providers
          const isVideoUrl = this.options.allowedProviders.some(provider => {
            switch (provider) {
              case 'youtube':
                return src.includes('youtube.com') || src.includes('youtu.be')
              case 'vimeo':
                return src.includes('vimeo.com')
              case 'dailymotion':
                return src.includes('dailymotion.com')
              default:
                return false
            }
          })
          
          return isVideoUrl ? {} : false
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'iframe',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        frameborder: '0',
        allowfullscreen: 'true',
        allow: 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture',
      }),
    ]
  },

  addCommands() {
    return {
      setVideo:
        options =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },

  addNodeView() {
    return ReactNodeViewRenderer(VideoViewBlock)
  },
})

// Helper function to convert various video URLs to embed URLs
export const getEmbedUrl = (url: string): string | null => {
  // YouTube
  const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
  const youtubeMatch = url.match(youtubeRegex)
  if (youtubeMatch) {
    return `https://www.youtube.com/embed/${youtubeMatch[1]}`
  }

  // Vimeo
  const vimeoRegex = /(?:vimeo\.com\/)([0-9]+)/
  const vimeoMatch = url.match(vimeoRegex)
  if (vimeoMatch) {
    return `https://player.vimeo.com/video/${vimeoMatch[1]}`
  }

  // Dailymotion
  const dailymotionRegex = /(?:dailymotion\.com\/video\/)([a-zA-Z0-9]+)/
  const dailymotionMatch = url.match(dailymotionRegex)
  if (dailymotionMatch) {
    return `https://www.dailymotion.com/embed/video/${dailymotionMatch[1]}`
  }

  // If already an embed URL, return as is
  if (url.includes('/embed/') || url.includes('player.vimeo.com')) {
    return url
  }

  return null
}
