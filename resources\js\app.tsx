import '../css/app.css';

import { TooltipProvider } from '@/components/ui/tooltip';
import { ConfirmationProvider } from '@/contexts/confirmation-context';
import { createInertiaApp } from '@inertiajs/react';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';
import FrontendLayout from './layouts/frontend-layout';

const appName = import.meta.env.VITE_APP_NAME || 'NIST | National Institute of Science & Technology';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: async (name) => {
        const pages = import.meta.glob('./pages/**/*.tsx', { eager: true });
        const page = await pages[`./pages/${name}.tsx`];
        page.default.layout = name.startsWith('public/')
            ? (page) => <FrontendLayout>{page}</FrontendLayout>
            : undefined;
        return page;
    },

    setup({ el, App, props }) {
        const root = createRoot(el);

        // detect if it's admin route
        const isAdminRoute = props.initialPage.component.startsWith('admin/');
;

        const appTree = (
            <TooltipProvider>
                {isAdminRoute ? (
                    <ConfirmationProvider>
                        <App {...props} />
                    </ConfirmationProvider>
                ) : (
                    <App {...props} />
                )}
            </TooltipProvider>
        );

        root.render(appTree);
    },
    progress: {
        color: '#fdc800',
    },
});

initializeTheme();
