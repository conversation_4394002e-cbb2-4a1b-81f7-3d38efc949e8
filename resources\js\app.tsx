import '../css/app.css';

import { TooltipProvider } from '@/components/ui/tooltip';
import { ConfirmationProvider } from '@/contexts/confirmation-context';
import { createInertiaApp } from '@inertiajs/react';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';
import AdminLayout from './layouts/admin-layout';
import FrontendLayout from './layouts/frontend-layout';
import DefaultLayout from './layouts/default-layout'; 

const appName =
    import.meta.env.VITE_APP_NAME || 'NIST | National Institute of Science & Technology';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => {
        const pages = import.meta.glob('./pages/**/*.tsx', { eager: true });
        const page: any = pages[`./pages/${name}.tsx`];

        // Layout selection
        if (name.startsWith('admin/')) {
            page.default.layout ??= (page) => <AdminLayout>{page}</AdminLayout>;
        } else if (name.startsWith('public/')) {
            page.default.layout ??= (page) => <FrontendLayout>{page}</FrontendLayout>;
        } else {
            page.default.layout ??= (page) => <DefaultLayout>{page}</DefaultLayout>;
            // If you don’t want a fallback wrapper, just:
            // page.default.layout ??= (page) => <>{page}</>;
        }

        return page;
    },
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <TooltipProvider>
                <ConfirmationProvider>
                    <App {...props} />
                </ConfirmationProvider>
            </TooltipProvider>
        );
    },
    progress: {
        color: '#f59e0b',
    },
});

initializeTheme();
