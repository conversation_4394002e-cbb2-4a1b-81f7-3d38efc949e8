import React, { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { router } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';

interface StatusToggleProps {
    id: number;
    currentStatus: 'active' | 'inactive' | 'published' | 'unpublished';
    updateRoute: string;
    onStatusChange?: (newStatus: string) => void;
    activeLabel?: string;
    inactiveLabel?: string;
    confirmTitle?: string;
    confirmDescription?: string;
    className?: string;
    statusField?: string; // Allow custom field name
}

export default function StatusToggle({
    id,
    currentStatus,
    updateRoute,
    onStatusChange,
    activeLabel = 'Active',
    inactiveLabel = 'Inactive',
    confirmTitle = 'Confirm Status Change',
    confirmDescription = 'Are you sure you want to change the status?',
    className = '',
    statusField, // Custom field name
}: StatusToggleProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [pendingStatus, setPendingStatus] = useState<string>('');
    const [isUpdating, setIsUpdating] = useState(false);

    const isActive = currentStatus === 'active' || currentStatus === 'published';

    const handleToggleClick = () => {
        const newStatus = isActive 
            ? (currentStatus === 'published' ? 'unpublished' : 'inactive')
            : (currentStatus === 'unpublished' ? 'published' : 'active');
        
        setPendingStatus(newStatus);
        setIsDialogOpen(true);
    };

    const handleConfirm = () => {
        setIsUpdating(true);

        // Use custom statusField if provided, otherwise determine based on current status
        const fieldName = statusField || (
            currentStatus === 'published' || currentStatus === 'unpublished'
                ? 'active_status'
                : 'active_status' // Default to active_status for most cases
        );


        router.put(updateRoute, {
            [fieldName]: pendingStatus,
        }, {
            onSuccess: () => {
                setIsDialogOpen(false);
                setIsUpdating(false);
                onStatusChange?.(pendingStatus);
                console.log('Status update successful');
            },
            onError: (errors) => {
                setIsUpdating(false);
                console.error('Status update failed:', errors);
            },
        });
    };

    const handleCancel = () => {
        setIsDialogOpen(false);
        setPendingStatus('');
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'active':
            case 'published':
                return activeLabel;
            case 'inactive':
            case 'unpublished':
                return inactiveLabel;
            default:
                return status;
        }
    };

    return (
        <>
            <div className={`flex items-center space-x-2 ${className}`}>
                <Switch
                    checked={isActive}
                    onCheckedChange={handleToggleClick}
                    disabled={isUpdating}
                />
                <span className={`text-sm font-medium ${
                    isActive ? 'text-green-600' : 'text-gray-500'
                }`}>
                    {getStatusLabel(currentStatus)}
                </span>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{confirmTitle}</DialogTitle>
                        <DialogDescription>
                            {confirmDescription}
                            <br />
                            <span className="mt-2 block">
                                Status will change from{' '}
                                <span className="font-semibold">{getStatusLabel(currentStatus)}</span>
                                {' '}to{' '}
                                <span className="font-semibold">{getStatusLabel(pendingStatus)}</span>
                            </span>
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={handleCancel}
                            disabled={isUpdating}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConfirm}
                            disabled={isUpdating}
                        >
                            {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            Confirm
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}
