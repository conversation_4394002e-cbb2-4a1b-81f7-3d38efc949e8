// RichTextEditor.js
import React, { useEffect, useRef, useState } from 'react';

// Import Quill and its styles
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import './EditorStyles.css';



// +++ ADDED the new blot formatter module
import QuillBlotF<PERSON>atter from 'quill-blot-formatter';

// +++ ADDED the new registration




// +++ ADDED the new registration. This is done inside useEffect for client-side safety.

const RichTextEditor = ({ value, onChange }) => {
  const [content, setContent] = useState('');
  const quillRef = useRef(null);
  const editorRef = useRef(null);

 

  const toolbarOptions = [
    [{ 'header': '1' }, { 'header': '2' },{ 'header': '3' }, { 'font': [] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [{ 'color': [] }, { 'background': [] }],
    [
      { 'list': 'ordered' },
      { 'list': 'bullet' },
      { 'indent': '-1' },
      { 'indent': '+1' }
    ],
    [{ 'align': [] }],
    ['link', 'image', 'video',],
    ['clean']
  ];

  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike', 'blockquote',
    'list', 'bullet', 'indent',
    'link', 'image', 'video', 'align', 'color', 'background'
  ];

  useEffect(() => {
    if (editorRef.current && !quillRef.current) {
      // +++ Register the blot formatter module with Quill
      Quill.register('modules/blotFormatter', QuillBlotFormatter);

      quillRef.current = new Quill(editorRef.current, {
        theme: 'snow',
        modules: {
          toolbar: toolbarOptions,
          blotFormatter: {
            // Options for the formatter
          },
          clipboard: {
            matchVisual: false,
          },
        },
        formats: formats
      });
      if (value) {
        quillRef.current.clipboard.dangerouslyPasteHTML(value);
      }

   
      quillRef.current.on('text-change', (delta, oldDelta, source) => {
        if (source === 'user') {
          const newContent = quillRef.current.root.innerHTML;
          // 3. Call the onChange prop to lift the state up
          onChange(newContent);
        }
      });

      setContent(quillRef.current.root.innerHTML);
    }

    return () => {
      // Clean up the Quill instance
      if (quillRef.current) {
          quillRef.current = null;
      }
    };
  }, []);

  return (
    <div className="editor-container">
      {/* --- MODIFIED LINE --- */}
      {/* Removed the inline style prop */}
      <div ref={editorRef} className="quill-editor" />
    </div>
  );
};

export default RichTextEditor;