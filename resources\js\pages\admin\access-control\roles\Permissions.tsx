import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Role, type Permission } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface RolePermissionsProps {
    role: Role;
    allPermissions: Permission[];
    rolePermissions: number[];
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Access Control',
        href: '/admin/access-control',
    },
    {
        title: 'Role Permissions',
        href: '#',
    },
];

export default function RolePermissions({ role, allPermissions, rolePermissions }: RolePermissionsProps) {
    const { data, setData, put, processing, errors } = useForm({
        permissions: rolePermissions,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.access-control.roles.permissions.update', role.id));
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        if (checked) {
            setData('permissions', [...data.permissions, permissionId]);
        } else {
            setData('permissions', data.permissions.filter(id => id !== permissionId));
        }
    };

    // Group permissions by category (assuming permission names follow a pattern like "category-action")
    const groupedPermissions = allPermissions.reduce((groups, permission) => {
        const category = permission.name.split('-')[0] || 'general';
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(permission);
        return groups;
    }, {} as Record<string, Permission[]>);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Role Permissions - ${role.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Card className="mx-auto w-full max-w-4xl">
                    <CardHeader>
                        <CardTitle>Role Permissions for: {role.name}</CardTitle>
                        <CardDescription>
                            Select the permissions you want to assign to this role. Users with this role will inherit all selected permissions.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {allPermissions.length === 0 ? (
                                <p className="text-sm text-muted-foreground">No permissions available. Create permissions first.</p>
                            ) : (
                                <div className="grid gap-6">
                                    {Object.entries(groupedPermissions).map(([category, permissions]) => (
                                        <div key={category} className="space-y-3">
                                            <h3 className="text-lg font-medium capitalize">{category} Permissions</h3>
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                {permissions.map((permission) => (
                                                    <div key={permission.id} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={`permission-${permission.id}`}
                                                            checked={data.permissions.includes(permission.id)}
                                                            onCheckedChange={(checked) => handlePermissionChange(permission.id, checked as boolean)}
                                                        />
                                                        <Label 
                                                            htmlFor={`permission-${permission.id}`}
                                                            className="text-sm font-normal cursor-pointer"
                                                        >
                                                            {permission.name}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                            {errors.permissions && <p className="text-sm text-red-600">{errors.permissions}</p>}

                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('admin.access-control.index', { tab: 'roles' })}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Saving...' : 'Save Permissions'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
