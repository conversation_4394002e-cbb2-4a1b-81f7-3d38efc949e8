<?php

namespace App\Http\Controllers\Schedule;

use App\Http\Controllers\Controller;
use App\Models\ClassSchedule;
use App\Models\FacultySchedule;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Resources\ClassScheduleResource;
use App\Http\Resources\FacultyScheduleResource;

class ScheduleController extends Controller
{
    //
    public function index(Request $request)
    {
        // Handle Class Schedule Data & Search
        $classSchedules = ClassSchedule::query()
            ->when($request->input('search_class'), function ($query, $search) {
                $query->where('department', 'like', "%{$search}%")
                      ->orWhere('session', 'like', "%{$search}%")
                      ->orWhere('batch', 'like', "%{$search}%");
            })
            ->paginate(10, ['*'], 'class_page') // Use a unique page name
            ->withQueryString();

        // Handle Faculty Schedule Data & Search
        $facultySchedules = FacultySchedule::query()
            ->when($request->input('search_faculty'), function ($query, $search) {
                $query->where('faculty_name', 'like', "%{$search}%")
                      ->orWhere('department', 'like', "%{$search}%");
            })
            ->paginate(10, ['*'], 'faculty_page') // Use a unique page name
            ->withQueryString();

        return Inertia::render('admin/schedule/ScheduleIndex', [
            'classSchedules' => ClassScheduleResource::collection($classSchedules),
            'facultySchedules' => FacultyScheduleResource::collection($facultySchedules),
            'filters' => $request->only(['search_class', 'search_faculty']),// Pass filters back to the view
            'activeTab' => $request->input('tab', 'class_schedule'),
        ]);
    }

    //class schedule manage
    public function storeClassSchedule(Request $request)
    {
        $data = $request->validate([
            'department' => 'required',
            'session' => 'required',
            'batch' => 'required',
            'scheduleEmbedLink' => 'nullable',
        ]);

        ClassSchedule::create($data);

        // Redirect back to the same tab
        return to_route('admin.schedule.index', ['tab' => 'class_schedule'])->with('success', 'Class schedule created successfully.');
    
    }

    public function editClassSchedule(ClassSchedule $classSchedule)
    {
        return inertia('admin/schedule/class-schedule/edit', ['currentClassSchedule' => new ClassScheduleResource($classSchedule),]);
    }

    public function updateClassSchedule(Request $request, ClassSchedule $classSchedule)
    {
        $data = $request->validate([
            'department' => 'required',
            'session' => 'required',
            'batch' => 'required',
            'scheduleEmbedLink' => 'nullable',
        ]);

        $classSchedule->update($data);

        return to_route('admin.schedule.index', ['tab' => 'class_schedule'])
            ->with('success', 'Class schedule updated successfully.');
    }

    public function destroyClassSchedule(ClassSchedule $classSchedule)
    {
        $classSchedule->delete();

        
        return to_route('admin.schedule.index', ['tab' => 'class_schedule'])
            ->with('danger', 'Class schedule has been deleted.');
    }
  
     
    //faculty schedule manage
    public function storeFacultySchedule(Request $request)
    {
        $data = $request->validate([
            'faculty_name' => 'required',
            'department' => 'required',
            'scheduleEmbedLink' => 'nullable',
        ]);

        FacultySchedule::create($data);

       return to_route('admin.schedule.index', ['tab' => 'faculty_schedule'])
            ->with('success', 'Faculty schedule created successfully.'); }

    public function editFacultySchedule(FacultySchedule $facultySchedule)
    {
        return inertia('admin/schedule/faculty-schedule/edit', ['currentFacultySchedule' => new FacultyScheduleResource($facultySchedule),]);
    }

    public function updateFacultySchedule(Request $request, FacultySchedule $facultySchedule)
    {
        $data = $request->validate([
            'faculty_name' => 'required',
            'department' => 'required',
            'scheduleEmbedLink' => 'nullable',
        ]);

        $facultySchedule->update($data);

       return to_route('admin.schedule.index', ['tab' => 'faculty_schedule'])
            ->with('success', 'Faculty schedule updated successfully.');
   
    }

    public function destroyFacultySchedule(FacultySchedule $facultySchedule)
    {
        $facultySchedule->delete();

         return to_route('admin.schedule.index', ['tab' => 'faculty_schedule'])
            ->with('danger', 'Faculty schedule has been deleted.');
    
    }   

}
