---
description: Repository Information Overview
alwaysApply: true
---

# NIST WCMS Information

## Summary
A Laravel-based web content management system (WCMS) built with Laravel 12 and React 19. The application uses Inertia.js to bridge Laravel and React, with a modern UI built using Tailwind CSS and Radix UI components.

## Structure
- **app**: Core application code including controllers, models, and middleware
- **bootstrap**: Application bootstrapping files
- **config**: Configuration files for Laravel and services
- **database**: Database migrations, seeders, and factories
- **docs**: Documentation files
- **public**: Publicly accessible files (compiled assets, index.php)
- **resources**: Frontend assets (React components, CSS, views)
- **routes**: Application route definitions
- **storage**: Application storage (logs, cache, uploads)
- **tests**: Test files for PHPUnit/Pest

## Language & Runtime
**Language**: PHP 8.2, JavaScript/TypeScript
**Framework**: Laravel 12.18.0, React 19.0.0
**Build System**: Vite 6.0
**Package Managers**: Composer, npm

## Dependencies

### Backend (PHP)
**Main Dependencies**:
- laravel/framework: ^12.0
- inertiajs/inertia-laravel: ^2.0
- spatie/laravel-permission: ^6.21
- tightenco/ziggy: ^2.4
- andreiio/blade-remix-icon: ^3.6
- doctrine/dbal: ^4.3

**Development Dependencies**:
- pestphp/pest: ^3.8
- pestphp/pest-plugin-laravel: ^3.2
- laravel/pint: ^1.18
- laravel/sail: ^1.41
- laravel/pail: ^1.2.2

### Frontend (JavaScript/TypeScript)
**Main Dependencies**:
- react: ^19.0.0
- react-dom: ^19.0.0
- @inertiajs/react: ^2.0.12
- @radix-ui/react-* (UI component library)
- @tiptap/react: ^3.0.3 (Rich text editor)
- tailwindcss: ^4.1.11

**Development Dependencies**:
- typescript: ^5.7.2
- eslint: ^9.17.0
- prettier: ^3.4.2
- vite: ^6.0

## Build & Installation

### Backend Setup
```bash
# Install PHP dependencies
composer install

# Set up environment
cp .env.example .env
php artisan key:generate
php artisan migrate

# Start development server
php artisan serve
```

### Frontend Setup
```bash
# Install JavaScript dependencies
npm install

# Start Vite development server
npm run dev

# Build for production
npm run build
```

### Combined Development
```bash
# Run both backend and frontend servers
composer dev
```

## Testing
**Framework**: Pest (PHPUnit wrapper)
**Test Location**: tests/ directory (Unit and Feature tests)
**Configuration**: phpunit.xml
**Run Command**:
```bash
# Run tests
composer test
# or
php artisan test
# or
./vendor/bin/pest
```

## Authentication
The application uses Laravel's built-in authentication system with custom routes for user registration, login, password reset, and email verification. Authentication routes are defined in routes/auth.php.