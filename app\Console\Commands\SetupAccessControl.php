<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetupAccessControl extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'access-control:setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup basic roles and permissions for access control system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up access control system...');

        // Create permissions
        $permissions = [
            'users-view',
            'users-create',
            'users-edit',
            'users-delete',
            'roles-view',
            'roles-create',
            'roles-edit',
            'roles-delete',
            'permissions-view',
            'permissions-create',
            'permissions-edit',
            'permissions-delete',
            'dashboard-access',
        ];

        foreach ($permissions as $permission) {
            DB::table('permissions')->insertOrIgnore([
                'name' => $permission,
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Create roles
        $roles = [
            ['name' => 'super_admin', 'guard_name' => 'web'],
            ['name' => 'admin', 'guard_name' => 'web'],
            ['name' => 'editor', 'guard_name' => 'web'],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insertOrIgnore([
                'name' => $role['name'],
                'guard_name' => $role['guard_name'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $this->info('Access control system setup completed!');
        $this->info('Created roles: super_admin, admin, editor');
        $this->info('Created basic permissions for users, roles, permissions, and dashboard access');
        
        return 0;
    }
}
