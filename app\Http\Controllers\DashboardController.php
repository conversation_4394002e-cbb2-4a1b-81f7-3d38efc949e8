<?php

namespace App\Http\Controllers;

use App\Models\ImageSlider;
use App\Models\News;
use App\Models\Noticeboard;
use App\Models\FacultyInfo;
use App\Models\GoverningBody;
use App\Models\User;
use App\Models\AdmissionPost;
use App\Models\ClassSchedule;
use App\Models\Gallery;

use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function __invoke(Request $request)
    {
        // Get statistics with error handling
        $stats = [
            'total_notices' => Noticeboard::count(),
            'total_news' => News::count(),
            'total_faculty' => FacultyInfo::count(),
            'total_governing_body' => GoverningBody::count(),
            'total_users' => User::count(),
            'total_gallery' => Gallery::count(), // Placeholder - update when gallery model exists
            'total_image_sliders' => ImageSlider::count(), // Placeholder - update when image slider model exists
            'total_class_schedule' => ClassSchedule::count(),
            'total_admission_posts' => AdmissionPost::count(),
        ];



        // Get recent items (3 each) - simplified approach
        $recentNotices = Noticeboard::orderBy('created_at', 'desc')
            ->select('id', 'title', 'created_at')
            ->limit(3)
            ->get()
            ->toArray();

        $recentNews = News::orderBy('created_at', 'desc')
            ->select('id', 'title', 'created_at')
            ->limit(3)
            ->get()
            ->toArray();

        $recentAdmissionPosts = AdmissionPost::orderBy('created_at', 'desc')
            ->select('id', 'title', 'created_at')
            ->limit(3)
            ->get()
            ->toArray();

        

        

        $data = [
            'stats' => $stats,
            'recentNotices' => $recentNotices,
            'recentNews' => $recentNews,
            'recentAdmissionPosts' => $recentAdmissionPosts,
        ];

        

        return Inertia::render('admin/dashboard', $data);
    }
}
