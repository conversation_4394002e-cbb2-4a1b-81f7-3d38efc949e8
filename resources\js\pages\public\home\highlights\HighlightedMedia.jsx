import React from 'react';

const HighlightedMedia = () => {
    const sections = [
        {
            icon: "fa-solid fa-graduation-cap",
            title: 'Admission',
            description: 'National Institute of Science and Technology (NIST) is offering 4 undergraduate Bachelor programs such as CSE, BBA, ECE, and Biochemistry. Our curriculum is designed to help the students to develop the skills required for 21st-century employment'
        },
        {
            icon: "fa-solid fa-book-open-reader",
            title: 'Skilled Faculty',
            description: 'NIST focuses on to engage the brilliant minds of the country in teaching. For this, NIST has some of the brilliant teachers. Our curriculum is designed to help the students to develop the skills required for 21st-century employment.'
        },
        {
            icon: "fa-solid fa-laptop",
            title: 'LAPTOP for All',
            description: 'We make sure all our students get the help they need to complete the degree. In order to encourage students and help the learning experiences of students, NIST is offering free Laptops for all the students.'
        },
        {
            icon: "fa-solid fa-circle-dollar-to-slot",
            title: 'Tuition Fees',
            description: 'We offer affordable tuition fees for students with the option to pay them in multiple installments to create convenience for students. Scholarships are available for students with excellent academic results.'
        },
    ];

    return (
        <div className="p-5 bg-[#0a4727]">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 max-w-7xl mx-auto">
                {sections.map((section, index) => (
                    <div 
                        key={index} 
                        className="rounded-lg bg-[#096635] p-6 text-center text-white transition-transform duration-300 ease-in-out hover:-translate-y-2.5"
                    >
                        <div className="mb-4 text-[50px]">
                            <i className={section.icon}></i>
                        </div>
                        <h3 className="mb-3 text-xl font-semibold sm:text-2xl">
                            {section.title}
                        </h3>
                        <p className="text-justify text-sm sm:text-base">
                            {section.description}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
}

export default HighlightedMedia;
