import React from 'react';
// Assuming 'Back' component is in a relative path
 
import Back from '../../common/back/Back';


// 1. Define a TypeScript interface for the shape of a single section object.
interface Section {
  id: number;
  title: string;
  subheading: string;
  text: string;
  image: string;
}

// Data for all the sections
const sections: Section[] = [
    {
      id: 1,
      title: 'National Institute of Science and Technology (NIST)',
      subheading: 'About',
      text: 'The National Institute of Science and Technology (NIST) runs professional BBA, CSE, and ECE programs, along with a BSc Honours in Biochemistry and Molecular Biology, under the National University of Bangladesh. In 2019, NIST became affiliated with the National University for conducting the BSc (Professional) programs in Computer Science and Engineering (CSE), Electronics and Communication Engineering (ECE), and Business Administration (BBA). NIST is an innovative, non-profit private institute, pioneering excellence in technology-based education, offering a unique blend of academic and professional skills in a modern, supportive environment. With students from diverse social backgrounds from across the country, NIST stands out as a leading institution under the National University in its programs.',
      image: '/images/about/library.jpg'
    },
    {
      id: 2,
      title: 'Welcome to NIST',
      subheading: 'Message',
      text: 'NIST welcomes you in your adventure with the highest possible support during your academic life and career as well! Here in NIST, you will discover the experts in your discipline continuously guiding you to your aspired success. NIST insists that its learners engage in comprehensive practical works and thus be proficient in whatever they do. You can find relevant admission information and course details along with the contact information to reach us directly. NIST is conducted by Daffodil Education Network, largest educational organization of Bangladesh which secures students continuous academic and career growth.',
      image: '/images/about/victory-day.jpg'
    },
    // ... add all other section objects here
     {
        id: 3,
        title: 'Daffodil Education Network',
        subheading: 'Core Values',
        text: 'Daffodil Education Network is the country’s largest & leading education provider for global Education solutions with a proud past and an exciting future.',
        image: '/images/about/den.jpg'
    },
    {
        id: 4,
        title: 'Mission & Vision',
        subheading: 'Our Goal',
        text: 'The National Institute of Science and Technology is committed to implementing the most effective teaching-learning approaches. One of our primary missions is to guarantee that the natural environment is protected, preserved, and maintained. Our main goal is to reawaken the thrill of discovery, learn from it, and gain international reputation by focusing on our capabilities. We also have the ambition to engage with relevant scientific research institutions, business participants, and the general public. We are dedicated to establishing our institution as a public policy role model.',
        image: '/images/about/biochem-lab.jpg' // Replace with actual image paths
    },
    {   
        id: 5,
        title: 'Experienced Teacher',
        subheading: '',
        text: 'NIST focuses on to engage the brilliant minds of the country in teaching. For this, NIST has some of the brilliant teachers.',
        image: '/images/about/nist_2024.jpg' // Replace with actual image paths
    },
    {
        id: 6,
        title: 'Class Facilities',
        subheading: 'Modern Classroom, Well Equiped LAB',
        text: 'As the scope of learning and teaching has extended vastly, blended learning has become important. To ensure proper blended learning, NIST has digital classroom. With well equipped and safety measures taken, NIST has the most up to date lab facilities.',
        image: '/images/about/cse-lab1.jpg'
    },
    {
        id: 7,
        title: 'Free LAPTOP for all',
        subheading: 'Laptop, E-learning and Industry Linkage',
        text: 'In order to encourage students and help the learning experiences of students, NIST is offering free Laptops for all the students. Students from far away can take part in the live classes with the help of our very own LMS or Learning Management System. In order to build up strong industry-academia linkage, our students are exposed to various real life problems to solve with the help of our expert teachers in the related fields. ',
        image: '/images/about/classroom.jpg'
    },
    {
        id: 8,
        title: 'Free Wi-Fi Zone and Others Facilities',
        subheading: 'NIST on Campus Add-on',
        text: 'Strong wifi zone throughout the whole campus has been ensured. The whole campus as well as the areas surrounding it are under close surveillance. Students from far away can stay in students’ hostels equipped with all modern facilities. Students inside Dhaka can enjoy clean, hygienic transport facilities. ',
        image: '/images/about/library2.jpg'
    },
    {
        id: 9,
        title: 'Financial Offerings, Job Placement',
        subheading: 'Eduloan, Entrpreunership and Job Opportunity',
        text: 'Edu Loan is offering the students to continue their studies no matter what happens to them financially. In order to help students’ who wants be an entrepreneur, NIST promises to fund them with the Co-op program. NIST can ensure a 100% job placement service to their students because of their industry academia collaboration. ',
        image: '/images/about/counsel.jpg'
    },
];


// 2. Define the component as a React Functional Component (React.FC).
const AboutUs: React.FC = () => {
  return (
    <>
   
      <Back title='About Us' />
      
      {/* Container with width, horizontal centering, and vertical padding */}
      <div className="w-[90%] mx-auto py-12">
        {sections.map((section, index) => (
          // 3. Main section container using Flexbox for layout.
          // It's a column on mobile (default) and reverses direction based on index for desktop (md:).
          <div 
            key={section.id} 
            className={`
              flex flex-col md:items-center gap-5 mb-16 md:mb-10
              ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}
            `}
          >
            {/* Image container */}
            <div className="flex-1">
              <img src={section.image} alt={section.title} className="w-full h-auto rounded-lg shadow-md" />
            </div>
            
            {/* Text container. Centered on mobile, left-aligned on desktop. */}
            <div className="flex-1 text-center md:text-left">
              {section.subheading && <h3 className="text-xl text-gray-600 mb-2">{section.subheading}</h3>}
              <h2 className="text-2xl font-bold mb-4">{section.title}</h2>
              <p className="text-justify text-base leading-relaxed">{section.text}</p>
            </div>
          </div>
        ))}
      </div>
      
    </>
  );
};

export default AboutUs;