<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user with super_admin role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating test user...');

        // Create test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Admin',
                'password' => Hash::make('password'),
            ]
        );

        // Get super_admin role
        $superAdminRole = DB::table('roles')->where('name', 'super_admin')->first();
        
        if ($superAdminRole) {
            // Assign role to user
            DB::table('model_has_roles')->insertOrIgnore([
                'role_id' => $superAdminRole->id,
                'model_type' => 'App\Models\User',
                'model_id' => $user->id,
            ]);
            
            $this->info('Test user created successfully!');
            $this->info('Email: <EMAIL>');
            $this->info('Password: password');
            $this->info('Role: super_admin');
        } else {
            $this->error('Super admin role not found. Run access-control:setup first.');
        }

        return 0;
    }
}
