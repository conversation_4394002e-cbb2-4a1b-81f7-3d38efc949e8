<?php
// database/migrations/2025_08_23_000300_alter_editor_uploads_to_morph.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('editor_uploads', function (Blueprint $table) {
            // Add polymorphic columns (nullable during transition)
            $table->nullableMorphs('uploadable'); // uploadable_type, uploadable_id

            // Optional quality-of-life columns (nullable for backfill)
         
            if (!Schema::hasColumn('editor_uploads', 'mime')) {
                $table->string('mime', 191)->nullable();
            }
            if (!Schema::hasColumn('editor_uploads', 'size')) {
                $table->unsignedBigInteger('size')->nullable();
            }
            if (!Schema::hasColumn('editor_uploads', 'original_name')) {
                $table->string('original_name', 255)->nullable();
            }

            // Safer path length
            $table->string('path', 1024)->change();
        });

        // Backfill polymorphic from existing news_id
        if (Schema::hasColumn('editor_uploads', 'news_id')) {
            DB::table('editor_uploads')
                ->whereNotNull('news_id')
                ->update([
                    'uploadable_type' => \App\Models\News::class,
                    // set uploadable_id = news_id
                ]);

            // We can’t set uploadable_id via array (no DB::raw in update array),
            // so do a single raw statement for that part:
            DB::statement('UPDATE editor_uploads SET uploadable_id = news_id WHERE news_id IS NOT NULL');
        }

        // Now drop FK + column news_id
        Schema::table('editor_uploads', function (Blueprint $table) {
            if (Schema::hasColumn('editor_uploads', 'news_id')) {
                try {
                    $table->dropForeign(['news_id']);
                } catch (\Throwable $e) {
                    // ignore if not present
                }
                $table->dropColumn('news_id');
            }
        });
    }

    public function down(): void
    {
        Schema::table('editor_uploads', function (Blueprint $table) {
            $table->foreignId('news_id')->nullable()->constrained()->cascadeOnDelete();

            // Move morph back into news_id if possible
            // (best-effort; rows for other models won’t map)
        });

        // Best-effort backfill
        DB::table('editor_uploads')
            ->where('uploadable_type', \App\Models\News::class)
            ->whereNotNull('uploadable_id')
            ->update([
                'news_id' => DB::raw('uploadable_id'),
            ]);

        Schema::table('editor_uploads', function (Blueprint $table) {
            $table->dropMorphs('uploadable');
            // Optional: drop extra columns if you want a strict rollback
            // $table->dropConstrainedForeignId('user_id');
            // $table->dropColumn(['mime','size','original_name']);
            $table->string('path', 255)->change();
        });
    }
};
