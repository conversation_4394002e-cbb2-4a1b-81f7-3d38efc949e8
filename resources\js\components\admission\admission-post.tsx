import React from 'react';
import '../../components/text-editor/RichTextStyles.css'
import { Link } from '@inertiajs/react';

interface AdmissionPostData {
  id: number;
  title: string;
  admission_circular_content: string;
  active_status: string;
  published_date: string;
}

interface AdmissionPostProps {
  admissionPost?: AdmissionPostData | null;
}

const AdmissionPost = ({ admissionPost }: AdmissionPostProps) => {
  if (!admissionPost) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            Admission Notice
          </h2>
          <div className="w-24 h-1 bg-green-700 mx-auto mb-6"></div>
        </div>

        {/* Post Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
            {/* Post Header */}
            <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
              <h3 className="text-2xl font-bold mb-2">
                {admissionPost.title}
              </h3>
              <p className="text-blue-100">
                Published on {formatDate(admissionPost.published_date)}
              </p>
            </div>

            {/* Post Content */}
            <div className="p-8">
              <div 
                className="richtext-output"
                dangerouslySetInnerHTML={{ 
                  __html: admissionPost.admission_circular_content || '<p>No content available.</p>' 
                }}
              />
            </div>

            {/* Post Footer */}
            <div className="bg-gray-50 px-8 py-4 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-600">
                <span>
                  Status: <span className="font-medium text-green-600 capitalize">{admissionPost.active_status}</span>
                </span>
                <span>
                  Last updated: {formatDate(admissionPost.published_date)}
                </span>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-8">
            <div className="inline-flex flex-col sm:flex-row gap-4">
              <Link 
                href="/admission-apply" 
                className="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
              >
                Apply Now
              </Link>
              <Link 
                href="/contact" 
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors duration-200 border border-gray-300"
              >
                Contact Admissions
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AdmissionPost;
