import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Back from '../../common/back/Back';
// Define placeholders for your common components


// Define the type for accordion items
interface AccordionItem {
  id: number;
  title: string;
  content: JSX.Element;
}

const CourseCsePage = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  
  const accordionData: AccordionItem[] = [
    {
      id: 0,
      title: 'Entry Requirements',
      content: (
        <ul className="list-[square] space-y-2 pl-5">
          <li>SSC or Equivalent with Minimum GPA 3 from Science Group.</li>
          <li>HSC or Equivalent with Minimum GPA 2.5 from Science Group.</li>
          <li>Minimum GPA 3.00 in Chemistry/ Higher Mathematics/Physics.</li>
          <li>Students from English medium have to complete in “O” level minimum 3B grades out of minimum 04 subjects and in “A” level minimum 1B grade out of minimum 02 subjects.</li>
        </ul>
      ),
    },
    {
      id: 1,
      title: 'Grading System',
      content: (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-[#E9F7FE] text-center text-[#004422]">
            <thead>
              <tr className="bg-[#B7DA9B]">
                <th className="border border-black p-2">Number Grade</th>
                <th className="border border-black p-2">Letter Grade</th>
                <th className="border border-black p-2">Grade Point</th>
                <th className="border border-black p-2">Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr><td className="border border-black p-2">80 - 100</td><td className="border border-black p-2">A+ (Plus)</td><td className="border border-black p-2">4.00</td><td className="border border-black p-2">Outstanding</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">75 - 79</td><td className="border border-black p-2">A (Regular)</td><td className="border border-black p-2">3.75</td><td className="border border-black p-2">Excellent</td></tr>
              <tr><td className="border border-black p-2">70 - 74</td><td className="border border-black p-2">A- (Minus)</td><td className="border border-black p-2">3.50</td><td className="border border-black p-2">Very good</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">65 - 69</td><td className="border border-black p-2">B+ (Plus)</td><td className="border border-black p-2">3.25</td><td className="border border-black p-2">Good</td></tr>
              <tr><td className="border border-black p-2">60 - 64</td><td className="border border-black p-2">B (Regular)</td><td className="border border-black p-2">3.00</td><td className="border border-black p-2">Satisfactory</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">55 - 59</td><td className="border border-black p-2">B- (Minus)</td><td className="border border-black p-2">2.75</td><td className="border border-black p-2">Above Average</td></tr>
              <tr><td className="border border-black p-2">50 - 54</td><td className="border border-black p-2">C+ (Plus)</td><td className="border border-black p-2">2.50</td><td className="border border-black p-2">Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">45 - 49</td><td className="border border-black p-2">C (Regular)</td><td className="border border-black p-2">2.25</td><td className="border border-black p-2">Below Average</td></tr>
              <tr><td className="border border-black p-2">40 - 44</td><td className="border border-black p-2">D</td><td className="border border-black p-2">2.00</td><td className="border border-black p-2">Below Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">Less Than 40</td><td className="border border-black p-2">F</td><td className="border border-black p-2">0.00</td><td className="border border-black p-2">Fail</td></tr>
              {/* ... Add other rows here ... */}
            </tbody>
          </table>
        </div>
      ),
    },
    {
      id: 2,
      title: 'Fee Structure',
      content: <img src="/images/fees/CSE_FEES_2025.jpg" alt="CSE Fee Structure" />,
    },
    {
      id: 3,
      title: 'Course Curriculum',
      content: <iframe src="https://docs.google.com/gview?url=https://nist.edu.bd/contents/uploads/syllabus/CSE-Course-Curriculam.pdf&embedded=true" className="h-[800px] w-full"></iframe>,
    },
  ];

  const cseFeatures = [
    'Skilled, expert faculty with university-level teaching standards.',
    'Dedicated modern labs, AC classroom.',
    'Fully Wi-Fi supported Campus.',
    'Outstanding academic performance by NIST students.',
    'Programming Club facilities.',
    'Central library with extensive resources.',
    'Foundation program for kickstarting computer literacy.',
    'Active participation in national and international competition, contests.',
    'Consultation center for top-performing students.',
    'Regular seminars, workshops, and webinars by industry professionals.',
  ];

  return (
    <>
      <Head title="Department of CSE" />
      <Back title="Department of Computer Science and Engineering" />
      
      {/* Intro Section */}
      <section className="container mx-auto px-4 py-12">
        <h3 className="text-lg font-medium text-center text-orange-500">WELLCOME</h3>
        <h2 className="text-4xl py-4 text-center uppercase font-bold text-[#000F38]">Department of Computer Science and Engineering</h2>
        <p className="text-justify mt-4 text-xl font-medium text-gray-600">
          Dept. of CSE, NIST welcomes all to the world of digital frontiers and emerging technologies. Learn, build and make a better Bangladesh. Our core values are to produce skillful graduates who can help better the world and country.
        </p>
      </section>

      {/* Why Study CSE Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row">
        <div className="basis-full md:basis-[56%]">
          <h2 className="text-4xl font-bold text-[#000F38]">Why Study CSE at NIST?</h2>
          <p className="my-4 text-justify text-gray-600">
            Computer Science and Engineering (CSE) is one of the most popular disciplines in Bangladesh due to its future aspects and a huge range of employment scopes. With a vast and skilful knowledge in the scientific and engineering aspects of computing, a graduate of the CSE discipline is highly likely to secure any prestigious job positions at home and across the world. To attain higher marketability and confidence enough to undertake any sort of challenges in the fields of computing, NIST is conducting this course with brilliant academics in the related field. Graduates after completing the course will be able to apply for the job roles of a System Database Administrator, Computer Programmer, Engineering Support Specialist, Data Warehouse Analyst, System Designer, Software Developer, Software Engineer, Lecturer/Professor, Research Analyst and many more in reputed institutions at home and abroad.
          </p>
          <div className="mt-6 space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-orange-500">MISSION</h3>
              <p className="text-justify text-gray-600">Computer Science and Engineering Discipline is committed to develop graduates with state of the art erudition and expertise to world class industries and research groups. The students were more complex problem solvers and lifelong learners who will be able to create, share and apply their knowledge in multidisciplinary areas to earn benefits for humanity.</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-orange-500">VISION</h3>
              <p className="text-justify text-gray-600">To provide leaders in the field of computer science and engineering by producing globally competitive professionals.</p>
            </div>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/cse/cse-lab1.jpg" alt="CSE Lab" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      {/* CSE Facilities Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row-reverse">
        <div className="basis-full md:basis-[56%]">
          <h3 className="text-base font-semibold text-orange-500">WHY CHOOSE NIST?</h3>
          <h2 className="my-2.5 max-w-xl text-4xl font-bold text-[#000F38]">CSE Facilities at NIST</h2>
          <div className="mt-4 rounded-lg bg-gray-50 p-4">
            <ul className="space-y-1.5">
              {cseFeatures.map((feature, index) => (
                <li key={index} className="border-l-4 border-[#07944a] bg-white px-2 py-1.5 text-base font-medium text-[#333] shadow-sm">
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/cse/cse-lab2.jpg" alt="Another CSE Lab" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>
      
      {/* Accordion Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-5xl px-4 md:w-[95%]">
          <h2 className="mb-10 text-center text-3xl font-bold uppercase text-[#000F38]">CSE Course Information</h2>
          <div className="space-y-2.5">
            {accordionData.map((item) => (
              <div key={item.id} className="overflow-hidden rounded-md bg-[#2c3e50] text-white">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="flex w-full cursor-pointer select-none items-center justify-between p-4 text-left text-lg font-medium transition-colors hover:bg-[#34495e] md:text-base"
                  aria-expanded={activeIndex === item.id}
                >
                  <span>{item.title}</span>
                  <span>{activeIndex === item.id ? <ChevronDown /> : <ChevronRight />}</span>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${activeIndex === item.id ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="bg-[#ecf0f1] p-4 text-[#2c3e50]">
                    {item.content}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default CourseCsePage;