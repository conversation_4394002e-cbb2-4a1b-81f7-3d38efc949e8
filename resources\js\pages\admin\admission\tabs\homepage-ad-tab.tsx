import React, { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import Pagination from '@/components/pagination';
import InputError from '@/components/input-error';
import StatusToggle from '@/components/ui/status-toggle';
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';

interface HomepageAdData {
    id: number;
    ad_title: string;
    program_info_title: string;
    session: string;
    offer_title: string;
    offer_text: string;
    active_status: 'active' | 'inactive';
}

interface PaginatedData {
    data: HomepageAdData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface HomepageAdTabProps {
    data: PaginatedData;
}

export default function HomepageAdTab({ data }: HomepageAdTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<HomepageAdData | null>(null);
    const [viewingItem, setViewingItem] = useState<HomepageAdData | null>(null);

    const { data: formData, setData, post, put, processing, errors, reset } = useForm({
        ad_title: '',
        program_info_title: '',
        session: '',
        offer_title: '',
        offer_text: '',
        active_status: 'inactive' as 'active' | 'inactive',
    });

    const handleCreate = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.admission.homepage-ads.store'), {
            onSuccess: () => {
                setIsCreateOpen(false);
                reset();
            },
        });
    };

    const handleEdit = (item: HomepageAdData) => {
        setEditingItem(item);
        setData({
            ad_title: item.ad_title || '',
            program_info_title: item.program_info_title || '',
            session: item.session || '',
            offer_title: item.offer_title || '',
            offer_text: item.offer_text || '',
            active_status: item.active_status,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            put(route('admin.admission.homepage-ads.update', editingItem.id), {
                onSuccess: () => {
                    setIsEditOpen(false);
                    setEditingItem(null);
                    reset();
                },
            });
        }
    };

    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Homepage Ad',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.homepage-ads.destroy', id));
        }
    };

    const handleView = (item: HomepageAdData) => {
        setViewingItem(item);
    };

    return (
        <div className="space-y-4 md:space-y-6">
            <div className="flex flex-col gap-3 md:flex-row md:justify-between md:items-center">
                <h2 className="text-lg md:text-xl font-semibold">Homepage Ads</h2>
                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button className="w-full md:w-auto">
                            <Plus className="mr-2 h-4 w-4" />
                            <span className="hidden sm:inline">Add Homepage Ad</span>
                            <span className="sm:hidden">Add Ad</span>
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle className="text-lg md:text-xl">Create Homepage Ad</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="ad_title">Ad Title</Label>
                                    <Input
                                        id="ad_title"
                                        value={formData.ad_title}
                                        onChange={(e) => setData('ad_title', e.target.value)}
                                        placeholder="Enter ad title"
                                    />
                                    <InputError message={errors.ad_title} />
                                </div>
                                <div>
                                    <Label htmlFor="program_info_title">Program Info Title</Label>
                                    <Input
                                        id="program_info_title"
                                        value={formData.program_info_title}
                                        onChange={(e) => setData('program_info_title', e.target.value)}
                                        placeholder="Enter program info title"
                                    />
                                    <InputError message={errors.program_info_title} />
                                </div>
                                <div>
                                    <Label htmlFor="session">Session</Label>
                                    <Input
                                        id="session"
                                        value={formData.session}
                                        onChange={(e) => setData('session', e.target.value)}
                                        placeholder="e.g., Spring 2024"
                                    />
                                    <InputError message={errors.session} />
                                </div>
                                <div>
                                    <Label htmlFor="offer_title">Offer Title</Label>
                                    <Input
                                        id="offer_title"
                                        value={formData.offer_title}
                                        onChange={(e) => setData('offer_title', e.target.value)}
                                        placeholder="Enter offer title"
                                    />
                                    <InputError message={errors.offer_title} />
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="offer_text">Offer Text</Label>
                                <Textarea
                                    id="offer_text"
                                    value={formData.offer_text}
                                    onChange={(e) => setData('offer_text', e.target.value)}
                                    placeholder="Enter offer description"
                                    rows={4}
                                />
                                <InputError message={errors.offer_text} />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Create
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent className="p-0">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="min-w-[150px]">Ad Title</TableHead>
                                    <TableHead className="min-w-[100px]">Session</TableHead>
                                    <TableHead className="min-w-[100px]">Status</TableHead>
                                    <TableHead className="min-w-[120px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell className="font-medium">{item.ad_title || 'N/A'}</TableCell>
                                    {/*<TableCell>{item.program_info_title || 'N/A'}</TableCell>*/}
                                    <TableCell>{item.session || 'N/A'}</TableCell>
                                    <TableCell>
                                        <StatusToggle
                                            id={item.id}
                                            currentStatus={item.active_status}
                                            updateRoute={route('admin.admission.homepage-ads.update', item.id)}
                                            activeLabel="Active"
                                            inactiveLabel="Inactive"
                                            confirmTitle="Change Ad Status"
                                            confirmDescription="Are you sure you want to change the status of this homepage ad?"
                                            statusField="active_status"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex flex-col gap-1 md:flex-row md:space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)} className="w-full md:w-auto">
                                                        <Eye className="h-4 w-4 md:mr-0 mr-2" />
                                                        <span className="md:hidden">View</span>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleEdit(item)} className="w-full md:w-auto">
                                                        <Edit className="h-4 w-4 md:mr-0 mr-2" />
                                                        <span className="md:hidden">Edit</span>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.ad_title || 'Homepage Ad')} className="w-full md:w-auto">
                                                        <Trash2 className="h-4 w-4 md:mr-0 mr-2" />
                                                        <span className="md:hidden">Delete</span>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    </div>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="text-lg md:text-xl">Edit Homepage Ad</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit_ad_title">Ad Title</Label>
                                <Input
                                    id="edit_ad_title"
                                    value={formData.ad_title}
                                    onChange={(e) => setData('ad_title', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_program_info_title">Program Info Title</Label>
                                <Input
                                    id="edit_program_info_title"
                                    value={formData.program_info_title}
                                    onChange={(e) => setData('program_info_title', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_session">Session</Label>
                                <Input
                                    id="edit_session"
                                    value={formData.session}
                                    onChange={(e) => setData('session', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_offer_title">Offer Title</Label>
                                <Input
                                    id="edit_offer_title"
                                    value={formData.offer_title}
                                    onChange={(e) => setData('offer_title', e.target.value)}
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="edit_offer_text">Offer Text</Label>
                            <Textarea
                                id="edit_offer_text"
                                value={formData.offer_text}
                                onChange={(e) => setData('offer_text', e.target.value)}
                                rows={4}
                            />
                        </div>
                        <div>
                            <Label htmlFor="edit_active_status">Status *</Label>
                            <Select value={formData.active_status} onValueChange={(value) => setData('active_status', value as 'active' | 'inactive')}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>View Homepage Ad</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Ad Title</Label>
                                    <p className="text-sm font-medium">{viewingItem.ad_title || 'N/A'}</p>
                                </div>
                                <div>
                                    <Label>Session</Label>
                                    <p className="text-sm">{viewingItem.session || 'N/A'}</p>
                                </div>
                            </div>

                            <div>
                                <Label>Program Info Title</Label>
                                <p className="text-sm">{viewingItem.program_info_title || 'N/A'}</p>
                            </div>

                            <div>
                                <Label>Offer Title</Label>
                                <p className="text-sm font-medium">{viewingItem.offer_title || 'N/A'}</p>
                            </div>

                            <div>
                                <Label>Offer Text</Label>
                                <p className="text-sm text-gray-600">{viewingItem.offer_text || 'N/A'}</p>
                            </div>

                            <div>
                                <Label>Status</Label>
                                <Badge variant={viewingItem.active_status === 'active' ? 'default' : 'secondary'}>
                                    {viewingItem.active_status === 'active' ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
