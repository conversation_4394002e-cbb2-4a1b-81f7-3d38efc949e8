import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import {
    Bell,
    Newspaper,
    Users,
    UserCheck,
    UsersIcon,
    Image,
    Calendar,
    GraduationCap,
    Plus,
    Eye,
    ArrowRight
} from 'lucide-react';
import { format } from 'date-fns';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard() {
    const { auth, stats, recentNotices, recentNews, recentAdmissionPosts } = usePage<SharedData>().props;
    const currentDate = format(new Date(), 'EEEE, MMMM do, yyyy');



    // Provide default values if data is not available
    const dashboardStats = stats || {
        total_notices: 0,
        total_news: 0,
        total_faculty: 0,
        total_governing_body: 0,
        total_users: 0,
        total_gallery: 0,
        total_image_sliders: 0,
        total_class_schedule: 0,
        total_admission_posts: 0,
    };

    // Provide default values for recent items
    const dashboardRecentNotices = recentNotices || [];
    const dashboardRecentNews = recentNews || [];
    const dashboardRecentAdmissionPosts = recentAdmissionPosts || [];

    const statCards = [
        {
            title: 'Total Notices',
            value: dashboardStats.total_notices,
            icon: Bell,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            href: '/admin/noticeboard'
        },
        {
            title: 'Total News',
            value: dashboardStats.total_news,
            icon: Newspaper,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
            href: '/admin/news'
        },
        {
            title: 'Total Faculty',
            value: dashboardStats.total_faculty,
            icon: Users,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
            href: '/admin/faculty'
        },
        {
            title: 'Governing Body',
            value: dashboardStats.total_governing_body,
            icon: UserCheck,
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
            href: '/admin/governingbody'
        },
        {
            title: 'Total Users',
            value: dashboardStats.total_users,
            icon: UsersIcon,
            color: 'text-red-600',
            bgColor: 'bg-red-50',
            href: '/admin/users'
        },
        {
            title: 'Gallery Items',
            value: dashboardStats.total_gallery,
            icon: Image,
            color: 'text-pink-600',
            bgColor: 'bg-pink-50',
            href: '/admin/file-upload?tab=gallery'
        },
        {
            title: 'Image Sliders',
            value: dashboardStats.total_image_sliders,
            icon: Image,
            color: 'text-cyan-600',
            bgColor: 'bg-cyan-50',
            href: '/admin/file-upload?tab=image-slider'
        },
        {
            title: 'Class Schedules',
            value: dashboardStats.total_class_schedule,
            icon: Calendar,
            color: 'text-indigo-600',
            bgColor: 'bg-indigo-50',
            href: '/admin/schedule'
        },
        {
            title: 'Admission Posts',
            value: dashboardStats.total_admission_posts,
            icon: GraduationCap,
            color: 'text-teal-600',
            bgColor: 'bg-teal-50',
            href: '/admin/admission'
        }
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-4 overflow-x-auto">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-gray-200">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div>
                            <h1 className="text-2xl md:text-3xl font-light text-gray-800">
                                Welcome back, <span className="font-medium">{auth.user.name}</span>
                            </h1>
                            <p className="text-gray-600 mt-1">{currentDate}</p>
                        </div>
                        <div className="text-right">
                            <div className="text-2xl md:text-3xl font-bold text-gray-800">
                                NIST wCMS
                            </div>
                            <div className="text-sm text-gray-500">v1.1</div>
                        </div>
                    </div>
                </div>

                {/* Stats Cards - Responsive Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {statCards.map((card, index) => {
                        const Icon = card.icon;
                        return (
                            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">{card.title}</p>
                                            <p className="text-2xl font-bold text-gray-900 mt-1">{card.value}</p>
                                        </div>
                                        <div className={`p-3 rounded-full ${card.bgColor}`}>
                                            <Icon className={`h-6 w-6 ${card.color}`} />
                                        </div>
                                    </div>
                                    <div className="mt-4">
                                        <Button variant="ghost" size="sm" asChild className="w-full justify-between">
                                            <Link href={card.href}>
                                                <span>View Details</span>
                                                <Eye className="h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Recent Items Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Recent Notices */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-semibold">Recent Notices</CardTitle>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/admin/noticeboard/create">
                                    <Plus className="h-4 w-4 mr-1" />
                                    Create
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {dashboardRecentNotices.length > 0 ? (
                                    dashboardRecentNotices.map((notice) => (
                                        <div key={notice.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <Bell className="h-4 w-4 text-blue-600 mt-1 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {notice.title}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {format(new Date(notice.created_at), 'MMM dd, yyyy')}
                                                </p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-gray-500 text-center py-4">No recent notices</p>
                                )}
                            </div>
                            {dashboardRecentNotices.length > 0 && (
                                <div className="mt-4">
                                    <Button variant="ghost" size="sm" asChild className="w-full">
                                        <Link href="/admin/noticeboard">
                                            View All <ArrowRight className="h-4 w-4 ml-1" />
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Recent News */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-semibold">Recent News</CardTitle>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/admin/news/create">
                                    <Plus className="h-4 w-4 mr-1" />
                                    Create
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {dashboardRecentNews.length > 0 ? (
                                    dashboardRecentNews.map((news) => (
                                        <div key={news.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <Newspaper className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {news.title}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {format(new Date(news.created_at), 'MMM dd, yyyy')}
                                                </p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-gray-500 text-center py-4">No recent news</p>
                                )}
                            </div>
                            {dashboardRecentNews.length > 0 && (
                                <div className="mt-4">
                                    <Button variant="ghost" size="sm" asChild className="w-full">
                                        <Link href="/admin/news">
                                            View All <ArrowRight className="h-4 w-4 ml-1" />
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Recent Admission Posts */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-lg font-semibold">Recent Admission Posts</CardTitle>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/admin/admission?tab=admission_post">
                                    <Plus className="h-4 w-4 mr-1" />
                                    Create
                                </Link>
                            </Button>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {dashboardRecentAdmissionPosts.length > 0 ? (
                                    dashboardRecentAdmissionPosts.map((post) => (
                                        <div key={post.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                                            <GraduationCap className="h-4 w-4 text-teal-600 mt-1 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {post.title}
                                                </p>
                                                <p className="text-xs text-gray-500">
                                                    {format(new Date(post.created_at), 'MMM dd, yyyy')}
                                                </p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-gray-500 text-center py-4">No recent admission posts</p>
                                )}
                            </div>
                            {dashboardRecentAdmissionPosts.length > 0 && (
                                <div className="mt-4">
                                    <Button variant="ghost" size="sm" asChild className="w-full">
                                        <Link href="/admin/admission">
                                            View All <ArrowRight className="h-4 w-4 ml-1" />
                                        </Link>
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
