/*--------head------------*/
.head {
  padding: 6px 0;
  color: #d0d6dd;
  background-color: #0a4727;
}
.logo {
  margin-right: 20px;
}
.logo h1 {
  font-size: 35px;
  font-weight: 900;
}
.head .icon {
  margin-left: 10px;
}
.top-contact ul {
  list-style: none; /* Remove bullet points */
  padding: 0; /* Remove default padding */
  margin: 0; /* Remove default margin */
  display: flex; /* Display items in a row */
  align-items: center; /* Align items vertically in the center */
}

.top-contact ul li {
  margin-right: 10px; /* Add some space between the list items */
}

.top-contact ul li:last-child {
  margin-right: 0; /* Remove right margin from the last item */
}

.top-contact a {
  text-decoration: none; /* Remove underline from links */
  color: inherit; /* Inherit color from parent element */
}

.top-contact i {
  margin-right: 5px; /* Add some space between the icon and text */
}

.top-left-nav {
  
  display: flex;
}

.top-right-action {
  align-items: flex-end;
}

.top-right-action ul{
  margin: 0px;
}

.top-apply-button {
  background-color: #fdc800;
  border: 1px solid #fdc800;
  border-radius: 5px;
  text-transform: uppercase;
  color: #0a4727;
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.5;
  padding: 4px 8px;
  text-align: center;
  -webkit-transition: all 0.5s ease-out 0s;
  transition: all 0.5s ease-out 0s;
}

.top-apply-button:hover {
  background-color: #0a4727;
  border: 1px solid #FFBC42;
  color: #FFBC42;
  transition: all 0.7s;
}

.top-apply-button a {
  color: inherit;
  text-decoration: none;
}





/*--------head------------*/
/*--------header------------*/
header {
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 30px;
}
header .nav {
  display: flex !important;
}
header li {
  margin-right: 15px; /*40px navbar, 25px when item list small*/
}
header ul {
  padding: 30px 20px;
}
header ul li a {
  color: #fff;
  font-weight: 500;
  transition: 0.5s;
}
header ul li a:hover {
  color: #1eb2a6;
}
.start {
  background-color: #1eb2a6;
  padding: 30px 70px;
  color: #fff;
  clip-path: polygon(10% 0%, 100% 0, 100% 100%, 0% 100%);
}
.toggle {
  display: none;
}
/*--------header------------*/
@media screen and (max-width: 768px) {
  .start {
    clip-path: none;
  }
  .head {
    display: none;
  }
  header {
    margin: 0;
    background-color: #ffffff;
    position: relative;
  }
  header ul.flexSB {
    display: none;
  }
  header ul li {
    margin-bottom: 20px;
  }
  .toggle {
    display: block;
    background: none;
    color: #fff;
    font-size: 30px;
    position: absolute;
    right: 0;
    top: -20px;
  }
  .mobile-nav {
    position: absolute;
    top: 7vh;
    left: 0;
    width: 100%;
    background-color: #1eb2a6;
  }
}
