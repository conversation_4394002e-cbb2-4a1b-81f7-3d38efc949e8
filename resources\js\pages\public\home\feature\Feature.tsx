import React from 'react';
import { PlayCircle } from 'lucide-react'; // For a modern play icon

// Define the component's props for TypeScript
interface FeatureProps {
  setPlayState: (state: boolean) => void;
}

const Feature: React.FC<FeatureProps> = ({ setPlayState }) => {
  // Data for the features list for easier management
  const features = [
    'Skilled, expert faculty with university-level teaching standards.',
    'Dedicated modern labs for every program.',
    'State of the art Biochemistry LAB.',
    'Central library with extensive resources.',
    'Employment opportunities within the NIST network.',
    'Active participation in national and international competition, contests.',
    'Consultation center for top-performing students.',
    'Engaging club activities (IT, Programming, Cultural, Research, Skill Development).',
    'Regular seminars, workshops, and webinars by industry professionals.',
  ];

  return (
    <section className="mx-auto my-20 flex w-[90%] flex-col items-center justify-between md:flex-row">
      {/* Left side with Image and Video Play Button */}
      <div className="group relative basis-full md:basis-2/5 md:order-1 order-2 m-5">
        <img 
          src="/images/cover/tour.jpg" 
          alt="campus tour" 
          className="w-full rounded-lg" 
        />
        <div 
          onClick={() => setPlayState(true)}
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 cursor-pointer opacity-90 transition-transform duration-300 ease-in-out group-hover:scale-110"
        >
          <img 
          src="/assets/play-icon.png" 
          alt="campus tour" 
          className="w-full rounded-lg" 
        />
        </div>
        <div className="absolute bottom-0 left-0 flex h-1/3 w-full items-center justify-center rounded-b-lg bg-black/50 p-2.5 text-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <h2 className="text-4xl font-bold text-gray-300">Campus Tour</h2>
        </div>
      </div>
      
      {/* Right side with Feature List */}
      <div className="basis-full md:basis-[56%] md:order-2 order-1">
        <h3 className="text-base font-semibold text-orange-500">WHY CHOOSE NIST?</h3>
        <h1 className="my-2.5 max-w-md text-4xl font-bold text-[#000F38]">Discover the amazing facilities at NIST</h1>
        <div className="mx-auto mt-4 max-w-3xl rounded-lg bg-gray-50 p-4">
          <ul className="pl-0">
            {features.map((feature, index) => (
              <li 
                key={index}
                className="mb-1.25 border-l-4 border-[#07944a] bg-white px-2 py-1.5 text-base font-medium text-[#333] shadow-md last:mb-0"
              >
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
};

export default Feature;