<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('news', function (Blueprint $table) {
            // Add published_at column, nullable for draft posts
            

            // Add active_status column, default 'active'
            $table->enum('active_status', ['active', 'inactive'])->default('active')->after('published_at');
        });
    }

    public function down(): void
    {
        Schema::table('news', function (Blueprint $table) {
          
            $table->dropColumn('active_status');
        });
    }
};
