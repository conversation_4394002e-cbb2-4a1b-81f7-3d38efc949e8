import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { Head, useForm, Link, router } from '@inertiajs/react';
import { Loader2 } from 'lucide-react'; // For a loading spinner icon
import React, { useState } from 'react';
import Back from '../../common/back/Back'; // Assuming correct path

// Define the shape of our form data for TypeScript
interface FormData {
    date: string;
    phone: string;
    name: string;
    permanent_address: string;
    present_address: string;
    ssc_institute: string;
    ssc_group: string;
    ssc_gpa: string;
    ssc_year: string;
    hsc_institute: string;
    hsc_group: string;
    hsc_gpa: string;
    hsc_year: string;
    hsc_reg: string;
    department: string;
    uid: string;
}

interface deptData {
    uid: string;
    department: string;
}

const ApplicationForm = () => {
    const [popupMessage, setPopupMessage] = useState(''); // State for popup message
    const [showPopup, setShowPopup] = useState(false); // State to control popup visibility
    const [isSubmitting, setIsSubmitting] = useState(false); // State to control submission state
    const [isProcessing, setIsProcessing] = useState(false); // State to control submission state
    const [successData, setSuccessData] = useState<deptData | null>(null); // State to store success data
    // useForm hook manages state, validation errors, and submission for you
    const { data, setData, post, processing, reset, errors } = useForm<FormData>({
        date: '',
        phone: '',
        name: '',
        permanent_address: '',
        present_address: '',
        ssc_institute: '',
        ssc_group: '',
        ssc_gpa: '',
        ssc_year: '',
        hsc_institute: '',
        hsc_group: '',
        hsc_gpa: '',
        hsc_year: '',
        hsc_reg: '',
        department: '',
        uid: '',
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;

        setIsSubmitting(true);
        setIsProcessing(true);
        console.log("datap: ",processing);

        // Create the final payload, including the generated UID.
        const day = new Date().getDate().toString();
        const date = new Date().toLocaleDateString();
        const uid = `${data.department}${day}nist${data.phone.slice(5)}`;
        setSuccessData({ uid, department: data.department });
        const payload = { ...data, date, uid };
        console.log("data: ",date, payload);
        //const scriptURL = 'https://script.google.com/macros/s/AKfycbyYuUQw7WopywvgZcs53-TaoxnBPqthDov8K_8Ae-FZEeZDV61ef9ywn3jU5DAE6TrF/exec';
        const scriptURL = 'https://script.google.com/macros/s/AKfycbyvy9uTtj1gSGICyITbKIM-d_18psKkUP0fZkKguSqG9W38EdrG3Ba0bSJfuSNwg5rWhw/exec';
        
        try {
            // Use the 'payload' object here, which contains the complete form data.
            await fetch(scriptURL, {
                method: 'POST',
                body: new URLSearchParams(payload as any),
            });
            setPopupMessage('Form Submitted Successfully!');
            reset();
             // Clears the form fields on success
        } catch (error) {
            console.error('Error!', error);
            setPopupMessage('Error submitting form. Please try again.');
        } finally {
            setIsSubmitting(false);
            setIsProcessing(false);
            setShowPopup(true);
        }
    };

    const handlePopupConfirm = () => {
        setShowPopup(false);
        if (popupMessage === 'Form Submitted Successfully!') {
        //history.push(`/apply/success/${formData.uid}/${formData.department}`); // Navigate to success page if the form is submitted successfully
        console.log("datau: ",successData?.uid, successData?.department);
        
        router.visit(`/apply/success/${successData?.uid}/${successData?.department}`);
    }
    };

    return (
        <>
            <Head title="Application Form" />
            <Back title="Application Form" />
            <div className="flex justify-center bg-gray-50 p-4">
                <form onSubmit={handleSubmit} className="w-full max-w-4xl rounded-lg bg-white p-6 shadow-md">
                    {/* Personal Information Section */}
                    <div>
                        <h2 className="mb-6 text-lg font-semibold text-gray-800">Personal Information:</h2>

                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {/* Phone */}
                            <div className="space-y-2">
                                <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                                    Phone*
                                </Label>
                                <Input
                                    id="phone"
                                    type="tel"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    className={cn('w-full', errors.phone && 'border-red-500')}
                                    required
                                />
                                {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                            </div>

                            {/* Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                                    Name*
                                </Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className={cn('w-full', errors.name && 'border-red-500')}
                                    required
                                />
                                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                            </div>

                            {/* Present Address */}
                            <div className="space-y-2">
                                <Label htmlFor="present_address" className="text-sm font-medium text-gray-700">
                                    Present Address: *
                                </Label>
                                <Textarea
                                    id="present_address"
                                    placeholder="Enter your present address"
                                    value={data.present_address}
                                    onChange={(e) => setData('present_address', e.target.value)}
                                    className={cn('min-h-[100px] w-full resize-none', errors.present_address && 'border-red-500')}
                                    required
                                />
                                {errors.present_address && <p className="text-sm text-red-500">{errors.present_address}</p>}
                            </div>

                            {/* Permanent Address */}
                            <div className="space-y-2">
                                <Label htmlFor="permanent_address" className="text-sm font-medium text-gray-700">
                                    Permanent Address: *
                                </Label>
                                <Textarea
                                    id="permanent_address"
                                    placeholder="Enter your permanent address"
                                    value={data.permanent_address}
                                    onChange={(e) => setData('permanent_address', e.target.value)}
                                    className={cn('min-h-[100px] w-full resize-none', errors.permanent_address && 'border-red-500')}
                                    required
                                />
                                {errors.permanent_address && <p className="text-sm text-red-500">{errors.permanent_address}</p>}
                            </div>
                        </div>
                    </div>

                    {/* SSC Information Section */}
                    <div className="mt-8">
                        <h2 className="mb-6 text-lg font-semibold text-gray-800">Education Information : SSC</h2>

                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {/* SSC */}
                            <div className="space-y-2">
                                <Label htmlFor="ssc_institute" className="text-sm font-medium text-gray-700">
                                    SSC School*
                                </Label>
                                <Input
                                    id="ssc_institute"
                                    type="text"
                                    value={data.ssc_institute}
                                    onChange={(e) => setData('ssc_institute', e.target.value)}
                                    className={cn('w-full', errors.ssc_institute && 'border-red-500')}
                                    required
                                />
                                {errors.ssc_institute && <p className="text-sm text-red-500">{errors.name}</p>}
                            </div>
                            {/* SSC Group */}
                            <div className="space-y-2">
                                <Label htmlFor="ssc_group" className="text-sm font-medium text-gray-700">
                                    SSC Group*
                                </Label>
                                <Input
                                    id="ssc_group"
                                    type="text"
                                    value={data.ssc_group}
                                    onChange={(e) => setData('ssc_group', e.target.value)}
                                    className={cn('w-full', errors.ssc_group && 'border-red-500')}
                                    required
                                />
                                {errors.ssc_group && <p className="text-sm text-red-500">{errors.ssc_group}</p>}
                            </div>

                            {/* SSC GPA */}
                            <div className="space-y-2">
                                <Label htmlFor="ssc_gpa" className="text-sm font-medium text-gray-700">
                                    SSC GPA*
                                </Label>
                                <Input
                                    id="ssc_gpa"
                                    type="text"
                                    value={data.ssc_gpa}
                                    onChange={(e) => setData('ssc_gpa', e.target.value)}
                                    className={cn('w-full', errors.ssc_gpa && 'border-red-500')}
                                    required
                                />
                                {errors.ssc_gpa && <p className="text-sm text-red-500">{errors.ssc_gpa}</p>}
                            </div>

                            {/* SSC Passing year */}
                            <div className="space-y-2">
                                <Label htmlFor="ssc_year" className="text-sm font-medium text-gray-700">
                                    SSC Passing year*
                                </Label>
                                <Input
                                    id="ssc_year"
                                    type="text"
                                    value={data.ssc_year}
                                    onChange={(e) => setData('ssc_year', e.target.value)}
                                    className={cn('w-full', errors.ssc_year && 'border-red-500')}
                                    required
                                />
                                {errors.ssc_year && <p className="text-sm text-red-500">{errors.ssc_year}</p>}
                            </div>
                        </div>
                    </div>

                    {/* HSC Information Section */}
                    <div className="mt-8">
                        <h2 className="mb-6 text-lg font-semibold text-gray-800">Education Information : HSC</h2>

                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {/* HSC */}
                            <div className="space-y-2">
                                <Label htmlFor="hsc_institute" className="text-sm font-medium text-gray-700">
                                    HSC School*
                                </Label>
                                <Input
                                    id="hsc_institute"
                                    type="text"
                                    value={data.hsc_institute}
                                    onChange={(e) => setData('hsc_institute', e.target.value)}
                                    className={cn('w-full', errors.hsc_institute && 'border-red-500')}
                                    required
                                />
                                {errors.hsc_institute && <p className="text-sm text-red-500">{errors.hsc_institute}</p>}
                            </div>
                            {/* HSC Group */}
                            <div className="space-y-2">
                                <Label htmlFor="hsc_group" className="text-sm font-medium text-gray-700">
                                    HSC Group*
                                </Label>
                                <Input
                                    id="hsc_group"
                                    type="text"
                                    value={data.hsc_group}
                                    onChange={(e) => setData('hsc_group', e.target.value)}
                                    className={cn('w-full', errors.hsc_group && 'border-red-500')}
                                    required
                                />
                                {errors.hsc_group && <p className="text-sm text-red-500">{errors.hsc_group}</p>}
                            </div>

                            {/* HSC GPA */}
                            <div className="space-y-2">
                                <Label htmlFor="hsc_gpa" className="text-sm font-medium text-gray-700">
                                    HSC GPA*
                                </Label>
                                <Input
                                    id="hsc_gpa"
                                    type="text"
                                    value={data.hsc_gpa}
                                    onChange={(e) => setData('hsc_gpa', e.target.value)}
                                    className={cn('w-full', errors.hsc_gpa && 'border-red-500')}
                                    required
                                />
                                {errors.hsc_gpa && <p className="text-sm text-red-500">{errors.hsc_gpa}</p>}
                            </div>

                            {/* HSC Passing year */}
                            <div className="space-y-2">
                                <Label htmlFor="hsc_year" className="text-sm font-medium text-gray-700">
                                    HSC Passing year*
                                </Label>
                                <Input
                                    id="hsc_year"
                                    type="text"
                                    value={data.hsc_year}
                                    onChange={(e) => setData('hsc_year', e.target.value)}
                                    className={cn('w-full', errors.hsc_year && 'border-red-500')}
                                    required
                                />
                                {errors.hsc_year && <p className="text-sm text-red-500">{errors.hsc_year}</p>}
                            </div>

                            {/* HSC Reg */}
                            <div className="space-y-2">
                                <Label htmlFor="hsc_reg" className="text-sm font-medium text-gray-700">
                                    HSC Registration*
                                </Label>
                                <Input
                                    id="hsc_reg"
                                    type="text"
                                    value={data.hsc_reg}
                                    onChange={(e) => setData('hsc_reg', e.target.value)}
                                    className={cn('w-full', errors.hsc_reg && 'border-red-500')}
                                    required
                                />
                                {errors.hsc_reg && <p className="text-sm text-red-500">{errors.hsc_reg}</p>}
                            </div>
                        </div>
                    </div>

                    {/* Department Selection Section */}
                    <div className="mt-8">
                        <h2 className="mb-4 text-lg font-semibold text-gray-800">Select Department for Admission:</h2>

                        <p className="mb-6 text-sm text-gray-600">
                            Choose the department that you want to admit. NIST offers B.Sc Hons. (Professional) in CSE, BBA, ECE and B.Sc Hons. in
                            Biochemestry Discipline.
                        </p>

                        <div className="max-w-md space-y-2">
                            <Label htmlFor="department" className="text-sm font-medium text-gray-700">
                                Department*
                            </Label>
                            <Select value={data.department} onValueChange={(value) => setData('department', value)}>
                                <SelectTrigger className={cn('w-full', errors.department && 'border-red-500')}>
                                    <SelectValue placeholder="Choose department" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="cse">B.Sc Hons. (Professional) in CSE</SelectItem>
                                    <SelectItem value="bba">BBA</SelectItem>
                                    <SelectItem value="ece">B.Sc Hons. (Professional) in ECE</SelectItem>
                                    <SelectItem value="biochemistry">B.Sc Hons. in Biochemestry Discipline</SelectItem>
                                </SelectContent>
                            </Select>
                            {errors.department && <p className="text-sm text-red-500">{errors.department}</p>}
                        </div>
                    </div>

                    <button
                        type="submit"
                        disabled={isProcessing}
                        className="flex w-32 items-center justify-center rounded bg-green-600 px-4 py-2 text-white transition hover:bg-green-700 disabled:bg-gray-400"
                    >
                        {isProcessing ? <Loader2 className="animate-spin" /> : 'Submit'}
                    </button>
                </form>
            </div>
            {/* Custom Popup */}
            {showPopup && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                    <div className="rounded-lg bg-white p-6 text-center shadow-lg">
                        <p className="mb-4">{popupMessage}</p>
                        <button onClick={handlePopupConfirm} className="rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700">
                            OK
                        </button>
                    </div>
                </div>
            )}
        </>
    );
};

export default ApplicationForm;
