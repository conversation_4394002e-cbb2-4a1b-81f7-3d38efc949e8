<?php

namespace App\Http\Controllers\News;
use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;
use App\Http\Resources\NewsResource;
use Illuminate\Support\Str;


class NewsEditController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(News $news)
    {

        return inertia('admin/news/edit', [
            'currentNews' => new NewsResource($news),
            'draftToken' => (string) Str::uuid(),
        ]);


    }
}

