import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Back from '../../common/back/Back';



// Define the type for accordion items
interface AccordionItem {
  id: number;
  title: string;
  content: JSX.Element;
}

const CourseBmbPage = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };
  
  // BMB-specific accordion data
  const accordionData: AccordionItem[] = [
    {
      id: 0,
      title: 'Entry Requirements',
      content: (
        <ul className="list-[square] space-y-2 pl-5">
          <li>SSC or Equivalent with Minimum GPA 2.75 from Science Group.</li>
          <li>HSC or Equivalent with Minimum GPA 2.5 from Science Group.</li>
          <li>Minimum GPA 2.5 in Chemistry/Biology/Psychology.</li>
          <li>Students from English medium have to complete in “O” level minimum 3B grades out of minimum 04 subjects and in “A” level minimum 1B grade out of minimum 02 subjects.</li>
        </ul>
      ),
    },
    {
      id: 1,
      title: 'Grading System',
      content: (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-[#E9F7FE] text-center text-[#004422]">
            <thead>
              <tr className="bg-[#B7DA9B]">
                <th className="border border-black p-2">Number Grade</th>
                <th className="border border-black p-2">Letter Grade</th>
                <th className="border border-black p-2">Grade Point</th>
                <th className="border border-black p-2">Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr><td className="border border-black p-2">80 - 100</td><td className="border border-black p-2">A+ (Plus)</td><td className="border border-black p-2">4.00</td><td className="border border-black p-2">Outstanding</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">75 - 79</td><td className="border border-black p-2">A (Regular)</td><td className="border border-black p-2">3.75</td><td className="border border-black p-2">Excellent</td></tr>
              <tr><td className="border border-black p-2">70 - 74</td><td className="border border-black p-2">A- (Minus)</td><td className="border border-black p-2">3.50</td><td className="border border-black p-2">Very Good</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">65 - 69</td><td className="border border-black p-2">B+ (Plus)</td><td className="border border-black p-2">3.25</td><td className="border border-black p-2">Good</td></tr>
              <tr><td className="border border-black p-2">60 - 64</td><td className="border border-black p-2">B (Regular)</td><td className="border border-black p-2">3.00</td><td className="border border-black p-2">Satisfactory</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">55 - 59</td><td className="border border-black p-2">B- (Minus)</td><td className="border border-black p-2">2.75</td><td className="border border-black p-2">Above Average</td></tr>
              <tr><td className="border border-black p-2">50 - 54</td><td className="border border-black p-2">C+ (Plus)</td><td className="border border-black p-2">2.50</td><td className="border border-black p-2">Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">45 - 49</td><td className="border border-black p-2">C (Regular)</td><td className="border border-black p-2">2.25</td><td className="border border-black p-2">Below Average</td></tr>
              <tr><td className="border border-black p-2">40 - 44</td><td className="border border-black p-2">D</td><td className="border border-black p-2">2.00</td><td className="border border-black p-2">Pass</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border border-black p-2">Less Than 40</td><td className="border border-black p-2">F</td><td className="border border-black p-2">0.00</td><td className="border border-black p-2">Fail</td></tr>
            </tbody>
          </table>
        </div>
      ),
    },
    {
      id: 2,
      title: 'Fee Structure',
      content: <img src="/images/fees/BMB_FEES_2025.jpg" alt="BMB Fee Structure" />,
    },
    {
      id: 3,
      title: 'Course Curriculum',
      content: (
        <iframe
          src="https://docs.google.com/gview?url=https://nist.edu.bd/contents/uploads/syllabus/BioChemistry-Course-Curriculam.pdf&embedded=true"
          className="h-[800px] w-full"
        ></iframe>
      ),
    },
  ];

  // BMB-specific features
  const bmbFeatures = [
    'Skilled, expert faculty with university-level teaching standards.',
    'Dedicated modern labs, AC classroom.',
    'Fully Wi-Fi supported Campus.',
    'Outstanding academic performance by NIST students.',
    'State of the art lab facilities.',
    'Central library with extensive resources.',
    'Foundation program for kickstarting computer literacy.',
    'Active participation in national and international competition, contests.',
    'Consultation center for top-performing students.',
    'Regular seminars, workshops, and webinars by industry professionals.',
  ];

  return (
    <>
      <Head title="Department of BMB" />
      <Back title="Department of Biochemistry and Molecular Biology" />

      {/* Intro Section */}
      <section className="container mx-auto px-4 py-12">
        <h3 className="text-lg font-medium text-center text-orange-500">WELCOME</h3>
        <h2 className="text-4xl py-4 text-center uppercase font-bold text-[#000F38]">Department of Biochemistry and Molecular Biology</h2>
        <p className="text-justify mt-4 text-xl font-medium text-gray-600">
          Explore the frontiers of science and discovery with the Department of Biochemistry & Molecular Biology at NIST. Our program opens doors to understanding life at the molecular level, empowering students to tackle critical challenges in health, agriculture, and environmental sustainability. Join us in building a healthier Bangladesh and a better world through rigorous research and scientific excellence. Our core values are rooted in producing skilled, compassionate graduates committed to advancing knowledge and improving lives.
        </p>
      </section>

      {/* Why Study BMB Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row">
        <div className="basis-full md:basis-[56%]">
          <h2 className="text-4xl font-bold text-[#000F38]">Why Study BMB at NIST?</h2>
          <p className="my-4 text-justify text-gray-600">
            The Biochemistry & Molecular Biology (BMB) program at NIST is dedicated to preparing a new generation of scientists equipped to address the pressing health and environmental challenges of our time, especially in the wake of the COVID-19 pandemic. This global crisis has underscored the critical importance of biochemistry in understanding, preventing, and treating infectious diseases. As Bangladesh accelerates its investment in high-tech laboratories and research infrastructure, our program aims to develop graduates who can lead scientific advancements in healthcare, pharmaceuticals, and biotechnology. Students will be prepared for impactful roles as research associates, chemical engineers, and innovators, driving solutions for global health crises and contributing to Bangladesh’s scientific growth.
          </p>
          <div className="mt-6 space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-orange-500">MISSION</h3>
              <p className="text-justify text-gray-600">
                To foster an environment of excellence in biochemistry and molecular biology education, equipping students with the theoretical knowledge and practical skills necessary to meet global scientific demands. Our mission is to empower students to pursue impactful careers in research and industry, contributing to Bangladesh’s growing prominence in biochemistry and molecular science.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-orange-500">VISION</h3>
              <p className="text-justify text-gray-600">
                To establish NIST’s Biochemistry & Molecular Biology department as a leading center for scientific discovery, innovation, and education, dedicated to producing globally competitive biochemists who will advance knowledge and improve human health and environmental sustainability.
              </p>
            </div>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/biochem/biochem-1.jpg" alt="BMB Program" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      {/* Facilities Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row-reverse">
        <div className="basis-full md:basis-[56%]">
          <h3 className="text-base font-semibold text-orange-500">WHY CHOOSE NIST?</h3>
          <h2 className="my-2.5 max-w-xl text-4xl font-bold text-[#000F38]">BMB Facilities at NIST</h2>
          <div className="mt-4 rounded-lg bg-gray-50 p-4">
            <ul className="space-y-1.5">
              {bmbFeatures.map((feature, index) => (
                <li key={index} className="border-l-4 border-[#07944a] bg-white px-2 py-1.5 text-base font-medium text-[#333] shadow-sm">
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/biochem/biochem-2.jpg" alt="NIST BMB Facilities" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      {/* Accordion Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-5xl px-4 md:w-[95%]">
          <h2 className="mb-10 text-center text-3xl font-bold uppercase text-[#000F38]">BMB Course Information</h2>
          <div className="space-y-2.5">
            {accordionData.map((item) => (
              <div key={item.id} className="overflow-hidden rounded-md bg-[#2c3e50] text-white">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="flex w-full cursor-pointer select-none items-center justify-between p-4 text-left text-lg font-medium transition-colors hover:bg-[#34495e] md:text-base"
                  aria-expanded={activeIndex === item.id}
                >
                  <span>{item.title}</span>
                  <span>{activeIndex === item.id ? <ChevronDown /> : <ChevronRight />}</span>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${activeIndex === item.id ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="bg-[#ecf0f1] p-4 text-[#2c3e50]">
                    {item.content}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default CourseBmbPage;