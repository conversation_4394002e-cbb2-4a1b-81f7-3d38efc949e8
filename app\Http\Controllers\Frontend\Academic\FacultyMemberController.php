<?php

namespace App\Http\Controllers\Frontend\Academic;

use App\Http\Controllers\Controller;
use App\Models\FacultyInfo;
use App\Http\Resources\FacultyInfoResource;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FacultyMemberController extends Controller
{
    public function index(Request $request)
    {
        // Get faculty members with active status, ordered by created time (old first)
        // Principal designated person will be on top
        $facultyMembers = FacultyInfo::where('active_status', 'active')
            ->orderByRaw("CASE WHEN LOWER(designation) LIKE '%principal%' THEN 0 ELSE 1 END")
            ->orderBy('created_at', 'asc')
            ->get();

        return Inertia::render('public/academic/faculty-member/index', [
            'facultyMembers' => FacultyInfoResource::collection($facultyMembers),
        ]);
    }

    public function show(string $id, string $slug)
    {
        $facultyMember = FacultyInfo::where('id', $id)
            ->where('slug', $slug)
            ->where('active_status', 'active')
            ->first();

        if (!$facultyMember) {
            abort(404, 'Faculty member not found');
        }

        return Inertia::render('public/academic/faculty-member/show', [
            'facultyMember' => new FacultyInfoResource($facultyMember),
        ]);
    }
}
