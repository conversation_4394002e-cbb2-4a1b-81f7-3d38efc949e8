import React from "react";
import { usePage, <PERSON> } from '@inertiajs/react';

const Back = ({ title }) => {
  // Use the usePage hook from Inertia to get the current URL
  const { url } = usePage();
  const currentPath = url.split("/")[1]; // Extracts the first segment of the path

  return (
    <>
      {/*
        The outer div combines the background image, padding, and text color styles.
        NOTE: The background image URL is hardcoded here. For dynamic images,
        you might pass it as a prop and use an inline style.
      */}
      <div 
        className="bg-[url('/assets/entrybanner.jpg')] bg-cover bg-center bg-no-repeat py-24 px-4 text-white"
      >
        <div className="mx-auto max-w-[85%]">
          <h2 className="text-[17px] font-medium uppercase">
            {/* Inertia link to the home page */}
            <Link href="/" className="hover:text-gray-300">Home</Link> / {currentPath}
          </h2>
          <h1 className="break-words text-4xl font-semibold leading-[1.1]">
            {title}
          </h1>
        </div>
      </div>
    </>
  );
};

export default Back;












