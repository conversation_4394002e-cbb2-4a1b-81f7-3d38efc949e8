import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { ClassSchedule, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Edit Class Schedule',
        href: '/schedule/class/edit',
    },
];

export default function ClassScheduleEdit({ currentClassSchedule }: { currentClassSchedule: ClassSchedule }) {
    const [department, setDepartment] = useState<string>(currentClassSchedule.department);
    const [session, setSession] = useState<string>(currentClassSchedule.session);
    const [batch, setBatch] = useState<string>(currentClassSchedule.batch);
    const [scheduleEmbedLink, setScheduleEmbedLink] = useState<string | null>(currentClassSchedule.scheduleEmbedLink);
    const { errors } = usePage().props;

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.schedule.class.update', currentClassSchedule.id), {
            _method: 'put',
            department,
            session,
            batch,
            scheduleEmbedLink,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Class Schedule" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Class Schedule</div>

                        <Button>
                            <Link href={route('admin.schedule.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="department">Department</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department"
                                            value={department}
                                            onChange={(e) => setDepartment(e.target.value)}
                                            aria-invalid={!!errors.department}
                                        />
                                        <InputError message={errors.department} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session">Session</Label>
                                        <Input
                                            type="text"
                                            id="session"
                                            placeholder="Session"
                                            value={session}
                                            onChange={(e) => setSession(e.target.value)}
                                            aria-invalid={!!errors.session}
                                        />
                                        <InputError message={errors.session} />
                                    </div>

                                    <div>
                                        <Label htmlFor="batch">Batch</Label>
                                        <Input
                                            type="text"
                                            id="batch"
                                            placeholder="Batch"
                                            value={batch}
                                            onChange={(e) => setBatch(e.target.value)}
                                            aria-invalid={!!errors.batch}
                                        />
                                        <InputError message={errors.batch} />
                                    </div>
                                </div>
                                <div className='mt-4'>
                                    <Label htmlFor="scheduleEmbedLink">Schedule Embed Link</Label>
                                    <Input
                                        type="text"
                                        id="scheduleEmbedLink"
                                        placeholder="Schedule Embed Link"
                                        value={scheduleEmbedLink ?? ''}
                                        onChange={(e) => setScheduleEmbedLink(e.target.value)}
                                        aria-invalid={!!errors.scheduleEmbedLink}
                                    />
                                    <InputError message={errors.scheduleEmbedLink} />
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Class Schedule</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
