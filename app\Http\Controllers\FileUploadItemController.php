<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileUploadItemController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:all_activity,events,cse,ece,bba,bmb,excellent_results',
            'file' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        $filePath = null;
        if ($request->hasFile('file')) {
            $filePath = storeSanitizedFile($request->file('file'), 'uploads/file-uploads');
        }

        FileUpload::create([
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'file' => $filePath,
        ]);

        return redirect()->back()->with('success', 'File uploaded successfully.');
    }

    public function update(Request $request, FileUpload $fileUpload)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:all_activity,events,cse,ece,bba,bmb,excellent_results',
            'file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        $data = [
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
        ];

        if ($request->hasFile('file')) {
            // Delete old file
            if ($fileUpload->file) {
                Storage::disk('public')->delete($fileUpload->file);
            }
            $data['file'] = storeSanitizedFile($request->file('file'), 'uploads/file-uploads');
        }

        $fileUpload->update($data);

        return redirect()->back()->with('success', 'File updated successfully.');
    }

    public function destroy(FileUpload $fileUpload)
    {
        // Delete file
        if ($fileUpload->file) {
            Storage::disk('public')->delete($fileUpload->file);
        }

        $fileUpload->delete();

        return redirect()->back()->with('success', 'File deleted successfully.');
    }
}
