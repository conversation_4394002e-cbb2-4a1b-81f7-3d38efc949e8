import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import HomePage from './public/home/<USER>';
import { News, Noticeboard, ImageSliderData, GalleryData } from '@/types';

interface AdmissionModalData {
    id: number;
    campaign_name: string | null;
    title: string | null;
    active_status: 'active' | 'inactive';
    published_date: string;
    image: string;
}

interface HomepageAdData {
    id: number;
    ad_title: string | null;
    program_info_title: string | null;
    session: string | null;
    offer_title: string | null;
    offer_text: string | null;
    active_status: 'active' | 'inactive';
}

export default function Welcome({
    blogs,
    notices,
    imageSliders,
    galleryImages,
    admissionModal,
    homepageAd
}: {
    blogs: News[];
    notices: Noticeboard[];
    imageSliders: ImageSliderData[];
    galleryImages: GalleryData[];
    admissionModal: AdmissionModalData | null;
    homepageAd: HomepageAdData | null;
}) {
    const { auth } = usePage<SharedData>().props;

    const news: News[] = blogs;
    const noticeboard: Noticeboard[] = notices;
    const imageSlider: ImageSliderData[] = imageSliders;
    const galleryImage: GalleryData[] = galleryImages;
    

    return (
        <>
            <Head title="National Institute of Science & Technology">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
           {/* <div className="flex min-h-screen flex-col items-center bg-[#FDFDFC] p-6 text-[#1b1b18] lg:justify-center lg:p-8 dark:bg-[#0a0a0a]">
                <header className="mb-6 w-full max-w-[335px] text-sm not-has-[nav]:hidden lg:max-w-4xl">
                    <nav className="flex items-center justify-end gap-4">
                        {auth.user ? (
                            <Link
                                href={route('admin.dashboard')}
                                className="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                            >
                                Dashboard
                            </Link>
                        ) : (
                            <>
                                <Link
                                    href={route('login')}
                                    className="inline-block rounded-sm border border-transparent px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#19140035] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                                >
                                    Log in
                                </Link>
                                <Link
                                    href={route('register')}
                                    className="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                                >
                                    Register
                                </Link>
                            </>
                        )}
                    </nav>
                </header>

                <div>
                    <h1>Welcome to Our Website</h1>

                    <section>
                        <h2>Latest Blogs</h2>
                        <ul>
                            {blogs.map((blog) => (
                                <li key={blog.id}>{blog.title}</li>
                            ))}
                        </ul>
                    </section>

                    <section>
                        <h2>Latest Notices</h2>
                        <ul>
                            {notices.map((notice) => (
                                <li key={notice.id}>{notice.title}</li>
                            ))}
                        </ul>
                    </section>
                </div>

                <div className="hidden h-14.5 lg:block"></div>
            </div>*/}
            <HomePage
                blogs={news}
                notices={noticeboard}
                imageSliders={imageSlider}
                galleryImages={galleryImage}
                admissionModal={admissionModal}
                homepageAd={homepageAd}
            />
        </>
    );
}
