import { Award, BookCheck, Briefcase, Users, Sparkles, Target, ScrollText  } from 'lucide-react';
import React from 'react';

import { Link } from '@inertiajs/react';

// Define the data types for type safety
interface AboutCardItem {
    icon: React.ElementType;
    title: string;
    description: string;
    borderColor: string;
    bgGradient: string;
    titleColor: string;
}

interface AwardItem {
    icon: React.ElementType;
    count: string;
    title: string;
}

// Data for the About, Mission, and Vision cards
const aboutCardsData: AboutCardItem[] = [
    {
        icon: ScrollText,
        title: 'About Us',
        description: 'National Institute of Science & Technology is a concern by Daffodil Group, affiliated with the national university brings the best degrees, BSc Hons.(Professional) in BBA, CSE, ECE, and BSc Hons. in Biochemistry and Molecular Biology with the help of the brightest minds from home and abroad for greater job placement and career opportunists.',
        borderColor: 'border-green-500',
        bgGradient: 'bg-gradient-to-r from-green-100 to-green-50',
        titleColor: 'text-green-700'
    },
    {
        icon: Target,
        title: 'Our Mission',
        description: 'The National Institute of Science and Technology is committed to implementing the most effective teaching-learning approaches. One of our primary missions is to guarantee that the natural environment is protected, preserved, and maintained. And provide education at an affordable cost.',
        borderColor: 'border-green-500',
        bgGradient: 'bg-gradient-to-r from-green-100 to-green-50',
        titleColor: 'text-green-700'
    },
    {
        icon: Sparkles,
        title: 'Our Vision',
        description: 'Our main goal is to reawaken the thrill of discovery, learn from it, and gain international reputation by focusing on our capabilities. We also have the ambition to engage with relevant scientific research institutions, business participants, and the general public.',
        borderColor: 'border-green-500',
        bgGradient: 'bg-gradient-to-r from-green-100 to-green-50',
        titleColor: 'text-green-700'
    }
];

// Data for the awards/stats wrapper section (commented out but kept for reference)
const awardsData: AwardItem[] = [
    { icon: Award, count: '3,000+', title: 'SUCCESS STORIES' },
    { icon: Users, count: '500+', title: 'TRUSTED TUTORS' },
    { icon: BookCheck, count: '1,000+', title: 'SCHEDULES' },
    { icon: Briefcase, count: '50+', title: 'COURSES' },
];

const AboutSection = () => {
    return (
        <>
            {/* About Us Section */}

            <section className="overflow-hidden bg-white py-16 sm:py-24">
                <div className="mx-auto max-w-7xl px-6 lg:px-8">
                    <div className="mx-auto grid max-w-2xl grid-cols-1 gap-x-6 gap-y-12 lg:mx-0 lg:max-w-none lg:grid-cols-2">
                        {/* Left Section */}
                        <div className="lg:pt-4 lg:pr-8">
                            {/* Subheading */}
                            <h3 className="text-sm font-semibold tracking-wide text-green-600 uppercase">What We Offer</h3>
                            {/* Heading */}
                            <h2 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                                National Institute of Science & Technology (NIST)
                            </h2>

                            {/* Dynamic Cards from Data Array */}
                            <div className="mt-8 space-y-6">
                                {aboutCardsData.map((card, index) => {
                                    const Icon = card.icon;
                                    return (
                                        <div
                                            key={index}
                                            className={`transform rounded-2xl border ${card.borderColor} ${card.bgGradient} p-6 shadow-lg transition hover:-translate-y-1 hover:shadow-xl`}
                                        >
                                            <div className="flex items-center gap-3">
                                                <Icon className={`h-6 w-6 ${card.titleColor}`} />
                                                <h3 className={`text-xl font-semibold ${card.titleColor}`}>
                                                    {card.title}
                                                </h3>
                                            </div>
                                            <p className="mt-2 text-justify text-sm leading-6 text-gray-700">
                                                {card.description}
                                            </p>
                                        </div>
                                    );
                                })}
                            </div>

                            {/* Buttons */}
                            <div className="mt-10 flex flex-wrap gap-4">
                                <Link
                                    href="/about-us"
                                    className="rounded-md bg-green-600 px-5 py-3 text-sm font-semibold text-white shadow-md hover:bg-green-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
                                >
                                    Learn More
                                </Link>
                                <Link
                                    href="/admission-info"
                                    className="rounded-md border border-green-600 px-5 py-3 text-sm font-semibold text-green-700 shadow-sm hover:bg-green-50"
                                >
                                    Admission Info
                                </Link>
                            </div>
                        </div>

                        {/* Right Image Section */}
                        {/* Right Image Section */}
                        <div className="relative flex items-center">
                            <div className="md:h-[600px] sm:h-[300px] w-full overflow-hidden rounded-sm shadow-xl ring-1 ring-gray-400/10">
                                <img
                                    src="/images/nist-building.jpg"
                                    alt="NIST Campus"
                                    className="h-full w-full object-cover object-center transition-transform duration-500 hover:scale-105"
                                />
                            </div>
                        </div>

                        
                    </div>
                </div>
            </section>

            {/* Awards/Stats Wrapper Section */}
            {/*<section className="bg-[#0a4727] py-12">
        <div className="container mx-auto flex flex-wrap justify-around gap-5">
          {awardsData.map((item) => {
            const Icon = item.icon;
            return (
              <div key={item.title} className="flex-1 basis-full p-4 text-center text-white sm:basis-[48%] xl:basis-[22%]">
                <div className="mb-5 flex justify-center">
                  <Icon size={60} />
                </div>
                <div className="text">
                  <h1 className="text-4xl font-bold sm:text-3xl xl:text-5xl">{item.count}</h1>
                  <h3 className="mt-2.5 text-base font-normal uppercase sm:text-sm">{item.title}</h3>
                </div>
              </div>
            );
          })}
        </div>
      </section>*/}
        </>
    );
};

export default AboutSection;
