import { type PaginatedData, type Role, type User } from '@/types';
import { router, useForm, usePage } from '@inertiajs/react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

// Shadcn/UI Components
import Pagination from '@/components/pagination';
import { Input } from '@/components/ui/input';

// Icons
import { CustomModalForm } from '@/components/custom-modal-form';
import { CustomTable } from '@/components/custom-table';
import { UsersModalFormConfig } from '@/config/forms/users-modal-form';
import { UsersTableConfig } from '@/config/tables/users-table';
import debounce from 'lodash/debounce';
import { Search } from 'lucide-react';

// --- TYPE DEFINITIONS ---

interface Flash {
    success?: string;
    danger?: string;
}

interface UsersPageProps {
    users: PaginatedData<User>;
    roles: PaginatedData<Role>;
    filters: {
        search?: string;
    };
}

interface LinkProps {
    active: boolean;
    label: string;
    url: string;
}



interface UserPagination {
    data: User[];
    links: LinkProps[];
    from: number;
    to: number;
    total: number;
}

interface FilterProps {
    search: string;
    perPage: string;
}

interface FlashProps extends Record<string, any> {
    flash?: {
        success?: string;
        error?: string;
    };
}

interface IndexProps {
    users: UserPagination;
    filters: FilterProps;
    totalCount: number;
    filteredCount: number;
}

// Breadcrumbs for the AppLayout

export default function UsersIndex({ users, roles, filters }: UsersPageProps) {
    const { flash } = usePage<{ flash: Flash }>().props;

    const [modalOpen, setModalOpen] = useState(false);
    const [mode, setMode] = useState<'create' | 'view' | 'edit'>('create');
    const [selectedCategory, setSelectedCategory] = useState<any>(null);

   

    const { data, setData, errors, processing, reset, post } = useForm<{
        name: string;
        email: string;
        password: string;
        confirm_password: string;
        roles: string;
        _method: string;
    }>({
        name: '',
        email: '',
        password: '',
        confirm_password: '',
        roles: '',
        _method: 'POST',
    });

    // Handle Delete
    const handleDelete = (route: string) => {
        if (confirm('Are you sure, you want to delete?')) {
            router.delete(route, {
                preserveScroll: true,
                onSuccess: (response: { props: FlashProps }) => {
                    const successMessage = response.props.flash?.success;

                    successMessage && toast.success(successMessage);
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message;
                    errorMessage && toast.error(errorMessage);
                    closeModal();
                },
            });
        }
    };

    // Handle Submit
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Edit mode
        if (mode === 'edit' && selectedCategory) {
            data._method = 'PUT';

            post(route('admin.acl.users.update', selectedCategory.id), {
                forceFormData: true,
                onSuccess: (response: { props: FlashProps }) => {
                    const successMessage = response.props.flash?.success;
                    if (successMessage) {
                        toast.success(successMessage);
                    }
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message;
                    if (errorMessage) {
                        toast.error(errorMessage);
                    }
                },
            });
        } else {
            post(route('admin.acl.users.store'), {
                onSuccess: (response: { props: FlashProps }) => {
                    const successMessage = response.props.flash?.success;
                    if (successMessage) {
                        toast.success(successMessage);
                    }
                    closeModal();
                },
                onError: (error: Record<string, string>) => {
                    const errorMessage = error?.message;
                    if (errorMessage) {
                        toast.error(errorMessage);
                    }
                },
            });
        }
    };

    // Closing modal
    const closeModal = () => {
        setMode('create');
        setSelectedCategory(null);
        reset();
        setModalOpen(false);
    };

    // Handle Modal Toggle
    const handleModalToggle = (open: boolean) => {
        setModalOpen(open);

        if (!open) {
            setMode('create');
            setSelectedCategory(null);
            reset();
        }
    };

    // Open Modal
    const openModal = (mode: 'create' | 'view' | 'edit', category?: any) => {
        setMode(mode);

        if (category) {
            Object.entries(category).forEach(([key, value]) => {
                if (key === 'roles' && Array.isArray(value)) {
                    setData('roles', value[0]?.name);
                } else {
                    setData(key as keyof typeof data, (value as string | null) ?? '');
                }
            });

            // Setting image preview
            setSelectedCategory(category);
        } else {
            reset();
        }

        setModalOpen(true);
    };

    // Effect for showing toast notifications for success/error messages
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    // --- SEARCH FUNCTIONALITY ---

    // Debounced search handler
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.access-control.index'), { search: query }, { preserveState: true, replace: true });
        }, 300),
    ).current;

    // Cleanup debounce on unmount
    useEffect(() => {
        return () => {
            handleSearch.cancel();
        };
    }, [handleSearch]);

    return (
        <>
            <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
                <div className="mb-4 flex items-center justify-between gap-4">
                    <div className="relative w-full max-w-sm">
                        <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="search"
                            placeholder="Search by name, email..."
                            className="pl-8"
                            defaultValue={filters.search}
                            onChange={(e) => handleSearch(e.target.value)}
                        />
                    </div>
                    {/* Custom Modal Form Component */}
                    <div className="ml-auto">
                        <CustomModalForm
                            addButton={UsersModalFormConfig.addButton}
                            title={mode === 'view' ? 'View User' : mode === 'edit' ? 'Update User' : UsersModalFormConfig.title}
                            description={UsersModalFormConfig.description}
                            fields={UsersModalFormConfig.fields}
                            buttons={UsersModalFormConfig.buttons}
                            data={data}
                            setData={setData}
                            errors={errors}
                            processing={processing}
                            handleSubmit={handleSubmit}
                            open={modalOpen}
                            onOpenChange={handleModalToggle}
                            mode={mode}
                            extraData={roles}
                        />
                    </div>
                </div>

                <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                    

                    {/* Custom Table component */}
                    <CustomTable
                        columns={UsersTableConfig.columns}
                        actions={UsersTableConfig.actions}
                        data={users.data}
                        from={users.meta.from}
                        onDelete={handleDelete}
                        onView={(category) => openModal('view', category)}
                        onEdit={(category) => openModal('edit', category)}
                        isModal={true}
                    />
                </div>

                <div className="mt-4">
                    <Pagination meta={users.meta} />
                </div>
            </div>
        </>
    );
}
