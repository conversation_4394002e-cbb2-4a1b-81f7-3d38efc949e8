<?php

namespace App\Http\Controllers;

use App\Models\ImageSlider;
use App\Http\Resources\ImageSliderResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ImageSliderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
  
    /**
     * Show the form for creating a new resource.
     */
   
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,jpg,png,webp|max:2048',
            'heading' => 'required|string|max:255',
            'caption' => 'required|string|max:500',
            'order' => 'required|integer|min:1',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = storeSanitizedFile($request->file('image'), 'uploads/image-sliders');
        }

        ImageSlider::create([
            'title' => $request->title,
            'image' => $imagePath,
            'heading' => $request->heading,
            'caption' => $request->caption,
            'order' => $request->order,
        ]);

        return redirect()->back()->with('success', 'Image slider created successfully.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ImageSlider $imageSlider)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,webp|max:2048',
            'heading' => 'required|string|max:255',
            'caption' => 'required|string|max:500',
            'order' => 'required|integer|min:1',
        ]);

        $data = [
            'title' => $request->title,
            'heading' => $request->heading,
            'caption' => $request->caption,
            'order' => $request->order,
        ];

        // Handle image upload if provided
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($imageSlider->image) {
                Storage::disk('public')->delete($imageSlider->image);
            }
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/image-sliders');
        }

        $imageSlider->update($data);

        return redirect()->back()->with('success', 'Image slider updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ImageSlider $imageSlider)
    {
        // Delete the image file if exists
        if ($imageSlider->image) {
            Storage::disk('public')->delete($imageSlider->image);
        }

        $imageSlider->delete();

        return redirect()->back()->with('success', 'Image slider deleted successfully.');
    }
}
