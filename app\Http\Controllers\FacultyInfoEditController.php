<?php

namespace App\Http\Controllers;

use App\Http\Resources\FacultyInfoResource;
use App\Models\FacultyInfo;
use Inertia\Inertia;
use Illuminate\Support\Str;

class FacultyInfoEditController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(FacultyInfo $faculty)
    {
        $draftToken = Str::uuid()->toString();
        return Inertia::render('admin/faculty/edit', [
            'draftToken' => $draftToken,
            'currentFaculty' => new FacultyInfoResource($faculty),
        ]);
    }
}
