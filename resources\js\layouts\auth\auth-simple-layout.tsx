import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';
import NISTLogo from '../../components/text-editor/NIST.png';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-[#eeeeee] p-6 md:p-10">
            <div className="flex w-full max-w-6xl overflow-hidden rounded-lg shadow-lg md:flex-row">
                {/* Left section with purple gradient - Desktop only */}
                <div className="hidden w-1/2 flex-col items-center justify-center bg-gradient-to-br from-green-700 to-green-900 p-8 text-white md:flex">
                    <div className="flex flex-col items-center justify-center space-y-4">
                        <img src={NISTLogo} alt="NIST Logo" />
                        <h1 className="text-2xl font-bold">NIST wCMS</h1>
                        <p className="text-sm font-light">v1.1</p>
                    </div>
                </div>

                {/* Right section with form content */}
                <div className="w-full bg-white p-6 md:w-1/2">
                    <div className="flex flex-col gap-6">
                        {/* Mobile logo section */}
                        <div className="hidden md:flex flex-col items-center gap-2 font-medium">
                            <Link href="/" className="">
                                <div className="mb-1 flex h-24 w-24 items-center justify-center rounded-md">
                                    <img src={NISTLogo} alt="NIST Logo" />
                                </div>
                            </Link>
                            <p className="text-center text-sm text-muted-foreground">
                                Welcome to the National Institute of Science and Technology&apos;s Web Content Management System (wCMS).
                            </p>
                        </div>

                        {/* Mobile logo section */}
                        <div className="w-full flex-row border rounded-md border-green-600 md:hidden">
                            <div className="flex items-center justify-between overflow-hidden rounded-md">
                                {/* Left part */}
                                <div className="w-1/2 flex flex-col items-center justify-center bg-gradient-to-br from-green-700 to-green-900 text-white h-full py-6">
                                    <h1 className="text-lg font-bold">NIST wCMS</h1>
                                    <p className="text-sm opacity-80">v1.1</p>
                                </div>

                                {/* Right part */}
                                <Link href="/" className="flex h-full flex-shrink-0 items-center justify-center bg-white w-1/2">
                                    <div className="flex h-20 w-20 items-center justify-center mx-auto">
                                        <img src={NISTLogo} alt="NIST Logo" className="max-h-full max-w-full" />
                                    </div>
                                </Link>
                            </div>
                        </div>

                        {/* Title and description */}
                        <div className="flex flex-col items-center gap-4">
                            <div className="text-center">
                                <h1 className="text-xl font-medium">{title}</h1>
                                <p className="text-center text-sm text-muted-foreground">{description}</p>
                            </div>
                        </div>

                        {/* Form content */}
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
