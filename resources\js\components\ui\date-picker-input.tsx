"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { format, parse, parseISO, isValid } from "date-fns"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popoverDialog"
import { cn } from "@/lib/utils"

interface DatePickerProps {
  label?: string
  value?: string // e.g. "26-Oct-2018"
  onChange?: (date: Date | undefined) => void
  required?: boolean
}

export default function DatePicker({ label, value, onChange, required }: DatePickerProps) {
  const [open, setOpen] = React.useState(false)

  // Parse incoming value string to Date
  // Handle both ISO format (from create) and dd-MMM-yyyy format (from edit)
  const parsedValue = React.useMemo(() => {
    if (!value) return undefined;

    // Try parsing as dd-MMM-yyyy format first (for edit modal)
    try {
      const parsedDate = parse(value, "dd-MMM-yyyy", new Date());
      if (isValid(parsedDate)) {
        return parsedDate;
      }
    } catch {
      // If dd-MMM-yyyy parsing fails, try ISO format
    }

    // Try parsing as ISO format (for create modal or other formats)
    try {
      const isoDate = new Date(value);
      if (isValid(isoDate)) {
        return isoDate;
      }
    } catch {
      // If both fail, return undefined
    }

    return undefined;
  }, [value])

  const formattedValue = parsedValue ? format(parsedValue, "dd-MMM-yyyy") : ""

  const handleSelect = (selectedDate: Date | undefined) => {
    onChange?.(selectedDate)
    setOpen(false)
  }

  return (
    <div className="flex flex-col gap-3">
      {label && (
        <Label htmlFor="date" className="px-1">
          {label} {required && "*"}
        </Label>
      )}
      <div className="relative flex gap-2">
        <Input
          id="date"
          value={formattedValue}
          readOnly
          placeholder="dd-MMM-yyyy"
          onClick={() => setOpen(true)}
        />
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              className="absolute top-1/2 right-2 size-6 -translate-y-1/2"
            >
              <CalendarIcon className="size-3.5" />
              <span className="sr-only">Select date</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={parsedValue}
              onSelect={handleSelect}
              captionLayout="dropdown"
            
             
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
