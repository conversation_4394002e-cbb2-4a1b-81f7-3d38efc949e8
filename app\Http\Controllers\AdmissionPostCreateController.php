<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;

class AdmissionPostCreateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $draftToken = Str::uuid()->toString();

        return Inertia::render('admin/admission/admission-post/create', [
            'draftToken' => $draftToken,
        ]);

    }
}

