import React, { useState } from 'react';
import { router, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import Pagination from '@/components/pagination';
import StatusToggle from '@/components/ui/status-toggle';
import { useConfirmation } from '@/contexts/confirmation-context';
import '@/components/minimal-tiptap/styles/index.css';

interface AdmissionPostData {
    id: number;
    title: string;
    admission_circular_content: string;
    active_status: 'unpublished' | 'published';
    published_date: string;
}

interface PaginatedData {
    data: AdmissionPostData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface AdmissionPostTabProps {
    data: PaginatedData;
}

export default function AdmissionPostTab({ data }: AdmissionPostTabProps) {
    const { showConfirmation } = useConfirmation();
    const [viewingItem, setViewingItem] = useState<AdmissionPostData | null>(null);

    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Admission Post',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.posts.destroy', id));
        }
    };

    const handleView = (item: AdmissionPostData) => {
        setViewingItem(item);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Admission Posts</h2>
                <Button asChild>
                    <Link href={route('admin.admission.posts.create')}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Admission Post
                    </Link>
                </Button>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Title</TableHead>
                                <TableHead>Published Date</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell className="font-medium">{item.title}</TableCell>
                                    <TableCell>{item.published_date}</TableCell>
                                    <TableCell>
                                        <StatusToggle
                                            id={item.id}
                                            currentStatus={item.active_status}
                                            updateRoute={route('admin.admission.posts.update', item.id)}
                                            activeLabel="Published"
                                            inactiveLabel="Unpublished"
                                            confirmTitle="Change Publication Status"
                                            confirmDescription="Are you sure you want to change the publication status of this admission post?"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" asChild>
                                                        <Link href={route('admin.admission.posts.edit', item.id)}>
                                                            <Edit className="h-4 w-4" />
                                                        </Link>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.title)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>View Admission Post</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Title</Label>
                                    <p className="text-sm font-medium">{viewingItem.title}</p>
                                </div>
                                <div>
                                    <Label>Published Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.published_date)}</p>
                                </div>
                            </div>

                            <div>
                                <Label>Status</Label>
                                <Badge variant={viewingItem.active_status === 'published' ? 'default' : 'secondary'}>
                                    {viewingItem.active_status === 'published' ? 'Published' : 'Unpublished'}
                                </Badge>
                            </div>

                            <div>
                                <Label>Content</Label>
                                <div
                                    className="mt-2 p-4 border rounded-lg bg-gray-50 ProseMirror prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-a:text-blue-600"
                                    dangerouslySetInnerHTML={{
                                        __html: viewingItem.admission_circular_content || '<p>No content available.</p>'
                                    }}
                                />
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
