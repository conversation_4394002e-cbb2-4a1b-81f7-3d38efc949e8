import { Head, useForm } from '@inertiajs/react';
import { Facebook, Linkedin, Loader2, Mail, MapPin, Phone, Youtube } from 'lucide-react';
import React from 'react';
import Back from '../common/back/Back';
// Define the type for the form data
interface FormData {
    name: string;
    email: string;
    subject: string;
    message: string;
}

const ContactPage = () => {
    const mapSrc =
        'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3651.8599868755177!2d90.3815803!3d23.7523718!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3755b9253036c2df%3A0x20114da0a21c3308!2sNational%20Institute%20of%20Science%20%26%20Technology%20(NIST)!5e0!3m2!1sen!2sbd!4v1724492721244!5m2!1sen!2sbd';

    // Inertia's useForm hook for state management, submission, and errors
    const { data, setData, post, processing, errors, recentlySuccessful } = useForm<FormData>({
        name: '',
        email: '',
        subject: '',
        message: 'Create a message here...',
    });

    const contactInfo = [
        { icon: MapPin, title: 'ADDRESS:', value: '19/1, West Panthapath, Dhaka-1205' },
        { icon: Mail, title: 'EMAIL:', value: '<EMAIL>' },
        { icon: Phone, title: 'PHONE:', value: '+8801713439163' },
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        /* post(route('contact.submit'), {
            preserveScroll: true,
            // onSuccess: () => reset(), // Uncomment to clear form on success
        });*/
    };

    return (
        <>
            <Head title="Contact Us" />
            <Back title="Contact Us" />
            <section className="py-16">
                <div className="container mx-auto flex flex-col overflow-hidden rounded-lg bg-white p-6 shadow-lg md:flex-row">
                    {/* Left Column: Map */}
                    <div className="w-full md:w-1/2">
                        <iframe src={mapSrc} className="h-full min-h-[400px] w-full border-none md:min-h-full" loading="lazy"></iframe>
                    </div>

                    {/* Right Column: Form and Info */}
                    <div className="w-full p-4 md:w-1/2 md:p-12">
                        <h1 className="text-3xl font-medium text-[#002147]">Contact Us</h1>
                        <p className="text-gray-500">We're open for any suggestion or just to have a chat</p>

                        <div className="my-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                            {contactInfo.map((item, index) => {
                                const Icon = item.icon;
                                return (
                                    <div key={index}>
                                        <h4 className="flex items-center gap-2 font-semibold text-gray-800">
                                            <Icon size={16} className="text-green-700" /> {item.title}
                                        </h4>
                                        <p className="mt-2 text-base text-gray-600">{item.value}</p>
                                    </div>
                                );
                            })}
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="flex flex-col gap-4 sm:flex-row">
                                <input
                                    type="text"
                                    placeholder="Name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="input-style flex-1"
                                />
                                <input
                                    type="email"
                                    placeholder="Email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="input-style flex-1"
                                />
                            </div>
                            <input
                                type="text"
                                placeholder="Subject"
                                value={data.subject}
                                onChange={(e) => setData('subject', e.target.value)}
                                className="input-style"
                            />
                            <textarea value={data.message} onChange={(e) => setData('message', e.target.value)} rows={6} className="input-style" />

                            {recentlySuccessful && (
                                <div className="rounded-md bg-green-100 p-3 text-center text-sm font-medium text-green-800">
                                    Message sent successfully!
                                </div>
                            )}

                            <button
                                type="submit"
                                disabled={processing}
                                className="flex w-48 items-center justify-center rounded-md bg-green-700 px-6 py-3 font-semibold text-white transition hover:bg-green-800 disabled:bg-gray-400"
                            >
                                {processing ? <Loader2 className="animate-spin" /> : 'SEND MESSAGE'}
                            </button>
                        </form>

                        <div className="mt-8">
                            <h3 className="mb-2.5 font-medium">Follow us here</h3>
                            <div className="flex space-x-3">
                                <a
                                    href="https://www.facebook.com/nist.edu"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex h-10 w-10 items-center justify-center rounded border-2 border-green-700 text-green-700 transition-colors duration-300 hover:bg-green-700 hover:text-white"
                                >
                                    <Facebook size={20} />
                                </a>
                                <a
                                    href="https://www.youtube.com/@nist156"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex h-10 w-10 items-center justify-center rounded border-2 border-green-700 text-green-700 transition-colors duration-300 hover:bg-green-700 hover:text-white"
                                >
                                    <Youtube size={20} />
                                </a>
                                <a
                                    href="https://www.linkedin.com/company/nistedubd"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex h-10 w-10 items-center justify-center rounded border-2 border-green-700 text-green-700 transition-colors duration-300 hover:bg-green-700 hover:text-white"
                                >
                                    <Linkedin size={20} />
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
};

export default ContactPage;

// Add these reusable styles to your main CSS file for consistency
// resources/css/app.css
/*
@layer components {
  .input-style {
    @apply w-full rounded border border-gray-300 p-3 outline-none focus:ring-2 focus:ring-green-500;
  }
  .social-icon {
    @apply flex h-10 w-10 items-center justify-center rounded-full bg-green-800 text-white transition-colors duration-300 hover:bg-blue-700;
  }
}
*/
