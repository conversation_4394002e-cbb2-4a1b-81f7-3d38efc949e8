<?php

namespace App\Http\Controllers;

use App\Models\FacultyInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class FacultyInfoDestroyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(FacultyInfo $faculty)
    {
        // Delete associated image if exists
        if ($faculty->image) {
            Storage::disk('public')->delete($faculty->image);
        }

        DB::transaction(function () use ($faculty) {
            // Collect related uploads
            $uploads = $faculty->editorUploads()->get();

            // Detach pivot first
            $faculty->editorUploads()->detach();

            // Delete the faculty profile itself
            $faculty->delete();

            // Cleanup orphaned uploads
            foreach ($uploads as $upload) {
                $stillUsed = $upload->admissionPosts()->exists() || $upload->news()->exists() || $upload->facultyProfiles()->exists()|| $upload->noticeboards()->exists(); // check if used by any other model
                if (!$stillUsed) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
            }
        });


        return redirect(route('admin.faculty.index'))->with('success', 'Faculty member deleted successfully');
    }

}
