<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdmissionSpotResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'campaign_name' => $this->campaign_name,
            'session' => $this->session,
            'program_name' => $this->program_name,
            'application_start_date' => $this->application_start_date?->format('Y-m-d'),
            'application_end_date' => $this->application_end_date?->format('Y-m-d'),
            'admission_test_date' => $this->admission_test_date?->format('Y-m-d'),
            'session_start_date' => $this->session_start_date?->format('Y-m-d'),
            'active_status' => $this->active_status,
            'date_created' => $this->date_created?->format('Y-m-d'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
