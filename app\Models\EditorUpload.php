<?php

// app/Models/EditorUpload.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EditorUpload extends Model
{
    protected $fillable = [
        'uploadable_type',
        'uploadable_id',
        'draft_token',
        'user_id',
        'path',
        'filename',
        'original_name',
        'mime',
        'size',
    ];

     public function admissionPosts()
    {
        return $this->morphedByMany(
            AdmissionPost::class,
            'uploadable',
            'editor_uploadables',
            'editor_upload_id',
            'uploadable_id'
        );
    }
    public function news()
    {
        return $this->morphedByMany(
            News::class,
            'uploadable',
            'editor_uploadables',
            'editor_upload_id',
            'uploadable_id'
        );
    }
    public function facultyProfiles()
    {
        return $this->morphedByMany(
            FacultyInfo::class,
            'uploadable',
            'editor_uploadables',
            'editor_upload_id',
            'uploadable_id'
        );
    }

    public function noticeboards()
    {
        return $this->morphedByMany(
            Noticeboard::class,
            'uploadable',
            'editor_uploadables',
            'editor_upload_id',
            'uploadable_id'
        );
    }
}
