<?php

namespace App\Http\Controllers\Frontend\Admission;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Resources\TuitionFeeResource;
use App\Models\TuitionFee;

class TuitionFeesController extends Controller
{
    //
    public function index()
    {
        $tuitionFees = TuitionFeeResource::collection(TuitionFee::get());
        $tuitionFeesData = $tuitionFees->map(function ($item) {
            return [
                'id' => $item->id,
                'image' =>  $item->image ? asset('storage/'.$item->image) : null,
                'program_name' => $item->program_name.' - ('.$item->department.')',
                'department' => $item->department,
            ];
        })->values()->toArray();

        return Inertia::render('public/admission/tution-fees/index', [
            'tuitionFees' => $tuitionFeesData,
        ]);
    }
}


