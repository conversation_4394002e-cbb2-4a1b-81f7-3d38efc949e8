import React from 'react';
import { Link } from '@inertiajs/react';
import { Facebook, Youtube, Linkedin, MapPin, Phone, Send } from 'lucide-react';

// Define the types for link data
interface NavLink {
  text: string;
  href: string;
  external?: boolean;
}

const Footer = () => {
  const exploreLinks: NavLink[] = [
    { text: 'About Us', href: '/about-us' },
    { text: 'Courses', href: '/courses' },
    { text: 'News & Events', href: '/news' },
    { text: 'Contact Us', href: '/contact' },
    { text: 'Daffodil Family', href: 'https://daffodil.family', external: true },
  ];
  
  return (
    <>
      <footer className="bg-[#0a4727] p-8 text-gray-300 md:p-10">
        {/* Switched to a responsive grid layout */}
        <div className="container mx-auto max-w-5xl grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          
          {/* Column 1: Logo and About Section */}
          <div className="text-center lg:text-left">
            <div className="mb-4">
              <img src='/images/NIST-logo.png' alt="NIST Logo" className="mx-auto h-20 lg:mx-0" />
            </div>
            <span className="text-sm font-semibold text-white">AFFILIATED WITH THE NATIONAL UNIVERSITY</span>
            <p className="my-4 text-justify text-[15px] font-normal leading-6 text-gray-300">
              National Institute of Science & Technology brings the best degrees in BBA, CSE, ECE, and Biochemistry with the help of the brightest minds for greater job placement.
            </p>
            <div className="mt-4 flex justify-center space-x-3 lg:justify-start">
              <a href="https://www.facebook.com/nist.edu" target="_blank" rel="noopener noreferrer" className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white transition-colors duration-300 hover:bg-blue-600">
                <Facebook size={20} />
              </a>
              <a href="https://www.youtube.com/@nist156" target="_blank" rel="noopener noreferrer" className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white transition-colors duration-300 hover:bg-red-600">
                <Youtube size={20} />
              </a>
              <a href="https://www.linkedin.com/company/nistedubd" target="_blank" rel="noopener noreferrer" className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white transition-colors duration-300 hover:bg-blue-700">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Column 2: Explore Links Section */}
          <div>
            <h3 className="mb-5 text-xl font-bold text-white">Explore</h3>
            <ul className="space-y-3">
              {exploreLinks.map((link) => (
                <li key={link.text} className="flex items-center text-base transition-colors hover:text-white">
                  <span className="mr-2 text-white">→</span>
                  {link.external ? (
                    <a href={link.href} target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white">{link.text}</a>
                  ) : (
                    <Link href={link.href} className="text-gray-300 hover:text-white">{link.text}</Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
          
          {/* Column 3: Duplicate Explore Links Section (as in original code) */}
          <div>
            <h3 className="mb-5 text-xl font-bold text-white">Quick Links</h3>
            <ul className="space-y-3">
              {exploreLinks.map((link) => (
                <li key={link.text} className="flex items-center text-base transition-colors hover:text-white">
                  <span className="mr-2 text-white">→</span>
                  {link.external ? (
                    <a href={link.href} target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-white">{link.text}</a>
                  ) : (
                    <Link href={link.href} className="text-gray-300 hover:text-white">{link.text}</Link>
                  )}
                </li>
              ))}
            </ul>
          </div>

          {/* Column 4: Contact Us Section */}
          <div>
            <h3 className="mb-5 text-xl font-bold text-white">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin size={20} className="mr-3 mt-1 w-5 shrink-0 text-gray-300" />
                <span>19/1, West Panthapath, Dhaka-1205</span>
              </li>
              <li className="flex items-center">
                <Phone size={20} className="mr-3 w-5 shrink-0 text-gray-300" />
                <span>+8801713439163</span>
              </li>
              <li className="flex items-center">
                <Phone size={20} className="mr-3 w-5 shrink-0 text-gray-300" />
                <span>+8801811458853</span>
              </li>
              <li className="flex items-center">
                <Send size={20} className="mr-3 w-5 shrink-0 text-gray-300" />
                <span><EMAIL></span>
              </li>
            </ul>
          </div>

        </div>
      </footer>
      <div className="bg-[#06311b] py-5 text-center text-gray-400">
        <p className="text-[15px]">
          Copyright ©{new Date().getFullYear()} All rights reserved | Developed by NIST Dev Team
        </p>
      </div>
    </>
  );
};

export default Footer;