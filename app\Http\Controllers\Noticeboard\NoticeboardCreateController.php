<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NoticeboardCreateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {

        $draftToken = Str::uuid()->toString();
        return inertia('admin/noticeboard/create', [
            'draftToken' => $draftToken,
        ]);
    }
}
