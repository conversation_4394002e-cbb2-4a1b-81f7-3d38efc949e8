<?php

namespace App\Http\Controllers;

use App\Models\AdmissionSpot;
use App\Models\TuitionFee;
use App\Models\AdmissionPost;
use App\Models\AdmissionPromoImage;
use App\Models\HomepageAd;
use App\Models\AdmissionModal;
use App\Http\Resources\AdmissionSpotResource;
use App\Http\Resources\TuitionFeeResource;
use App\Http\Resources\AdmissionPostResource;
use App\Http\Resources\AdmissionPromoImageResource;
use App\Http\Resources\HomepageAdResource;
use App\Http\Resources\AdmissionModalResource;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdmissionController extends Controller
{
    public function index(Request $request)
    {
        // Get data for all tabs
        $admissionSpots = AdmissionSpotResource::collection(
            AdmissionSpot::latest()->paginate(5, ['*'], 'admission_spots_page')->withQueryString()
        );

        $tuitionFees = TuitionFeeResource::collection(
            TuitionFee::latest()->paginate(5, ['*'], 'tuition_fees_page')->withQueryString()
        );

        $admissionPosts = AdmissionPostResource::collection(
            AdmissionPost::latest()->paginate(5, ['*'], 'admission_posts_page')->withQueryString()
        );

        $admissionPromoImages = AdmissionPromoImageResource::collection(
            AdmissionPromoImage::latest()->paginate(5, ['*'], 'admission_promo_images_page')->withQueryString()
        );

        $homepageAds = HomepageAdResource::collection(
            HomepageAd::latest()->paginate(5, ['*'], 'homepage_ads_page')->withQueryString()
        );

        $admissionModals = AdmissionModalResource::collection(
            AdmissionModal::latest()->paginate(5, ['*'], 'admission_modals_page')->withQueryString()
        );

        return Inertia::render('admin/admission/index', [
            'admissionSpots' => $admissionSpots,
            'tuitionFees' => $tuitionFees,
            'admissionPosts' => $admissionPosts,
            'admissionPromoImages' => $admissionPromoImages,
            'homepageAds' => $homepageAds,
            'admissionModals' => $admissionModals,
            'activeTab' => $request->input('tab', 'admission_spot'),
        ]);
    }
}
