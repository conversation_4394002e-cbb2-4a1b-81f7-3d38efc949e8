<?php

namespace App\Http\Controllers;

use App\Models\TuitionFee;
use App\Http\Resources\TuitionFeeResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TuitionFeeController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'image' => 'required|image|max:2048',
            'program_name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
        ]);

        if ($request->hasFile('image')) {
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/tuition-fees');
        }

        TuitionFee::create($data);

        return redirect(route('admin.admission.index', ['tab' => 'tuition_fees']))
            ->with('success', 'Tuition fee created successfully');
    }

    public function update(Request $request, TuitionFee $tuitionFee)
    {
        $data = $request->validate([
            'image' => 'nullable|image|max:2048',
            'program_name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
        ]);

        $data['image'] = $tuitionFee->image;

        if ($request->hasFile('image')) {
            if ($tuitionFee->image) {
                Storage::disk('public')->delete($tuitionFee->image);
            }
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/tuition-fees');
        }

        $tuitionFee->update($data);

        return redirect(route('admin.admission.index', ['tab' => 'tuition_fees']))
            ->with('success', 'Tuition fee updated successfully');
    }

    public function destroy(TuitionFee $tuitionFee)
    {
        if ($tuitionFee->image) {
            Storage::disk('public')->delete($tuitionFee->image);
        }

        $tuitionFee->delete();

        return redirect(route('admin.admission.index', ['tab' => 'tuition_fees']))
            ->with('success', 'Tuition fee deleted successfully');
    }
}
