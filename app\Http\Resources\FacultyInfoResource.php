<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FacultyInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "slug" => $this->slug,
            "designation" => $this->designation,
            "department" => $this->department,
            "image" => $this->image ? asset('storage/'.$this->image) : null,
            "bio" => $this->bio,
            "about" => $this->about,
            "education" => $this->education,
            "research" => $this->research,
            "interests" => $this->interests,
            "official_email" => $this->official_email,
            "secondary_email" => $this->secondary_email,
            "primary_phone" => $this->primary_phone,
            "secondary_phone" => $this->secondary_phone,
            "created_at" => $this->created_at,
            "updated_at" => $this->updated_at,
            "active_status" => $this->active_status,
        
        ];
    }
}
