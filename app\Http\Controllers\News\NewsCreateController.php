<?php

namespace App\Http\Controllers\News;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsCreateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $draftToken = Str::uuid()->toString();
        
        return inertia('admin/news/create',[
            'draftToken' => $draftToken,
        ]);
    }
}