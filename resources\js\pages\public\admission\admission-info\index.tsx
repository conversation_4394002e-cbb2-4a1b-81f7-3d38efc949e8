import React from 'react';
import { Head } from '@inertiajs/react';
import Back from '../../common/back/Back';

import AdmissionHeroBanners from '@/components/admission/admission-hero-banner';
import AdmissionDeadlines from '@/components/admission/admission-deadlines';
import AdmissionPost from '@/components/admission/admission-post';
import EligibilityAccordion from '@/components/admission/admission-eligibilty';
import ApplicationProcedure from '@/components/admission/application-procedure';
import TuitionFees from '@/components/admission/tution-fees';

interface AdmissionPromoImageData {
  id: number;
  left_banner_image: string;
  right_banner_image_top: string;
  right_banner_image_top_caption: string | null;
  right_banner_image_top_subtitle: string | null;
  right_banner_image_bottom: string;
  right_banner_image_bottom_caption: string | null;
  right_banner_image_bottom_subtitle: string | null;
  status: string;
}

interface AdmissionSpotData {
  id: number;
  campaign_name: string;
  session: string;
  program_name: string;
  application_start_date: string;
  application_end_date: string;
  admission_test_date: string | null;
  session_start_date: string | null;
  active_status: string;
  date_created: string;
}

interface AdmissionPostData {
  id: number;
  title: string;
  admission_circular_content: string;
  active_status: string;
  published_date: string;
}

interface AdmissionInfoPageProps {
  admissionPromoImage?: AdmissionPromoImageData | null;
  admissionSpot?: AdmissionSpotData | null;
  admissionPost?: AdmissionPostData | null;
}

const AdmissionInfoPage = ({ admissionPromoImage, admissionSpot, admissionPost }: AdmissionInfoPageProps) => {
  return (
    <>
      <Head title="Admission Information" />
      <Back title='Admission Information' />
      <div className="bg-white py-10">

          <AdmissionHeroBanners admissionPromoImage={admissionPromoImage} />
          <AdmissionDeadlines admissionSpot={admissionSpot} />
          <AdmissionPost admissionPost={admissionPost} />
          <EligibilityAccordion />
          <ApplicationProcedure />
          <TuitionFees />

      </div>
    </>
  );
};

// Assign the main layout


export default AdmissionInfoPage;