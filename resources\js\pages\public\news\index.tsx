import { News } from '@/types';
import { <PERSON>, <PERSON> } from '@inertiajs/react'; // Using Inertia's Link for SPA navigation
import { CalendarDays, User } from 'lucide-react';
import Back from '../common/back/Back';
import Pagination from '@/components/pagination';

// Define the type for a blog item for type safety

interface PaginatedNewsData {
    data: News[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
}

const NewsPage = ({ news }: { news: PaginatedNewsData }) => {
    return (
        <>
            <Head title="News & Events" />
            <Back title="Latest News & Event" />

            <section className="bg-gray-50 pt-24 pb-12">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {news.data.map((val) => (
                            <div key={val.id} className="group overflow-hidden rounded-lg bg-white shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-2">
                                <div className="h-64 overflow-hidden">
                                    <img 
                                        src={val.image} 
                                        alt={val.title} 
                                        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105" 
                                    />
                                </div>
                                <div className="p-8">
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="flex items-center gap-2 font-medium text-green-600 uppercase">
                                            <User size={16} />
                                            <label className="text-gray-500">{'NIST'}</label>
                                        </span>
                                        <span className="flex items-center gap-2 font-medium text-green-600 uppercase">
                                            <CalendarDays size={16} />
                                            <label className="text-gray-500">{val.created_at}</label>
                                        </span>
                                    </div>

                                    <Link href={`/news/${val.id}/${val.slug}`}>
                                        <h1 className="my-5 cursor-pointer text-xl leading-tight font-semibold text-[#001234] transition-colors duration-300 hover:text-green-600">
                                            {val.title}
                                        </h1>
                                    </Link>

                                    <p className="text-gray-600">{val.excerpt?(val.excerpt.substring(0, 100)):'Read More '}...</p>
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-8 mb-8">
                        <Pagination meta={news.meta} />
                    </div>
                </div>
            </section>
        </>
    );
};

export default NewsPage;
