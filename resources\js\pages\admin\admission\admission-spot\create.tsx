import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admission Management',
        href: '/admin/admission',
    },
    {
        title: 'Add Admission Spot',
        href: '/admin/admission/spots/create',
    },
];

export default function AdmissionSpotCreate() {
    const [applicationStartDate, setApplicationStartDate] = useState<Date>();
    const [applicationEndDate, setApplicationEndDate] = useState<Date>();
    const [admissionTestDate, setAdmissionTestDate] = useState<Date>();
    const [sessionStartDate, setSessionStartDate] = useState<Date>();
    const [dateCreated, setDateCreated] = useState<Date>();

    const { data, setData, post, errors, processing } = useForm<{
        campaign_name: string;
        session: string;
        program_name: string;
        application_start_date: string;
        application_end_date: string;
        admission_test_date: string;
        session_start_date: string;
        date_created: string;
    }>({
        campaign_name: '',
        session: '',
        program_name: '',
        application_start_date: '',
        application_end_date: '',
        admission_test_date: '',
        session_start_date: '',
        date_created: '',
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        post(route('admin.admission.spots.store'));
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Admission Spot" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Admission Spot</div>

                        <Button>
                            <Link href="/admin/admission?tab=admission_spot" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="campaign_name">Campaign Name *</Label>
                                        <Input
                                            type="text"
                                            id="campaign_name"
                                            placeholder="Enter campaign name"
                                            value={data.campaign_name}
                                            onChange={(e) => setData('campaign_name', e.target.value)}
                                            aria-invalid={!!errors.campaign_name}
                                        />
                                        <InputError message={errors.campaign_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session">Session *</Label>
                                        <Input
                                            type="text"
                                            id="session"
                                            placeholder="Enter session"
                                            value={data.session}
                                            onChange={(e) => setData('session', e.target.value)}
                                            aria-invalid={!!errors.session}
                                        />
                                        <InputError message={errors.session} />
                                    </div>

                                    <div>
                                        <Label htmlFor="program_name">Program Name *</Label>
                                        <Input
                                            type="text"
                                            id="program_name"
                                            placeholder="Enter program name"
                                            value={data.program_name}
                                            onChange={(e) => setData('program_name', e.target.value)}
                                            aria-invalid={!!errors.program_name}
                                        />
                                        <InputError message={errors.program_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="application_start_date">Application Start Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !applicationStartDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {applicationStartDate ? format(applicationStartDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={applicationStartDate}
                                                    onSelect={(date) => {
                                                        setApplicationStartDate(date);
                                                        setData('application_start_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.application_start_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="application_end_date">Application End Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !applicationEndDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {applicationEndDate ? format(applicationEndDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={applicationEndDate}
                                                    onSelect={(date) => {
                                                        setApplicationEndDate(date);
                                                        setData('application_end_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.application_end_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="admission_test_date">Admission Test Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !admissionTestDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {admissionTestDate ? format(admissionTestDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={admissionTestDate}
                                                    onSelect={(date) => {
                                                        setAdmissionTestDate(date);
                                                        setData('admission_test_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.admission_test_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session_start_date">Session Start Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !sessionStartDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {sessionStartDate ? format(sessionStartDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={sessionStartDate}
                                                    onSelect={(date) => {
                                                        setSessionStartDate(date);
                                                        setData('session_start_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.session_start_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="date_created">Date Created *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !dateCreated && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {dateCreated ? format(dateCreated, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={dateCreated}
                                                    onSelect={(date) => {
                                                        setDateCreated(date);
                                                        setData('date_created', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.date_created} />
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Admission Spot</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
