import React from 'react';
import Back from '../../common/back/Back';
// Define a placeholder for your Back component if needed


// Define the type for the data items
interface GlanceItem {
  label: string;
  value: string[];
}

const AtAGlance = () => {
  const glanceData: GlanceItem[] = [
    {
      label: "Official name of the University",
      value: ["National Institute of Science and Technology (NIST)"],
    },
    {
      label: "Year of establishment",
      value: [
        "NIST is affiliated with the National University of Bangladesh, offering BSc (Professional) programs in CSE, ECE, BBA, and BSc Honours in Biochemistry and Molecular Biology. Since 2019, NIST has maintained its commitment to pioneering innovation in technical education.",
      ],
    },
    {
      label: "Campus buildings and locations",
      value: ["19/1, West Panthapath, Dhaka-1205"],
    },
    {
      label: "Programs",
      value: [
        "Undergraduate Program:",
        "- Bachelor of Business Administration (BBA)",
        "- Computer Science and Engineering (CSE)",
        "- Electronics and Communication Engineering (ECE)",
        "- Biochemistry and Molecular Biology (BMB)",
      ],
    },
    {
      label: "Departments",
      value: [
        "- Department of Computer Science & Engineering (CSE)",
        "- Department of Business Administration (BBA)",
        "- Department of Electronics and Communication Engineering (ECE)",
        "- Department of Biochemistry and Molecular Biology (BMB)",
      ],
    },
  ];

  return (
    <>
      <Back title="At a Glance" />
      <section className="py-24">
        <div className="container mx-auto max-w-4xl px-4">
          <div className="overflow-hidden rounded-lg bg-white shadow-lg">
            {glanceData.map((item, index) => (
              <div 
                key={index} 
                className="flex flex-col border-b last:border-b-0 md:flex-row"
              >
                <div className="w-full bg-gray-50 p-4 font-bold text-gray-800 md:w-[30%] md:text-base sm:text-lg">
                  {item.label}:
                </div>
                <div className="w-full p-4 text-justify leading-relaxed text-gray-700 md:w-[70%] md:pt-4 sm:pt-2.5">
                  {item.value.map((val, idx) => (
                    <p key={idx} className="mb-1">
                      {val}
                    </p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default AtAGlance;