<?php
// app/Http/Controllers/EditorUploadController.php
namespace App\Http\Controllers;

use App\Models\EditorUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EditorUploadController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'image' => ['required', 'file', 'mimes:jpeg,png,gif', 'max:5120'],
            'draft_token' => ['required', 'uuid'],
        ]);

        $file = $validated['image'];
        $draftToken = $validated['draft_token'];

        // Ensure unique upload per draft_token + original_name
        $existing = EditorUpload::where('draft_token', $draftToken)
            ->where('original_name', $file->getClientOriginalName())
            ->first();

        if ($existing) {
            return response()->json([
                'id' => $existing->id,
                'url' => asset('storage/' . $existing->path),
            ]);
        }

        // Store new file if no duplicate
        $path = storeSanitizedFile($file, 'uploads/post-images');

        $upload = EditorUpload::create([
            'path' => $path,
            'filename' => basename($path),
            'original_name' => $file->getClientOriginalName(),
            'mime' => $file->getClientMimeType(),
            'size' => $file->getSize(),
            'draft_token' => $draftToken,
        ]);

        return response()->json([
            'id' => $upload->id,
            'url' => asset('storage/' . $upload->path),
        ]);
    }

    public function destroy($id)
    {
        $upload = EditorUpload::findOrFail($id);

        // Delete the file from storage
        if ($upload->path && Storage::disk('public')->exists($upload->path)) {
            Storage::disk('public')->delete($upload->path);
        }

        // Delete the DB record
        $upload->delete();

        return response()->json([
            'success' => true,
            'message' => 'Upload deleted successfully.'
        ]);
    }

}