<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdmissionPromoImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'left_banner_image' => $this->left_banner_image ? asset('storage/'.$this->left_banner_image) : null,
            'right_banner_image_top' => $this->right_banner_image_top ? asset('storage/'.$this->right_banner_image_top) : null,
            'right_banner_image_top_caption' => $this->right_banner_image_top_caption,
            'right_banner_image_top_subtitle' => $this->right_banner_image_top_subtitle,
            'right_banner_image_bottom' => $this->right_banner_image_bottom ? asset('storage/'.$this->right_banner_image_bottom) : null,
            'right_banner_image_bottom_caption' => $this->right_banner_image_bottom_caption,
            'right_banner_image_bottom_subtitle' => $this->right_banner_image_bottom_subtitle,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
