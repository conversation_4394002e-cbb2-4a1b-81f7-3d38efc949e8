<?php

namespace App\Http\Controllers;
use App\Http\Controllers\Controller;
use App\Models\GoverningBody;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class GoverningBodyDestroyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GoverningBody $governingbody)
    {
        
        if ($governingbody->profile_image) {
            Storage::disk("public")->delete($governingbody->profile_image);
        }
        $governingbody->delete();

        return redirect(route('admin.governingbody.index'))->with('success', 'Entry deleted successfully');
    }
}
