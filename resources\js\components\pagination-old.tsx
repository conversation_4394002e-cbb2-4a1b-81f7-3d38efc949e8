import { Link, router } from '@inertiajs/react';

import { Arrow<PERSON>eft, ArrowRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

type paginationProps = {
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
    activeTab: 'class_schedule' | 'faculty_schedule';
};

const Pagination = ({ meta, activeTab }: paginationProps) => {
    const handleClick = (url: string | null) => {
        if (!url) return;
        const newUrl = new URL(url);
        newUrl.searchParams.set('tab', activeTab); // Preserve tab
        router.get(newUrl.toString(), {}, { preserveState: true, replace: true });
    };
    return (
        <>
            {meta.links.length > 3 && (
                <div className="mt-4 flex flex-wrap items-center justify-center gap-2 px-10 sm:justify-between">
                    <div>
                        Showing {meta.from} - {meta.to} of {meta.total} entries
                    </div>
                    <div className="flex flex-wrap items-center justify-center gap-2">
                    {meta.links.map((link) => (
                        <span key={link.label}>
                            {link.label.includes('Previous') && link.url === null && (
                                <span className={`cursor-not-allowed rounded-md border px-2 py-1 text-gray-500 dark:text-gray-600`}>
                                    <ChevronsLeft className="mr-2 inline-block h-4 w-4" />
                                    Previous
                                </span>
                            )}
                            {link.label.includes('Previous') && link.url !== null && (
                                <Link
                                    href={link.url ?? ''}
                                    onClick={() => handleClick(link.url)}
                                    preserveScroll
                                    className={`cursor-pointer rounded-md border px-2 py-1 text-black hover:text-gray-700 dark:text-gray-300 ${link.active ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300'}`}
                                >
                                    <ChevronsLeft className="mr-2 inline-block h-4 w-4" />
                                    Previous
                                </Link>
                            )}
                            {link.label.includes('Next') && link.url !== null && (
                                <Link
                                    href={link.url ?? ''}
                                    preserveScroll
                                    className={`cursor-pointer rounded-md border px-2 py-1 text-black hover:text-gray-700 dark:text-gray-400 ${link.active ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300'}`}
                                >
                                    Next
                                    <ChevronsRight className="ml-2 inline-block h-4 w-4" />
                                </Link>
                            )}
                            {link.label.includes('Next') && link.url === null && (
                                <span className={`cursor-not-allowed rounded-md border px-2 py-1 text-gray-500 dark:text-gray-600`}>
                                    Next
                                    <ChevronsRight className="ml-2 inline-block h-4 w-4" />
                                </span>
                            )}
                            {!link.label.includes('Previous') && !link.label.includes('Next') && (
                                <Link
                                    href={link.url ?? ''}
                                    preserveScroll
                                    className={`cursor-pointer rounded-md border px-2 py-1 text-black hover:text-gray-700 dark:text-gray-300 ${link.active ? 'border-blue-500 dark:border-blue-400' : 'border-gray-300'}`}
                                >
                                    {link.label}
                                </Link>
                            )}
                        </span>
                    ))}
                    </div>
                </div>
            )}
        </>
    );
};

export default Pagination;
