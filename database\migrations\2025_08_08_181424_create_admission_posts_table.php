<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admission_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('admission_circular_content')->nullable();
            $table->enum('active_status', ['unpublished', 'published'])->default('unpublished');
            $table->date('published_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admission_posts');
    }
};
