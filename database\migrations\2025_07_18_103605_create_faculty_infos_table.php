<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faculty_infos', function (Blueprint $table) {
            $table->id(); // Standard auto-incrementing primary key
            $table->string('name');
            $table->string('slug');
            $table->string('designation');
            $table->string('department');
            $table->string('image')->nullable(); // Path to the image file

            $table->text('bio')->nullable();
            $table->text('about')->nullable();
            $table->text('education')->nullable();
            $table->text('research')->nullable();
            $table->text('interests')->nullable();

            $table->string('official_email')->unique();
            $table->string('secondary_email')->nullable();
            $table->string('primary_phone');
            $table->string('secondary_phone')->nullable();

            $table->timestamps(); // `created_at` and `updated_at` columns
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faculty_infos');
    }
};
