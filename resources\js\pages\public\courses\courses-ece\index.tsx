import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Back from '../../common/back/Back';

interface AccordionItem {
  id: number;
  title: string;
  content: JSX.Element;
}

const CourseEcePage = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const accordionData: AccordionItem[] = [
    {
      id: 0,
      title: 'Entry Requirements',
      content: (
        <ul className="list-[square] space-y-2 pl-5">
          <li>SSC or Equivalent with Minimum GPA 3 from Science Group.</li>
          <li>HSC or Equivalent with Minimum GPA 2.5 from Science Group.</li>
          <li>Minimum GPA 3.00 in Physics/Mathematics.</li>
          <li>English medium students: minimum 3B grades in “O” level and 1B grade in “A” level subjects.</li>
        </ul>
      ),
    },
    {
      id: 1,
      title: 'Grading System',
      content: (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-[#E9F7FE] text-center text-[#004422]">
            <thead>
              <tr className="bg-[#B7DA9B]">
                <th className="border border-black p-2">Number Grade</th>
                <th className="border border-black p-2">Letter Grade</th>
                <th className="border border-black p-2">Grade Point</th>
                <th className="border border-black p-2">Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr><td className="border p-2">80 - 100</td><td className="border p-2">A+</td><td className="border p-2">4.00</td><td className="border p-2">Outstanding</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">75 - 79</td><td className="border p-2">A</td><td className="border p-2">3.75</td><td className="border p-2">Excellent</td></tr>
              <tr><td className="border p-2">70 - 74</td><td className="border p-2">A-</td><td className="border p-2">3.50</td><td className="border p-2">Very Good</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">65 - 69</td><td className="border p-2">B+</td><td className="border p-2">3.25</td><td className="border p-2">Good</td></tr>
              <tr><td className="border p-2">60 - 64</td><td className="border p-2">B</td><td className="border p-2">3.00</td><td className="border p-2">Satisfactory</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">55 - 59</td><td className="border p-2">B-</td><td className="border p-2">2.75</td><td className="border p-2">Above Average</td></tr>
              <tr><td className="border p-2">50 - 54</td><td className="border p-2">C+</td><td className="border p-2">2.50</td><td className="border p-2">Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">45 - 49</td><td className="border p-2">C</td><td className="border p-2">2.25</td><td className="border p-2">Below Average</td></tr>
              <tr><td className="border p-2">40 - 44</td><td className="border p-2">D</td><td className="border p-2">2.00</td><td className="border p-2">Pass</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">Less Than 40</td><td className="border p-2">F</td><td className="border p-2">0.00</td><td className="border p-2">Fail</td></tr>
            </tbody>
          </table>
        </div>
      ),
    },
    {
      id: 2,
      title: 'Fee Structure',
      content: <img src="/images/fees/ECE_FEES_2025.jpg" alt="ECE Fee Structure" />,
    },
    {
      id: 3,
      title: 'Course Curriculum',
      content: (
        <iframe
          src="https://docs.google.com/gview?url=https://nist.edu.bd/contents/uploads/syllabus/ECE-Course-Curriculam.pdf&embedded=true"
          className="h-[800px] w-full"
        ></iframe>
      ),
    },
  ];

  const eceFeatures = [
    'Modern Electronics and Communication labs.',
    'Advanced simulation tools for embedded system design.',
    'Seminars and workshops on cutting-edge technologies.',
    'Wi-Fi enabled smart classrooms.',
    'Robotics and IoT-based projects.',
    'Experienced faculty with national and international exposure.',
    'Industry visits and internship opportunities.',
    'Active participation in national-level tech competitions.',
    'Research facilities for final year students.',
    'Library access with specialized engineering resources.',
  ];

  return (
    <>
      <Head title="Department of ECE" />
      <Back title="Department of Electronics and Communication Engineering" />

      <section className="container mx-auto px-4 py-12">
        <h3 className="text-lg font-medium text-center text-orange-500">WELCOME</h3>
        <h2 className="text-4xl py-4 text-center uppercase font-bold text-[#000F38]">
          Department of Electronics and Communication Engineering
        </h2>
        <p className="text-justify mt-4 text-xl font-medium text-gray-600">
          Dept. of ECE, NIST provides students with a future-ready curriculum focused on electronics, embedded systems, wireless technologies, and communication engineering. Students receive hands-on lab training and career-oriented knowledge to thrive in a global engineering industry.
        </p>
      </section>

      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row">
        <div className="basis-full md:basis-[56%]">
          <h2 className="text-4xl font-bold text-[#000F38]">Why Study ECE at NIST?</h2>
          <p className="my-4 text-justify text-gray-600">
            Electronics and Communication Engineering is one of the most versatile fields of modern engineering. With a focus on both hardware and software systems, ECE graduates are highly demanded in industries ranging from telecommunications and embedded systems to automation and robotics.
          </p>
          <div className="mt-6 space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-orange-500">MISSION</h3>
              <p className="text-justify text-gray-600">
                To empower students with deep technical knowledge in electronics and communication systems and develop innovators who contribute to technological advancement and nation building.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-orange-500">VISION</h3>
              <p className="text-justify text-gray-600">
                To become a leading ECE department producing globally competent engineers and technologists for a sustainable future.
              </p>
            </div>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/ece/ece-lab1.jpg" alt="ECE Lab" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row-reverse">
        <div className="basis-full md:basis-[56%]">
          <h3 className="text-base font-semibold text-orange-500">WHY CHOOSE NIST?</h3>
          <h2 className="my-2.5 max-w-xl text-4xl font-bold text-[#000F38]">ECE Facilities at NIST</h2>
          <div className="mt-4 rounded-lg bg-gray-50 p-4">
            <ul className="space-y-1.5">
              {eceFeatures.map((feature, index) => (
                <li key={index} className="border-l-4 border-[#07944a] bg-white px-2 py-1.5 text-base font-medium text-[#333] shadow-sm">
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/ece/ece-lab2.jpg" alt="ECE Lab" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto max-w-5xl px-4 md:w-[95%]">
          <h2 className="mb-10 text-center text-3xl font-bold uppercase text-[#000F38]">ECE Course Information</h2>
          <div className="space-y-2.5">
            {accordionData.map((item) => (
              <div key={item.id} className="overflow-hidden rounded-md bg-[#2c3e50] text-white">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="flex w-full cursor-pointer select-none items-center justify-between p-4 text-left text-lg font-medium transition-colors hover:bg-[#34495e] md:text-base"
                  aria-expanded={activeIndex === item.id}
                >
                  <span>{item.title}</span>
                  <span>{activeIndex === item.id ? <ChevronDown /> : <ChevronRight />}</span>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${activeIndex === item.id ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="bg-[#ecf0f1] p-4 text-[#2c3e50]">{item.content}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default CourseEcePage;
