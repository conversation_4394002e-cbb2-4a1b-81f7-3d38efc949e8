export const homeAbout = [
  {
    id: 1,
    cover: "https://img.icons8.com/ios/80/ffffff/athlete.png",
    title: "About",
    desc: "National Institute of Science & Technology, affiliated with the national university brings the best degrees, BSc Hons.(Professional) in BBA, CSE, ECE, and BSc Hons. in Biochemistry and Molecular Biology with the help of the brightest minds from home and abroad for greater job placement and career opportunists.",
    bgColor: "#28824f", // background color
    hoverColor: "#001234", // hover color
  },
  {
    id: 1,
    cover: "https://img.icons8.com/dotty/80/ffffff/storytelling.png",
    title: "Mission",
    desc: "The National Institute of Science and Technology is committed to implementing the most effective teaching-learning approaches. One of our primary missions is to guarantee that the natural environment is protected, preserved, and maintained. And provide education at an affordable cost.",
    bgColor: "#28824f", // background color
    hoverColor: "#001234", // hover color
    
  },
  {
    id: 1,
    cover: "https://img.icons8.com/ios/80/ffffff/diploma.png",
    title: "Vision",
    desc: "Our main goal is to reawaken the thrill of discovery, learn from it, and gain international reputation by focusing on our capabilities. We also have the ambition to engage with relevant scientific research institutions, business participants, and the general public .",
    bgColor: "#28824f", // background color
    hoverColor: "#001234", // hover color
  },
]

export const homeAbout2 = [
  {
    title: "Benefit 1",
    desc: "Description of Benefit 1",
    bgColor: "#f0f0f0", // background color
    hoverColor: "#e0e0e0", // hover color
  },
  {
    title: "Benefit 2",
    desc: "Description of Benefit 2",
    bgColor: "#cfe2f3",
    hoverColor: "#9fc5e8",
  },
  // Add more items as needed
];


export const awrapper = [
  {
    cover: "https://img.icons8.com/external-yogi-aprelliyanto-basic-outline-yogi-aprelliyanto/80/ffffff/external-graduation-education-yogi-aprelliyanto-basic-outline-yogi-aprelliyanto.png",
    data: "300+",
    title: "SUCCESS STORIES",
  },

  {
    cover: "https://img.icons8.com/ios/80/ffffff/athlete.png",
    data: "30+",
    title: "EXPERIENCED FACULTY",
  },
  {
    cover: "https://img.icons8.com/external-outline-icons-maxicons/80/ffffff/external-calender-insurance-outline-outline-icons-maxicons.png",
    data: "400+",
    title: "ENROLLED",
  },
  {
    cover: "https://img.icons8.com/ios/80/ffffff/macbook-idea--v3.png",
    data: "6+",
    title: "COURSES",
  },
]


export const faq = [
  {
    title: "How to Enroll This Online Courses?",
    desc: "This is the first item's accordion body. It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
  {
    title: "Where It is hidden by default, until the collapse?",
    desc: "It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
  {
    title: "How It is hidden by default, until the collapse?",
    desc: "It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
  {
    title: "How to Enroll This Online Courses?",
    desc: "This is the first item's accordion body. It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
  {
    title: "Where It is hidden by default, until the collapse?",
    desc: "It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
  {
    title: "How It is hidden by default, until the collapse?",
    desc: "It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. It's also worth noting that just about any HTML can go within the .accordion-body, though the transition does limit overflow.",
  },
]
export const blog = [
  {
    id: 1,
    type: "admin",
    date: "Apr. 15, 2025",
    com: "3 COMMENTS ",
    title: "জাতীয় বিশ্ববিদ্যালয়ের ২০২৪-২০২৫ শিক্ষাবর্ষে ১ম বর্ষ স্নাতক (সম্মান) ভর্তি",
    desc: `জাতীয় বিশ্ববিদ্যালয়ের অধীন ২০২৪-২০২৫ শিক্ষাবর্ষে ৪ বছর মেয়াদি বিএসসি ইন প্রাণরসায়ন ও অণুপ্রান বিজ্ঞান ( Biochemistry & Molecular Biology) সম্মান কোর্সের ভর্তি কার্যক্রম শুরু হয়েছে।
    আবেদনের শেষ তারিখ: ২৫ এপ্রিল ২০২৫ ইং। ভর্তি পরীক্ষা: ২৪ মে, ২০২৫ ইং। যোগাযোগঃ ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনলজি (NIST) ড্যাফোডিল কনকর্ড টাওয়ার, ১৯/১, পশ্চিম পান্থপথ, ঢাকা । ফোনঃ ০১৭১৩৪৯৩১৯৬, ০১৮১১৪৫৮৮৫৩ ।`,
    cover: "/images/nist/popup.jpg",
  },
  {
    id: 2,
    type: "admin",
    date: "Jan. 21, 2025",
    com: "3 COMMENTS ",
    title: "ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজির নবাগত শিক্ষার্থীদের নবীন বরণ ও পুরস্কার বিতরণ ২০২৫ অনুষ্ঠিত",
    desc: "ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজির নবাগত শিক্ষার্থীদের নবীন বরণ ও পুরস্কার বিতরণ অনুষ্ঠান ২১ জানুয়ারি ২০২৫, বিজয় মিলনায়তনে অনুষ্ঠিত হয়। এডহক কমিটির সভাপতি ড. বিলকিস বেগমের সভাপতিত্বে প্রধান অতিথি হিসেবে উপস্থিত ছিলেন শিক্ষক কর্মচারি ঐক্যজোটের চেয়ারম্যান অধ্যক্ষ মো. সেলিম ভূঁইয়া। প্রধান বক্তা ছিলেন সাবেক সংসদ সদস্য রাশেদা বেগম হীরা। বিশেষ অতিথি ছিলেন ঢাকা বিশ্ববিদ্যালয়ের অধ্যাপক ড. মোহাম্মদ আলমোজাদ্দেদী আলফেছানী ও ড্যাফোডিল পরিবারের প্রধান নির্বাহী কর্মকর্তা মোহাম্মদ নূরুজ্জামান। স্বাগত বক্তব্য রাখেন অধ্যক্ষ (চলতি দায়িত্ব) তানজিদা সুপ্তা। অনুষ্ঠানে বার্ষিক ক্রীড়া প্রতিযোগিতায় বিজয়ীদের মাঝে পুরস্কার বিতরণ করা হয়। সাংস্কৃতিক অনুষ্ঠানের মাধ্যেমে অনুষ্ঠান শেষ হয় ।",
    cover: "../images/news/blog/freshersreceiption2025.jpg", 
  },
  {
    id: 3,
    type: "admin",
    date: "Nov. 23, 2024",
    com: "3 COMMENTS ",
    title: " অধ্যক্ষ প্রফেসর মো: ফয়েজ হোসেন স্যারের মৃত্যুতে আমরা  গভীরভাবে শোকাহত",
    desc: "ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি’র অধ্যক্ষ প্রফেসর মো: ফয়েজ হোসেন বৃহস্পতিবার রাত ১১.৪০ মিনিটে চিকিৎসাধীন অবস্থায় স্কায়ার হাসপাতালে মৃত্যুবরণ করেছেন (ইন্নালিল্লাহি ওয়া ইন্না ইলাইহি রাজিউন)। মৃত্যুকালে তিন কন্যা, স্ত্রীসহ অসংখ্য ভক্ত রেখে গেছেন। শুক্রবার সিদ্ধেশ্বরী কলেজ মাঠে ও দিলুরোড জামে মসজিদে জানাযা শেষে মিরপুর বুদ্ধিজীবী কবরস্থানে দাফন করা হয়। উল্লেখ্য, তিনি বাংলাদেশ কলেজ শিক্ষক সমিতি ((বাকশিস) এর সাধারণ সম্পাদক, সিদ্ধেশ্বরী কলেজের সাবেক অধ্যক্ষ, প্রফেশনাল ইনস্টিটিউট এসোসিয়েশন অব ন্যাশনাল ইউনিভার্সিটি'র আহ্বায়ক এর দায়িত্বে ছিলেন। স্যারের মৃত্যুতে ড্যাফোডিল পরিবারের সকল সদস্য  গভীর শোকাহত।",
    cover: "../../../../images/nist/cover_foyezsir.jpg",
  },
  {
    id: 4,
    type: "admin",
    date: "SEP. 18, 2024",
    com: "3 COMMENTS ",
    title: "৪ বছর মেয়াদি প্রফেশনাল (অনার্স ) CSE, ECE & BBA প্রোগ্রামে ২০২৩-২৪ শিক্ষাবর্ষে ভর্তি ফি’তে ৩০% ছাড়ে ভর্তি চলছে।",
    desc: "প্রাথমিক আবেদনের মাধ্যমে ড্যাফোডিল পরিচালিত ও জাতীয় বিশ্ববিদ্যালয় অধিভুক্ত - ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি (NIST) তে ৪ বছর মেয়াদি প্রফেশনাল (অনার্স ) CSE, ECE & BBA প্রোগ্রামে ২০২৩-২৪ শিক্ষাবর্ষে ভর্তি ফি’তে ৩০% ছাড়ে ভর্তি চলছে।",
    cover: "../images/news/ad/nist_ad.jpg",
  },
  {
    id: 5,
    type: "admin",
    date: "SEP. 11, 2024",
    com: "5 COMMENTS ",
    title: "২০২৩-২৪ শিক্ষাবর্ষে ২য় রিলিজ স্লিপের মাধ্যমে ৪ বছর মেয়াদি বিএসসি অনার্স ইন প্রাণরসায়ন ও অণুপ্রাণ বিজ্ঞান (বায়োকেমিস্ট্রি ও মলিকুলার বায়োলজি) বিষয়ে ভর্তির জন্য আবেদন",
    desc: "২০২৩-২৪ শিক্ষাবর্ষে ২য় রিলিজ স্লিপের মাধ্যমে ২০%- ১০০% ছাড়ে ড্যাফোডিল পরিচালিত ও জাতীয় বিশ্ববিদ্যালয় অধিভুক্ত - ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি (NIST) তে ৪ বছর মেয়াদি বিএসসি অনার্স ইন প্রাণরসায়ন ও অণুপ্রাণ বিজ্ঞান (বায়োকেমিস্ট্রি ও মলিকুলার বায়োলজি) বিষয়ে জাতীয় বিশ্ববিদ্যালয় এর ওয়েবসাইট থেকে ভর্তির জন্য আবেদন করা যাবে। আবেদনের সময় ১১/০৯/২০২৪ থেকে ২৩/০৯/২০২৪ রাত ১২টা পযর্ন্ত।",
    cover: "../images/news/ad/nist_ad2.jpg",
  },
  {
    id: 6,
    type: "user",
    date: "JUL. 6, 2024",
    com: "10 COMMENTS ",
    title: `(NIST) এর ২০২১-২০২২ সেশন এর CSE বিভাগের দল Nirex "National Steam Olympiad" এর ফাইনাল রাউন্ডের জন্য নির্বাচিত`,
    desc: "ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি (NIST) এর ২০২১-২০২২ সেশন এর CSE বিভাগের দল Nirex National Steam Olympiad এর ফাইনাল রাউন্ডের জন্য নির্বাচিত হওয়ায় তাদেরকে অভিনন্দন ও শুভেচ্ছা । তাদের প্রোজেক্ট 3D printer। যেটি পরিবেশ বান্ধব এবং এটি বর্তমান  বিশ্বের জন্য একটি গুরুত্বপূর্ণ আবিষ্কার।",
    cover: "../images/news/achievement/steam_olympiad.PNG",
  },
  {
    id: 7,
    type: "admin",
    date: "SEP. 18, 2024",
    com: "3 COMMENTS ",
    title: "৪ বছর মেয়াদি প্রফেশনাল (অনার্স ) CSE, ECE & BBA প্রোগ্রামে ২০২৩-২৪ শিক্ষাবর্ষে ভর্তি ফি’তে ৩০% ছাড়ে ভর্তি চলছে।",
    desc: "প্রাথমিক আবেদনের মাধ্যমে ড্যাফোডিল পরিচালিত ও জাতীয় বিশ্ববিদ্যালয় অধিভুক্ত - ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি (NIST) তে ...",
    cover: "../images/news/ad/nist_ad.jpg",
  },
  {
    id: 8,
    type: "admin",
    date: "SEP. 11, 2024",
    com: "5 COMMENTS ",
    title: "২০২৩-২৪ শিক্ষাবর্ষে ২য় রিলিজ স্লিপের মাধ্যমে ৪ বছর মেয়াদি বিএসসি অনার্স ইন প্রাণরসায়ন ও অণুপ্রাণ বিজ্ঞান (বায়োকেমিস্ট্রি ও মলিকুলার বায়োলজি) বিষয়ে ভর্তির জন্য আবেদন",
    desc: "২০২৩-২৪ শিক্ষাবর্ষে ২য় রিলিজ স্লিপের মাধ্যমে ২০%- ১০০% ছাড়ে ড্যাফোডিল পরিচালিত ও জাতীয় বিশ্ববিদ্যালয় অধিভুক্ত - ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি ...",
    cover: "../images/news/ad/nist_ad2.jpg",
  },
  {
    id: 9,
    type: "user",
    date: "JUL. 6, 2024",
    com: "10 COMMENTS ",
    title: `(NIST) এর ২০২১-২০২২ সেশন এর CSE বিভাগের দল Nirex "National Steam Olympiad" এর ফাইনাল রাউন্ডের জন্য নির্বাচিত`,
    desc: "ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি (NIST) এর ২০২১-২০২২ সেশন এর CSE বিভাগের দল Nirex National Steam Olympiad এর ...",
    cover: "../images/news/achievement/steam_olympiad.PNG",
  },
]
export const testimonal = [
  {
    id: 1,
    name: "ROGER SCOTT",
    post: "MARKETING MANAGER",
    desc: "Far far away, behind the word mountains, far from the countries Vokalia and Consonantia, there live the blind texts.",
    cover: "./images/testo/t1.webp",
  },
  {
    id: 2,
    name: "ROGER SCOTT",
    post: "MARKETING MANAGER",
    desc: "Far far away, behind the word mountains, far from the countries Vokalia and Consonantia, there live the blind texts.",
    cover: "./images/testo/t2.webp",
  },
  {
    id: 3,
    name: "ROGER SCOTT",
    post: "MARKETING MANAGER",
    desc: "Far far away, behind the word mountains, far from the countries Vokalia and Consonantia, there live the blind texts.",
    cover: "./images/testo/t3.webp",
  },
]
