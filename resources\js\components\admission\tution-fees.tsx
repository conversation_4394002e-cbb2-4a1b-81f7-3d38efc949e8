import React from 'react';

const feesData = [
  { program: 'B.Sc. in CSE', totalCredits: 144, totalCost: 'Tk. 2,72,000' },
  { program: 'B.Sc. in ECE', totalCredits: 144, totalCost: 'Tk. 2,08,000' },
  { program: 'BBA', totalCredits: 126, totalCost: 'Tk. 2,04,000' },
  { program: 'B.Sc. in BMB', totalCredits: 128, totalCost: 'Tk. 2,73,000' },
];

const TuitionFees = () => (
  <section className="py-16">
    <div className="container mx-auto px-4 max-w-4xl">
      <h2 className="text-center text-3xl font-bold text-gray-800">Tuition Fees & Structure</h2>
      <div className="mt-8 overflow-x-auto rounded-lg border">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Program</th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Total Credits</th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Total Estimated Cost</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {feesData.map((item, index) => (
              <tr key={index}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.program}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.totalCredits}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-700">{item.totalCost}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <p className="mt-4 text-center text-sm text-gray-500">*Waivers, Installment plans and scholarships are available. Please contact admission office for details.</p>
    </div>
  </section>
);

export default TuitionFees;