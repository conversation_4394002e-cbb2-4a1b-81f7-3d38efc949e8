<?php

namespace App\Http\Controllers\Frontend\Admission;

use App\Http\Controllers\Controller;
use App\Http\Resources\AdmissionPromoImageResource;
use App\Http\Resources\AdmissionSpotResource;
use App\Http\Resources\AdmissionPostResource;
use App\Models\AdmissionPromoImage;
use App\Models\AdmissionSpot;
use App\Models\AdmissionPost;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdmissionInfoController extends Controller
{
    public function index(Request $request)
    {
        // Get active admission promo image (most recent if multiple active)
        $admissionPromoImage = AdmissionPromoImage::where('status', 'active')
            ->orderBy('updated_at', 'desc')
            ->first();

        // Get active admission spot (most recent if multiple active)
        $admissionSpot = AdmissionSpot::where('active_status', 'published')
            ->orderBy('updated_at', 'desc')
            ->first();

        // Get active admission post (most recent if multiple active)
        $admissionPost = AdmissionPost::where('active_status', 'published')
            ->orderBy('updated_at', 'desc')
            ->first();

        return Inertia::render('public/admission/admission-info/index', [
            'admissionPromoImage' => $admissionPromoImage ? new AdmissionPromoImageResource($admissionPromoImage) : null,
            'admissionSpot' => $admissionSpot ? new AdmissionSpotResource($admissionSpot) : null,
            'admissionPost' => $admissionPost ? new AdmissionPostResource($admissionPost) : null,
        ]);
    }
}
