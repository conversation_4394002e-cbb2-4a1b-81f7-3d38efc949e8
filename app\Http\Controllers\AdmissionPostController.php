<?php

namespace App\Http\Controllers;

use App\Models\AdmissionPost;
use App\Models\EditorUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class AdmissionPostController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'admission_circular_content' => 'nullable|string',
            'published_date' => 'required|date',
            'draft_token' => ['required', 'uuid'],
        ]);

        $data['active_status'] = 'unpublished';

       

        return DB::transaction(function () use ($request, $data) {
            $admissionPost = AdmissionPost::create([
                'title' => $data['title'],
                'admission_circular_content' => $data['admission_circular_content'],
                'active_status' => $data['active_status'],
                'published_date' => $data['published_date'],
            ]);

            // Extract image paths used in content
            $pathsInContent = $this->extractStoragePathsFromHtml($admissionPost->admission_circular_content);

            // Find matching uploads for this draft_token
            $uploads = EditorUpload::where('draft_token', $data['draft_token'])
                ->when(!empty($pathsInContent), fn($q) => $q->whereIn('path', $pathsInContent))
                ->get();

            // Attach them to AdmissionPost via pivot
            foreach ($uploads as $upload) {
                $admissionPost->editorUploads()->syncWithoutDetaching([$upload->id]);
                $upload->update(['draft_token' => null]);
            }

            // Delete unused drafts from this token
            EditorUpload::where('draft_token', $data['draft_token'])
                ->whereNotIn('path', $pathsInContent)
                ->get()
                ->each(function ($img) {
                    Storage::disk('public')->delete($img->path);
                    $img->delete();
                });

             

            return redirect()->route('admin.admission.index', ['tab' => 'admission_post'])
                ->with('success', 'Admission post created');
        });
    }

    public function update(Request $request, AdmissionPost $admissionPost)
    {
        // Status-only update case
        if ($request->has('active_status') && count($request->all()) === 1) {
            $data = $request->validate([
                'active_status' => 'required|in:unpublished,published',
            ]);

            $admissionPost->update($data);
            return back()->with('success', 'Status updated');
        } else {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'admission_circular_content' => 'nullable|string',
                'active_status' => 'required|in:unpublished,published',
                'published_date' => 'required|date',
                'draft_token' => ['nullable', 'uuid'],
            ]);
        }

        return DB::transaction(function () use ($request, $admissionPost, $data) {
            $admissionPost->fill(attributes: [
                'title' => $data['title'],
                'admission_circular_content' => $data['admission_circular_content'] ?? '',
                'active_status' => $data['active_status'],
                'published_date' => $data['published_date'],
            ])->save();

            // Collect paths from content
            $pathsInContent = $this->extractStoragePathsFromHtml($admissionPost->admission_circular_content);
            $pathsInContent = array_values(array_unique($pathsInContent));

            $attachedIds = [];

            // (A) Attach draft uploads
            if (!empty($data['draft_token']) && !empty($pathsInContent)) {
                $drafts = EditorUpload::where('draft_token', $data['draft_token'])
                    ->whereIn('path', $pathsInContent)
                    ->get();

                foreach ($drafts as $upload) {
                    $admissionPost->editorUploads()->syncWithoutDetaching([$upload->id]);
                    $upload->update(['draft_token' => null]);
                    $attachedIds[] = $upload->id;
                }
            }

            // (B) Attach already-saved uploads (loose ones with same path)
            foreach ($pathsInContent as $path) {
                $upload = EditorUpload::where('path', $path)->first();
                if ($upload) {
                    $admissionPost->editorUploads()->syncWithoutDetaching([$upload->id]);
                    $attachedIds[] = $upload->id;
                }
            }

            // (C) Remove stale uploads that are no longer in content
            $currentIds = $admissionPost->editorUploads()->pluck('editor_uploads.id')->all();
            $staleIds = array_diff($currentIds, $attachedIds);

            if (!empty($staleIds)) {
                $staleUploads = EditorUpload::whereIn('id', $staleIds)->get();
                foreach ($staleUploads as $upload) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
                $admissionPost->editorUploads()->detach($staleIds);
            }

            return redirect(route('admin.admission.index', ['tab' => 'admission_post']))
                ->with('success', 'Admission post updated successfully');
        });
    }

    public function destroy(AdmissionPost $admissionPost)
    {
        //$admissionPost->delete();

        DB::transaction(function () use ($admissionPost) {
        // Collect related uploads
        $uploads = $admissionPost->editorUploads()->get();

        // Detach pivot first
        $admissionPost->editorUploads()->detach();

        // Delete the admission post itself
        $admissionPost->delete();

        // Cleanup orphaned uploads
        foreach ($uploads as $upload) {
            $stillUsed = $upload->admissionPosts()->exists() || $upload->news()->exists()|| $upload->facultyProfiles()->exists()|| $upload->noticeboards()->exists(); // check if used by any other model
            if (!$stillUsed) {
                if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                    Storage::disk('public')->delete($upload->path);
                }
                $upload->delete();
            }
        }
    });

    return redirect(route('admin.admission.index', ['tab' => 'admission_post']))
        ->with('success', 'Admission post deleted successfully');

    
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) {
            return [];
        }

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn($path) => ltrim(substr($path, strlen('/storage/')), '/'))
            ->values()
            ->all();
    }
}
