import { Head, <PERSON> } from '@inertiajs/react';
import html2canvas from 'html2canvas-pro';
import { jsPDF } from 'jspdf';
import { useEffect, useState } from 'react';

// Define the props passed from your Laravel controller
interface Props {
    uid: string;
    department: string;
}

const ApplicationSuccess = ({ uid, department }: Props) => {
    const [intendedProgram, setIntendedProgram] = useState('');

    useEffect(() => {
        const programMap: { [key: string]: string } = {
            cse: 'BSc <PERSON>s (Professional) in CSE',
            bba: 'BBA',
            ece: 'BSc Hons (Professional) in ECE',
            biochemistry: 'BSc Hons in Biochemistry and Molecular Biology',
        };
        setIntendedProgram(programMap[department] || 'Unknown Program');
    }, [department]);

    const handleSaveAsPDF = () => {
        const element = document.getElementById('content-to-save');
        if (element) {
            html2canvas(element).then((canvas) => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'px', 'a4');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
                pdf.save(`${uid}-Application.pdf`);
            });
        }
    };

    const handleSaveAsImage = () => {
        const element = document.getElementById('content-to-save');
        if (element) {
            html2canvas(element).then((canvas) => {
                const link = document.createElement('a');
                link.href = canvas.toDataURL('image/png');
                link.download = `${uid}-Application.png`;
                link.click();
            });
        }
    };

    return (
        <>
            <Head title="Application Submitted" />
            <div className="flex min-h-screen flex-col items-center justify-center space-y-8 bg-gray-50 p-4">
                {/* Top content block to be saved */}
                <div id="content-to-save" className="w-full max-w-lg rounded-lg bg-white p-6 text-center shadow-lg">
                    <h2 className="mt-4 pb-4 text-2xl font-semibold text-gray-800">Thank you for your application!</h2>
                    <img src="/images/NIST-logo.png" alt="logo" className="mx-auto h-20" />
                    <p className="mt-4 text-lg text-gray-700">
                        <strong>Your Application ID:</strong>
                        <span className="ml-2 font-bold text-green-700">{uid}</span>
                    </p>
                    <p className="text-gray-600">
                        <strong>Intended Program:</strong> {intendedProgram}
                    </p>
                    <p className="mt-4 text-justify text-lg text-gray-500">
                        National Institute of Science & Technology, affiliated with the national university brings the best degrees in BBA, CSE, ECE,
                        Biochemistry and Molecular Biology with the help of the brightest minds from home and abroad for grater job placement and
                        career opportunists.
                    </p>
                </div>

                {/* Bottom content block with instructions and buttons */}
                <div className="w-full max-w-lg rounded-lg bg-white p-6 text-center shadow-lg">
                    <p className="text-gray-600">Please save your application ID for the on-campus admission process.</p>
                    <p className="mt-2 text-gray-600">Our representatives will contact you soon for further details.</p>
                    <div className="mt-6 flex flex-col gap-4 sm:flex-row sm:justify-around">
                        <Link href="/" className="flex-1 rounded-md bg-blue-600 px-4 py-2.5 text-white transition hover:bg-blue-700">
                            Back to Home
                        </Link>
                        <button
                            onClick={handleSaveAsPDF}
                            className="flex-1 rounded-md bg-green-600 px-2 py-2.5 text-white transition hover:bg-green-700"
                        >
                            Save as PDF
                        </button>
                        <button
                            onClick={handleSaveAsImage}
                            className="flex-1 rounded-md bg-green-600 px-2 py-2.5 text-white transition hover:bg-green-700"
                        >
                            Save as Image
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ApplicationSuccess;
