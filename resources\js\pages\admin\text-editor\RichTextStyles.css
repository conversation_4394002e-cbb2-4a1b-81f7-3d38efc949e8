
/* =============================    
   RICH TEXT EDITOR OUTPUT STYLES
   ============================= */

   /* =============================
   RICH TEXT EDITOR OUTPUT STYLES
   ============================= */
.richtext-output {
  font-family: "Poppins", "Inter", "Segoe UI", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: hsl(var(--foreground, 210 10% 20%));
  word-wrap: break-word;
}

/* Headings */
.richtext-output h1.heading-node,
.richtext-output h2.heading-node,
.richtext-output h3.heading-node {
  font-weight: 600;
  line-height: 1.3;
  margin: 1em 0 0.5em;
  color: hsl(var(--foreground, 210 15% 15%));
}

.richtext-output h1.heading-node {
  font-size: 1.6rem;
  border-bottom: 2px solid hsl(var(--border, 210 15% 90%));
  padding-bottom: 0.3em;
}

.richtext-output h2.heading-node {
  font-size: 1.35rem;
}

.richtext-output h3.heading-node {
  font-size: 1.25rem;
}

/* Paragraphs & Text */
.richtext-output p.text-node {
  margin: 0.8em 0;
}

.richtext-output strong {
  font-weight: 600;
}

.richtext-output em {
  font-style: italic;
}

.richtext-output code.inline {
  background: hsl(var(--muted, 210 20% 96%));
  color: hsl(var(--accent, 210 60% 40%));
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: "Fira Code", monospace;
}

/* Lists */
.richtext-output ul.list-node,
.richtext-output ol.list-node {
  margin: 1em 0 1em 1.5em;
  padding: 0;
  line-height: 1em;
}

.richtext-output ul.list-node {
  list-style: disc;
}

.richtext-output ol.list-node {
  list-style: decimal;
}

.richtext-output li {
  margin: 0em 0;
}

/* Links */
.richtext-output a.link {
  color: hsl(var(--link, 220 80% 45%));
  text-decoration: underline;
  transition: color 0.2s;
}

.richtext-output a.link:hover {
  color: hsl(var(--link-hover, 220 90% 35%));
}

/* Images */
.richtext-output img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

/* Horizontal Rule */
.richtext-output hr {
  border: none;
  border-top: 2px solid #dcdcdc; /* Bootstrap-style green */
  margin: 1em 0;
}

/* Blockquote */
.richtext-output blockquote.block-node {
  border-left: 3px solid #ccc;
  padding-left: 1em;
  margin: 1em 0;
  color: hsl(var(--foreground, 210 10% 30%));
  font-style: italic;
  background: hsl(var(--muted, 210 20% 97%));
  
}
