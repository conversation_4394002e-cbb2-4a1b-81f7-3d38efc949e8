import React, { useState, useEffect } from 'react';
import { Head, useForm, usePage } from '@inertiajs/react';
import { toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight, Download, RotateCcw, Save, Settings } from 'lucide-react';
import { format } from 'date-fns';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Site Settings',
        href: '/admin/site-settings',
    },
];

interface Flash {
    success?: string;
    error?: string;
}

interface SiteSettings {
    youtubeLink: string;
    primaryPhone: string;
    secondaryPhone: string;
    email: string;
    facebook: string;
    youtube: string;
    linkedin: string;
    address: string;
    footerMenu1Heading: string;
    footerMenu1Paragraph: string;
}

interface SiteSettingsPageProps {
    settings: SiteSettings;
    hasBackup: boolean;
    backupDate: string | null;
}

export default function SiteSettingsIndex({ settings, hasBackup, backupDate }: SiteSettingsPageProps) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const [isExpanded, setIsExpanded] = useState(false);
    const [isJsonMode, setIsJsonMode] = useState(false);
    const [jsonValue, setJsonValue] = useState('');

    const { data, setData, put, processing, errors, reset } = useForm<SiteSettings>(settings);

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash]);

    useEffect(() => {
        setJsonValue(JSON.stringify(data, null, 2));
    }, [data]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (isJsonMode) {
            try {
                const parsedData = JSON.parse(jsonValue);
                put(route('admin.site-settings.update'), {
                    data: parsedData,
                    onSuccess: () => {
                        toast.success('Settings updated successfully');
                        setIsJsonMode(false);
                    },
                });
            } catch (error) {
                toast.error('Invalid JSON format');
            }
        } else {
            put(route('admin.site-settings.update'), {
                onSuccess: () => {
                    toast.success('Settings updated successfully');
                },
            });
        }
    };

    const handleDownload = () => {
        window.open(route('admin.site-settings.download'), '_blank');
    };

    const handleRestore = () => {
        if (confirm('Are you sure you want to restore from backup? This will overwrite current settings.')) {
            put(route('admin.site-settings.restore'), {
                onSuccess: () => {
                    toast.success('Settings restored from backup');
                },
            });
        }
    };

    const handleJsonChange = (value: string) => {
        setJsonValue(value);
        try {
            const parsedData = JSON.parse(value);
            setData(parsedData);
        } catch (error) {
            // Invalid JSON, don't update form data
        }
    };

    const settingsFields = [
        { key: 'youtubeLink', label: 'YouTube Embed Link', type: 'url', placeholder: 'https://youtube.com/embed/xyz' },
        { key: 'primaryPhone', label: 'Primary Phone', type: 'tel', placeholder: '+880123456789' },
        { key: 'secondaryPhone', label: 'Secondary Phone', type: 'tel', placeholder: '+880987654321' },
        { key: 'email', label: 'Email', type: 'email', placeholder: '<EMAIL>' },
        { key: 'facebook', label: 'Facebook URL', type: 'url', placeholder: 'https://facebook.com/example' },
        { key: 'youtube', label: 'YouTube Channel URL', type: 'url', placeholder: 'https://youtube.com/example' },
        { key: 'linkedin', label: 'LinkedIn URL', type: 'url', placeholder: 'https://linkedin.com/company/example' },
        { key: 'address', label: 'Address', type: 'textarea', placeholder: '123 Main Street, Dhaka, Bangladesh' },
        { key: 'footerMenu1Heading', label: 'Footer Menu 1 Heading', type: 'text', placeholder: 'Quick Links' },
        { key: 'footerMenu1Paragraph', label: 'Footer Menu 1 Paragraph', type: 'textarea', placeholder: 'This is a short description for footer.' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Site Settings" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-2 md:p-4">
                <div className="rounded border p-4 md:p-6 shadow-xl">
                    <div className="mb-6">
                        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                            <div>
                                <h1 className="text-xl md:text-2xl font-bold flex items-center gap-2">
                                    <Settings className="h-6 w-6" />
                                    Site Settings Management
                                </h1>
                                <p className="text-sm md:text-base text-gray-600">
                                    Manage static site content and configuration settings
                                </p>
                            </div>
                            <div className="flex flex-col gap-2 md:flex-row">
                                <Button 
                                    variant="outline" 
                                    onClick={handleDownload}
                                    className="w-full md:w-auto"
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    Download JSON
                                </Button>
                                {hasBackup && (
                                    <Button 
                                        variant="outline" 
                                        onClick={handleRestore}
                                        className="w-full md:w-auto"
                                    >
                                        <RotateCcw className="mr-2 h-4 w-4" />
                                        Restore Backup
                                    </Button>
                                )}
                            </div>
                        </div>
                        
                        {hasBackup && backupDate && (
                            <div className="mt-4">
                                <Badge variant="secondary" className="text-xs">
                                    Last backup: {format(new Date(backupDate), 'MMM dd, yyyy HH:mm')}
                                </Badge>
                            </div>
                        )}
                    </div>

                    <Card>
                        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                            <CollapsibleTrigger asChild>
                                <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                                    <CardTitle className="flex items-center justify-between">
                                        <span className="flex items-center gap-2">
                                            <Settings className="h-5 w-5" />
                                            Site Settings
                                        </span>
                                        {isExpanded ? (
                                            <ChevronDown className="h-5 w-5" />
                                        ) : (
                                            <ChevronRight className="h-5 w-5" />
                                        )}
                                    </CardTitle>
                                </CardHeader>
                            </CollapsibleTrigger>
                            
                            <CollapsibleContent>
                                <CardContent className="pt-6">
                                    <hr />
                                    <form onSubmit={handleSubmit} className="pt-2 space-y-6">
                                        <div className="flex flex-col gap-2 md:flex-row md:justify-between md:items-center">
                                            <div className="flex gap-2">
                                                <Button
                                                    type="button"
                                                    variant={!isJsonMode ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => setIsJsonMode(false)}
                                                >
                                                    Form Mode
                                                </Button>
                                                <Button
                                                    type="button"
                                                    variant={isJsonMode ? "default" : "outline"}
                                                    size="sm"
                                                    onClick={() => setIsJsonMode(true)}
                                                >
                                                    JSON Mode
                                                </Button>
                                            </div>
                                            <Button type="submit" disabled={processing} className="w-full md:w-auto">
                                                <Save className="mr-2 h-4 w-4" />
                                                {processing ? 'Saving...' : 'Save Settings'}
                                            </Button>
                                        </div>

                                        {isJsonMode ? (
                                            <div className="space-y-2">
                                                <Label htmlFor="json-editor">JSON Schema Editor</Label>
                                                <Textarea
                                                    id="json-editor"
                                                    value={jsonValue}
                                                    onChange={(e) => handleJsonChange(e.target.value)}
                                                    rows={20}
                                                    className="font-mono text-sm"
                                                    placeholder="Enter JSON configuration..."
                                                />
                                            </div>
                                        ) : (
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                {settingsFields.map((field) => (
                                                    <div key={field.key} className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
                                                        <Label htmlFor={field.key}>{field.label}</Label>
                                                        {field.type === 'textarea' ? (
                                                            <Textarea
                                                                id={field.key}
                                                                value={data[field.key as keyof SiteSettings] || ''}
                                                                onChange={(e) => setData(field.key as keyof SiteSettings, e.target.value)}
                                                                placeholder={field.placeholder}
                                                                rows={3}
                                                            />
                                                        ) : (
                                                            <Input
                                                                id={field.key}
                                                                type={field.type}
                                                                value={data[field.key as keyof SiteSettings] || ''}
                                                                onChange={(e) => setData(field.key as keyof SiteSettings, e.target.value)}
                                                                placeholder={field.placeholder}
                                                            />
                                                        )}
                                                        {errors[field.key as keyof SiteSettings] && (
                                                            <p className="text-sm text-red-600 mt-1">
                                                                {errors[field.key as keyof SiteSettings]}
                                                            </p>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </form>
                                </CardContent>
                            </CollapsibleContent>
                        </Collapsible>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
