<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ImageSlider extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'image',
        'heading',
        'caption',
        'order',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Scope to get images ordered by display order
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc');
    }
}
