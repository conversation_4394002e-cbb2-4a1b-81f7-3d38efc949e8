<?php

// database/migrations/2025_08_22_000100_update_news_table_for_seo.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Drop FK then column (often safer in separate calls)
        Schema::table('news', function (Blueprint $table) {
            if (Schema::hasColumn('news', 'user_id')) {
                $table->dropForeign(['user_id']); // news_user_id_foreign
            }
        });
        Schema::table('news', function (Blueprint $table) {
            if (Schema::hasColumn('news', 'user_id')) {
                $table->dropColumn('user_id');
            }
        });

        Schema::table('news', function (Blueprint $table) {
            // content to longText
            $table->longText('content')->change();
            // SEO + excerpt
            $table->string('meta_tag_title')->nullable();
            $table->string('meta_tag_description', 255)->nullable();
            $table->text('excerpt')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('news', function (Blueprint $table) {
            // revert longText back to text
            $table->text('content')->change();
            $table->dropColumn(['meta_tag_title', 'meta_tag_description', 'excerpt']);
        });

        Schema::table('news', function (Blueprint $table) {
            // restore user_id (nullable for safety on rollback)
            $table->foreignId('user_id')->nullable()->constrained()->cascadeOnDelete();
        });
    }
};

