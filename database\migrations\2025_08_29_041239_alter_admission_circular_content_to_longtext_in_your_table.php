<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admission_posts', function (Blueprint $table) {
            //
            $table->longText('admission_circular_content')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admission_posts', function (Blueprint $table) {
            //
            $table->text('admission_circular_content')->nullable()->change();
        });
    }
};
