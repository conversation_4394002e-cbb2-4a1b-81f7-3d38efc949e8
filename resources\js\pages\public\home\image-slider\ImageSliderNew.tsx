import React, { useState } from 'react';
import { Gr<PERSON><PERSON><PERSON>, GrPrevious } from 'react-icons/gr';

interface ImageSliderProps {
    imageSliders: {
        data: { image: string; heading: string; caption: string }[];
    };
}

const ImageSlider: React.FC<ImageSliderProps> = ({ imageSliders }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    const handlePrevClick = () => {
        setCurrentIndex((prevIndex) => (prevIndex === 0 ? imageSliders.data.length - 1 : prevIndex - 1));
    };

    const handleNextClick = () => {
        setCurrentIndex((prevIndex) => (prevIndex === imageSliders.data.length - 1 ? 0 : prevIndex + 1));
    };

    const handleDotClick = (index: number) => {
        setCurrentIndex(index);
    };

    return (
        <div className="relative w-full overflow-hidden">
            {/* Wrapper */}
            <div className="flex transition-transform duration-700 ease-in-out" style={{ transform: `translateX(-${currentIndex * 100}%)` }}>
                {imageSliders.data.map((slide, index) => (
                    <div key={index} className="relative w-full flex-shrink-0 sm:aspect-video md:h-[500px] lg:h-[530px]">
                        {/* Background Image */}
                        <img src={slide.image} alt={slide.heading} className="h-full w-full object-cover object-center" loading="lazy" />

                        {/* Overlay Content */}
                      

                        {/*sol1<div className="absolute bottom-0 left-0 w-full bg-black/50 p-3 text-white sm:p-4 md:p-6">
                            <h1 className="mb-1 text-sm font-semibold sm:text-base md:text-2xl">{slide.heading}</h1>
                            <p className="text-xs leading-snug sm:text-sm md:text-base">{slide.caption}</p>
                        </div>*/}

                        <div className="absolute bottom-2 md:bottom-10 left-6  max-w-[90%] md:max-w-[75%] lg:max-w-[75%] rounded bg-black/60 p-3 text-white sm:max-w-[90%] md:left-16 md:p-6">
                            <h1 className="mb-1 text-sm font-semibold md:text-2xl">{slide.heading}</h1>
                            <p className="text-xs leading-snug md:text-sm">{slide.caption}</p>
                        </div>
                    
                    </div>
                ))}
            </div>

            {/* Navigation Buttons */}
            <button className="absolute top-1/2 left-2 -translate-y-1/2 rounded-full bg-black/50 p-2 hover:bg-black/70" onClick={handlePrevClick}>
                <GrPrevious size={22} color="#fff" />
            </button>
            <button className="absolute top-1/2 right-2 -translate-y-1/2 rounded-full bg-black/50 p-2 hover:bg-black/70" onClick={handleNextClick}>
                <GrNext size={22} color="#fff" />
            </button>

            {/* Dots */}
            <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 gap-2">
                {imageSliders.data.map((_, index) => (
                    <span
                        key={index}
                        onClick={() => handleDotClick(index)}
                        className={`h-3 w-3 cursor-pointer rounded-full transition ${
                            index === currentIndex ? 'scale-110 bg-green-500' : 'bg-white/60'
                        }`}
                    />
                ))}
            </div>
        </div>
    );
};

export default ImageSlider;
