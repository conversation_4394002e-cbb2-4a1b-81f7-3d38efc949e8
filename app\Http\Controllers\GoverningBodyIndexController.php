<?php

namespace App\Http\Controllers;
use App\Http\Controllers\Controller;
use App\Models\GoverningBody;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Resources\GoverningbodyResource;

class GoverningBodyIndexController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        //
        $governingbodyQuery = GoverningBody::query()->orderBy('display_order', 'asc');
        if ($request->has('search') && $request->search !== null) {
            $search = $request->search;
            $governingbodyQuery->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('designation', 'like', "%{$search}%");
            });
        }
        $governingbody = $governingbodyQuery
            ->paginate(10)
            ->withQueryString();



        return Inertia::render('admin/governingbody/index', [
            'governingbodyData' => GoverningbodyResource::collection($governingbody),
            'filters' => $request->only(['search']),
        ]);
    }


}


