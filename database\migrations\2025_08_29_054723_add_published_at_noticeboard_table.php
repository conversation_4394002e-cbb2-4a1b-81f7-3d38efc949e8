<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('noticeboards', function (Blueprint $table) {
            // Drop user_id column
            
            if (Schema::hasColumn('noticeboards', 'user_id')) {
                $table->dropForeign(['user_id']);
                $table->dropColumn('user_id');
            }

            // Add published_at column
             if (!Schema::hasColumn('noticeboards', 'published_at')) {
                $table->timestamp('published_at')->nullable()->after('attachment');
            }
            
            if (!Schema::hasColumn('noticeboards', 'active_status')) {
                $table->enum('active_status', ['active', 'inactive'])->default('active')->after('published_at');
            }

            // Add active_status column
           

            // Optional: convert content column to longText if needed
            $table->longText('content')->change();
        });
    }

    public function down(): void
    {
        Schema::table('noticeboards', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->nullable()->after('updated_at');

            $table->dropColumn(['published_at', 'active_status']);

            $table->text('content')->change();
        });
    }
};
