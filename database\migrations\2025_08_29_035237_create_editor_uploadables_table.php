<?php
// database/migrations/2025_08_25_000400_create_editor_uploadables_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('editor_uploadables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('editor_upload_id')
                ->constrained('editor_uploads')
                ->cascadeOnDelete();

            $table->morphs('uploadable'); // uploadable_type + uploadable_id
            $table->timestamps();

            // Optional: prevent duplicates
            $table->unique(
                ['editor_upload_id', 'uploadable_type', 'uploadable_id'],
                'editor_uploadables_unique'
            );
        });

        /**
         * ⚡ Transition strategy:
         * If you already have `uploadable_type` and `uploadable_id`
         * on `editor_uploads` (from your previous migration),
         * backfill them into the new pivot.
         */
        if (Schema::hasTable('editor_uploads')) {
            DB::statement("
                INSERT INTO editor_uploadables (editor_upload_id, uploadable_type, uploadable_id, created_at, updated_at)
                SELECT id, uploadable_type, uploadable_id, NOW(), NOW()
                FROM editor_uploads
                WHERE uploadable_type IS NOT NULL AND uploadable_id IS NOT NULL
            ");
        }

        // Optionally: drop the old single morph columns
        Schema::table('editor_uploads', function (Blueprint $table) {
            if (Schema::hasColumn('editor_uploads', 'uploadable_type')) {
                $table->dropColumn(['uploadable_type', 'uploadable_id']);
            }
        });
    }

    public function down(): void
    {
        // Restore morph directly on uploads (single relation only)
        Schema::table('editor_uploads', function (Blueprint $table) {
            $table->nullableMorphs('uploadable');
        });

        // Best-effort: copy back one relation per upload
        DB::statement("
            UPDATE editor_uploads eu
            JOIN (
                SELECT editor_upload_id, uploadable_type, uploadable_id
                FROM editor_uploadables
                GROUP BY editor_upload_id
            ) t ON eu.id = t.editor_upload_id
            SET eu.uploadable_type = t.uploadable_type, eu.uploadable_id = t.uploadable_id
        ");

        Schema::dropIfExists('editor_uploadables');
    }
};
