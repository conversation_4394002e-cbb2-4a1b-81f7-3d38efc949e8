import React, { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import Pagination from '@/components/pagination';
import InputError from '@/components/input-error';
import StatusToggle from '@/components/ui/status-toggle';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';

interface AdmissionPromoImageData {
    id: number;
    left_banner_image: string;
    right_banner_image_top: string;
    right_banner_image_top_caption: string;
    right_banner_image_top_subtitle: string;
    right_banner_image_bottom: string;
    right_banner_image_bottom_caption: string;
    right_banner_image_bottom_subtitle: string;
    status: 'active' | 'inactive';
}

interface PaginatedData {
    data: AdmissionPromoImageData[];
    meta: {
        links: Array<{ url: string | null; label: string; active: boolean }>;
        total: number;
        from: number;
        to: number;
    };
}

interface AdmissionPromoImageTabProps {
    data: PaginatedData;
}

export default function AdmissionPromoImageTab({ data }: AdmissionPromoImageTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<AdmissionPromoImageData | null>(null);
    const [viewingItem, setViewingItem] = useState<AdmissionPromoImageData | null>(null);

    const { data: formData, setData, post, processing, errors, reset } = useForm({
        left_banner_image: null as File | null,
        right_banner_image_top: null as File | null,
        right_banner_image_top_caption: '',
        right_banner_image_top_subtitle: '',
        right_banner_image_bottom: null as File | null,
        right_banner_image_bottom_caption: '',
        right_banner_image_bottom_subtitle: '',
        status: 'inactive' as 'active' | 'inactive',
    });

    const handleCreate = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.admission.promo-images.store'), {
            onSuccess: () => {
                setIsCreateOpen(false);
                reset();
            },
        });
    };

    const handleEdit = (item: AdmissionPromoImageData) => {
        setEditingItem(item);
        setData({
            left_banner_image: null,
            right_banner_image_top: null,
            right_banner_image_top_caption: item.right_banner_image_top_caption || '',
            right_banner_image_top_subtitle: item.right_banner_image_top_subtitle || '',
            right_banner_image_bottom: null,
            right_banner_image_bottom_caption: item.right_banner_image_bottom_caption || '',
            right_banner_image_bottom_subtitle: item.right_banner_image_bottom_subtitle || '',
            status: item.status,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(route('admin.admission.promo-images.update', editingItem.id), {
                _method: 'put',
                ...formData,
            }, {
                onSuccess: () => {
                    setIsEditOpen(false);
                    setEditingItem(null);
                    reset();
                },
            });
        }
    };

    const handleDelete = async (id: number) => {
        const confirmed = await showConfirmation({
            title: 'Delete Promo Image',
            description: 'Are you sure you want to delete this promo image? This action cannot be undone.',
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.promo-images.destroy', id));
        }
    };

    const handleView = (item: AdmissionPromoImageData) => {
        setViewingItem(item);
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Admission Promo Images</h2>
                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Promo Images
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Create Admission Promo Images</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="col-span-2">
                                    <Label htmlFor="left_banner_image">Left Banner Image *</Label>
                                    <Input
                                        id="left_banner_image"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('left_banner_image', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.left_banner_image} />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_top">Right Banner Top *</Label>
                                    <Input
                                        id="right_banner_image_top"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('right_banner_image_top', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.right_banner_image_top} />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_bottom">Right Banner Bottom *</Label>
                                    <Input
                                        id="right_banner_image_bottom"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('right_banner_image_bottom', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.right_banner_image_bottom} />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_top_caption">Top Caption</Label>
                                    <Input
                                        id="right_banner_image_top_caption"
                                        value={formData.right_banner_image_top_caption}
                                        onChange={(e) => setData('right_banner_image_top_caption', e.target.value)}
                                        placeholder="Enter top caption"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_top_subtitle">Top Subtitle</Label>
                                    <Input
                                        id="right_banner_image_top_subtitle"
                                        value={formData.right_banner_image_top_subtitle}
                                        onChange={(e) => setData('right_banner_image_top_subtitle', e.target.value)}
                                        placeholder="Enter top subtitle"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_bottom_caption">Bottom Caption</Label>
                                    <Input
                                        id="right_banner_image_bottom_caption"
                                        value={formData.right_banner_image_bottom_caption}
                                        onChange={(e) => setData('right_banner_image_bottom_caption', e.target.value)}
                                        placeholder="Enter bottom caption"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="right_banner_image_bottom_subtitle">Bottom Subtitle</Label>
                                    <Input
                                        id="right_banner_image_bottom_subtitle"
                                        value={formData.right_banner_image_bottom_subtitle}
                                        onChange={(e) => setData('right_banner_image_bottom_subtitle', e.target.value)}
                                        placeholder="Enter bottom subtitle"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="status">Status *</Label>
                                    <Select value={formData.status} onValueChange={(value) => setData('status', value as 'active' | 'inactive')}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                            <SelectItem value="active">Active</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.status} />
                                </div>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Create
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Left Banner</TableHead>
                                <TableHead>Right Top</TableHead>
                                <TableHead>Right Bottom</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        <img src={item.left_banner_image} alt="Left Banner" className="w-16 h-16 object-cover rounded" />
                                    </TableCell>
                                    <TableCell>
                                        <img src={item.right_banner_image_top} alt="Right Top" className="w-16 h-16 object-cover rounded" />
                                    </TableCell>
                                    <TableCell>
                                        <img src={item.right_banner_image_bottom} alt="Right Bottom" className="w-16 h-16 object-cover rounded" />
                                    </TableCell>
                                    <TableCell>
                                        <StatusToggle
                                            id={item.id}
                                            currentStatus={item.status}
                                            statusField="status"
                                            updateRoute={route('admin.admission.promo-images.update', item.id)}
                                            activeLabel="Active"
                                            inactiveLabel="Inactive"
                                            confirmTitle="Change Promo Image Status"
                                            confirmDescription="Are you sure you want to change the status of this promo image?"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Edit Admission Promo Images</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-6">
                        {/* Left Banner Image Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Left Banner Image</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_left_banner_image">Replace Left Banner Image</Label>
                                    <Input
                                        id="edit_left_banner_image"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('left_banner_image', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.left_banner_image} />
                                    {formData.left_banner_image && (
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-600 mb-2">New Image Preview:</p>
                                            <img
                                                src={URL.createObjectURL(formData.left_banner_image)}
                                                alt="New Left Banner Preview"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Label>Current Left Banner Image</Label>
                                    {editingItem?.left_banner_image && (
                                        <div className="mt-2">
                                            <img
                                                src={editingItem.left_banner_image}
                                                alt="Current Left Banner"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Right Banner Top Image Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Right Banner Top Image</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_right_banner_image_top">Replace Right Banner Top Image</Label>
                                    <Input
                                        id="edit_right_banner_image_top"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('right_banner_image_top', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.right_banner_image_top} />
                                    {formData.right_banner_image_top && (
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-600 mb-2">New Image Preview:</p>
                                            <img
                                                src={URL.createObjectURL(formData.right_banner_image_top)}
                                                alt="New Right Top Banner Preview"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Label>Current Right Banner Top Image</Label>
                                    {editingItem?.right_banner_image_top && (
                                        <div className="mt-2">
                                            <img
                                                src={editingItem.right_banner_image_top}
                                                alt="Current Right Top Banner"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_right_banner_image_top_caption">Top Caption</Label>
                                    <Input
                                        id="edit_right_banner_image_top_caption"
                                        value={formData.right_banner_image_top_caption}
                                        onChange={(e) => setData('right_banner_image_top_caption', e.target.value)}
                                        placeholder="Enter top caption"
                                    />
                                    <InputError message={errors.right_banner_image_top_caption} />
                                </div>
                                <div>
                                    <Label htmlFor="edit_right_banner_image_top_subtitle">Top Subtitle</Label>
                                    <Input
                                        id="edit_right_banner_image_top_subtitle"
                                        value={formData.right_banner_image_top_subtitle}
                                        onChange={(e) => setData('right_banner_image_top_subtitle', e.target.value)}
                                        placeholder="Enter top subtitle"
                                    />
                                    <InputError message={errors.right_banner_image_top_subtitle} />
                                </div>
                            </div>
                        </div>

                        {/* Right Banner Bottom Image Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Right Banner Bottom Image</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_right_banner_image_bottom">Replace Right Banner Bottom Image</Label>
                                    <Input
                                        id="edit_right_banner_image_bottom"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => setData('right_banner_image_bottom', e.target.files?.[0] || null)}
                                    />
                                    <InputError message={errors.right_banner_image_bottom} />
                                    {formData.right_banner_image_bottom && (
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-600 mb-2">New Image Preview:</p>
                                            <img
                                                src={URL.createObjectURL(formData.right_banner_image_bottom)}
                                                alt="New Right Bottom Banner Preview"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Label>Current Right Banner Bottom Image</Label>
                                    {editingItem?.right_banner_image_bottom && (
                                        <div className="mt-2">
                                            <img
                                                src={editingItem.right_banner_image_bottom}
                                                alt="Current Right Bottom Banner"
                                                className="w-32 h-32 object-cover rounded border"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="edit_right_banner_image_bottom_caption">Bottom Caption</Label>
                                    <Input
                                        id="edit_right_banner_image_bottom_caption"
                                        value={formData.right_banner_image_bottom_caption}
                                        onChange={(e) => setData('right_banner_image_bottom_caption', e.target.value)}
                                        placeholder="Enter bottom caption"
                                    />
                                    <InputError message={errors.right_banner_image_bottom_caption} />
                                </div>
                                <div>
                                    <Label htmlFor="edit_right_banner_image_bottom_subtitle">Bottom Subtitle</Label>
                                    <Input
                                        id="edit_right_banner_image_bottom_subtitle"
                                        value={formData.right_banner_image_bottom_subtitle}
                                        onChange={(e) => setData('right_banner_image_bottom_subtitle', e.target.value)}
                                        placeholder="Enter bottom subtitle"
                                    />
                                    <InputError message={errors.right_banner_image_bottom_subtitle} />
                                </div>
                            </div>
                        </div>

                        {/* Status Section */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Status</h3>
                            <div>
                                <Label htmlFor="edit_status">Status *</Label>
                                <Select value={formData.status} onValueChange={(value) => setData('status', value as 'active' | 'inactive')}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.status} />
                            </div>
                        </div>

                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>View Admission Promo Images</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-6">
                            {/* Status */}
                            <div>
                                <Label>Status</Label>
                                <div className="mt-1">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        viewingItem.status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {viewingItem.status === 'active' ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>

                            {/* Left Banner */}
                            <div className="space-y-2">
                                <Label>Left Banner Image</Label>
                                <img
                                    src={viewingItem.left_banner_image}
                                    alt="Left Banner"
                                    className="w-full max-w-md h-auto rounded border object-cover"
                                />
                            </div>

                            {/* Right Top Banner */}
                            <div className="space-y-2">
                                <Label>Right Top Banner</Label>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <img
                                        src={viewingItem.right_banner_image_top}
                                        alt="Right Top Banner"
                                        className="w-full h-auto rounded border object-cover"
                                    />
                                    <div className="space-y-2">
                                        <div>
                                            <Label className="text-sm">Caption</Label>
                                            <p className="text-sm">{viewingItem.right_banner_image_top_caption || 'No caption'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm">Subtitle</Label>
                                            <p className="text-sm">{viewingItem.right_banner_image_top_subtitle || 'No subtitle'}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Right Bottom Banner */}
                            <div className="space-y-2">
                                <Label>Right Bottom Banner</Label>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <img
                                        src={viewingItem.right_banner_image_bottom}
                                        alt="Right Bottom Banner"
                                        className="w-full h-auto rounded border object-cover"
                                    />
                                    <div className="space-y-2">
                                        <div>
                                            <Label className="text-sm">Caption</Label>
                                            <p className="text-sm">{viewingItem.right_banner_image_bottom_caption || 'No caption'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm">Subtitle</Label>
                                            <p className="text-sm">{viewingItem.right_banner_image_bottom_subtitle || 'No subtitle'}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
