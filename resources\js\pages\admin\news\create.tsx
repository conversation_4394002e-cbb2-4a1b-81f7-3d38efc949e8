import InputError from '@/components/input-error';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { Content } from '@tiptap/react';
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { ChangeEvent, FormEventHandler, useEffect, useState } from 'react';
import '../text-editor/RichTextStyles.css';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'News',
        href: route('admin.news.index'),
    },
    {
        title: 'Create News',
        href: route('admin.news.create'),
    },
];

type PageProps = {
    draftToken: string;
};

type NewsForm = {
    title: string;
    content: string;
    image: File | null;
    meta_tag_title: string;
    meta_tag_description: string;
    excerpt: string;
    draft_token: string | null;
    published_date: string;
    active_status: string;
};

export default function NewsCreate() {
    const { draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);
    const [publishedDate, setPublishedDate] = useState<Date | undefined>(new Date());

    const { data, setData, post, processing, errors } = useForm<NewsForm>({
        title: '',
        content: '',
        image: null as File | null,
        meta_tag_title: '',
        meta_tag_description: '',
        excerpt: '',
        draft_token: draftToken || null,
        published_date: '',
        active_status: 'active',
    });

    useEffect(() => {
        setData('published_date', format(new Date(), 'yyyy-MM-dd'));
    }, []);

    const submit: FormEventHandler = (e) => {
        console.log('news data before submitting', data);
        e.preventDefault();
        post(route('admin.news.store'));
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('image', file);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create News" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create News</div>
                        <Button>
                            <Link href={route('admin.news.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form className="space-y-6" onSubmit={submit}>
                                <div className="grid gap-6">
                                    <div className="grid gap-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            type="text"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            placeholder="Enter news title"
                                        />
                                        <InputError message={errors.title} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="image">Featured Image *</Label>
                                        <Input id="image" type="file" accept="image/*" onChange={handleFileChange} />
                                        {data.image && (
                                            <img src={URL.createObjectURL(data.image)} alt="Preview" className="h-20 w-20 rounded object-cover" />
                                        )}
                                        <InputError message={errors.image} />
                                    </div>
                                   
                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="content">Content *</Label>
                                        <MinimalTiptapEditor
                                            value={value}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setData('content', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                    </div>
                                    <div className="grid gap-2">
                                        Preview:
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (value as string) || '' }} />
                                    </div>
                                    <InputError message={errors.content} />
                                    <hr />
                                    <div className="grid gap-2">
                                        <h2><b>SEO Content</b></h2>
                                    </div>
                                    <hr />
                                    <div className="grid gap-2">
                                        <Label htmlFor="excerpt">Summary (Excerpt)</Label>
                                        <Textarea
                                            id="excerpt"
                                            value={data.excerpt}
                                            onChange={(e) => setData('excerpt', e.target.value)}
                                            placeholder="Enter news excerpt"
                                            rows={3}
                                        />
                                        <InputError message={errors.excerpt} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="meta_tag_title">Meta Tag Title</Label>
                                        <Input
                                            id="meta_tag_title"
                                            type="text"
                                            value={data.meta_tag_title}
                                            onChange={(e) => setData('meta_tag_title', e.target.value)}
                                            placeholder="Enter meta tag title"
                                        />
                                        <InputError message={errors.meta_tag_title} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="meta_tag_description">Meta Tag Description</Label>
                                        <Textarea
                                            id="meta_tag_description"
                                            value={data.meta_tag_description}
                                            onChange={(e) => setData('meta_tag_description', e.target.value)}
                                            placeholder="Enter meta tag description"
                                            rows={3}
                                        />
                                        <InputError message={errors.meta_tag_description} />
                                    </div>
                                    <div>
                                        <Label htmlFor="published_date">Published Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        'w-full justify-start text-left font-normal',
                                                        !publishedDate && 'text-muted-foreground',
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedDate ? format(publishedDate, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedDate}
                                                    onSelect={(date) => {
                                                        setPublishedDate(date);
                                                        setData('published_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                    captionLayout="dropdown"
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.published_date} />
                                    </div>
                                  
                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline">
                                            <Link href={route('admin.news.index')} prefetch>
                                                Cancel
                                            </Link>
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            {processing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Create News
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
