<?php

namespace App\Http\Controllers;

use App\Http\Resources\AdmissionPostResource;
use App\Models\AdmissionPost;
use Inertia\Inertia;
use Illuminate\Support\Str;

class AdmissionPostEditController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(AdmissionPost $admissionPost)
    {
        $draftToken = Str::uuid()->toString();

        return Inertia::render('admin/admission/admission-post/edit', [
            'currentAdmissionPost' => new AdmissionPostResource($admissionPost),
            'draftToken' => $draftToken,
        ]);
    }
}
