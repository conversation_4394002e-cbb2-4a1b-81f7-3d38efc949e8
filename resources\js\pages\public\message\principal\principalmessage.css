.profile-container {
    width: 85%;
    margin: 0 auto;
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .profile-main {
    display: flex;
    flex-direction: row;
    width: 100%;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .profile-image img {
    /*width: 100%;*/
    max-width: 400px;
    border-radius: 10px;
  }
  
  .profile-content {
    flex-grow: 1;
  }
  
  .profile-content h2 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
  }
  
  .profile-content h3 {
    margin: 10px 0;
    font-size: 22px;
    color: #777;
  }
  
  .profile-content p {
    line-height: 1.6;
    margin: 15px 0;
    color: #000;
    text-align: justify;
  }
  
  .profile-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .profile-links a {
    color: #000;
    font-size: 20px;
  }
  
  .about-section {
    width: 100%;
    text-align: center;
  }
  
  .about-section h3 {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: bold;
  }
  
  .about-section p {
    line-height: 1.6;
    color: #000;
  }
  
  @media (max-width: 768px) {
    .profile-main {
      flex-direction: column;
      align-items: center;
    }
  
    .profile-content {
      text-align: center;
    }
  
    .profile-image img {
      max-width: 200px;
    }
  }
  