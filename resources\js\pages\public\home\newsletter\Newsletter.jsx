const Newsletter = () => {
    return (
        <section className="bg-[#0c1b2e] py-12 text-white">
            <div className="container mx-auto flex flex-col items-center gap-8 px-4 md:flex-row md:items-center">
                {/* Left Side Text (1/3 width on medium screens and up) */}
                <div className="w-full text-center md:w-1/3 md:text-left">
                    <h1 className="mb-2 text-3xl font-bold md:text-2xl">Newsletter</h1>
                    <span className="block text-lg text-gray-300">Subscribe to get the latest updates.</span>
                </div>

                {/* Right Side Form (2/3 width on medium screens and up) */}
                <div className="flex w-full flex-col gap-4 sm:flex-row md:w-2/3">
                    <input
                        type="email"
                        placeholder="Enter email address"
                        className="w-full flex-1 rounded-full border-none bg-white px-6 py-4 text-base text-gray-800 outline-none"
                        aria-label="Email Address"
                    />
                    <button
                        className="w-full rounded-full border border-transparent bg-white px-7 py-4 text-base font-semibold text-[#271a3d] uppercase shadow-lg transition-all duration-300 hover:border-white hover:bg-[#0c1b2e] hover:text-white sm:w-auto"
                        type="submit"
                    >
                        Subscribe
                    </button>
                </div>
            </div>
        </section>
    );
};

export default Newsletter;
