import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { AdmissionPost, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Content } from '@tiptap/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admission Management',
        href: '/admin/admission',
    },
    {
        title: 'Edit Admission Post',
        href: '/admin/admission/posts/edit',
    },
];

type PageProps = {
    draftToken: string;
};

export default function AdmissionPostEdit({ currentAdmissionPost }: { currentAdmissionPost: AdmissionPost }) {
    const [title, setTitle] = useState<string>(currentAdmissionPost.title);
    const [admissionCircularContent, setAdmissionCircularContent] = useState<string>(currentAdmissionPost.admission_circular_content || '');
    const [activeStatus, setActiveStatus] = useState<'unpublished' | 'published'>(currentAdmissionPost.active_status);

    const [publishedDate, setPublishedDate] = useState<Date>(parseISO(currentAdmissionPost.published_date));
    const { errors, draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);
    
    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.admission.posts.update', currentAdmissionPost.id), {
            _method: 'put',
            title: title,
            admission_circular_content: admissionCircularContent,
            active_status: activeStatus,
            published_date: format(publishedDate, 'yyyy-MM-dd'),
            draft_token: draftToken,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Admission Post" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Admission Post</div>

                        <Button>
                            <Link href="/admin/admission?tab=admission_post" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            type="text"
                                            id="title"
                                            placeholder="Enter post title"
                                            value={title}
                                            onChange={(e) => setTitle(e.target.value)}
                                            aria-invalid={!!errors.title}
                                        />
                                        <InputError message={errors.title} />
                                    </div>

                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="admission_circular_content">Admission Circular Content *</Label>
                                        <MinimalTiptapEditor
                                            value={currentAdmissionPost.admission_circular_content}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setAdmissionCircularContent(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                        <InputError message={errors.content} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="content">Content Preview</Label>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: value }} />
                                        </div>
                                    </div>
                                    <hr />

                                    <div>
                                        <Label htmlFor="active_status">Status *</Label>
                                        <Select value={activeStatus} onValueChange={(value: 'unpublished' | 'published') => setActiveStatus(value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="unpublished">Unpublished</SelectItem>
                                                <SelectItem value="published">Published</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.active_status} />
                                    </div>

                                    <div>
                                        <Label htmlFor="published_date">Published Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        'w-full justify-start text-left font-normal',
                                                        !publishedDate && 'text-muted-foreground',
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedDate ? format(publishedDate, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedDate}
                                                    onSelect={(date) => {
                                                        if (date) setPublishedDate(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.published_date} />
                                    </div>
                                </div>

                                <div className="mt-6 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Admission Post</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
