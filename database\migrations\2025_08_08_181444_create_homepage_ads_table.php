<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('homepage_ads', function (Blueprint $table) {
            $table->id();
            $table->string('ad_title')->nullable();
            $table->string('program_info_title')->nullable();
            $table->string('session')->nullable();
            $table->string('offer_title')->nullable();
            $table->text('offer_text')->nullable();
            $table->enum('active_status', ['active', 'inactive'])->default('inactive');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('homepage_ads');
    }
};
