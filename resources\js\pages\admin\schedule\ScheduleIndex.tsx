import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { useConfirmation } from '@/contexts/confirmation-context';

// Shadcn/UI Components
import Pagination from '@/components/pagination'; // Your custom pagination component
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

// Icons
import TruncatedCell from '@/components/utils/TruncatedCell';
import debounce from 'lodash/debounce';
import { Pencil, Search, Trash2, Eye } from 'lucide-react';

// --- TYPE DEFINITIONS ---

// For flash messages from Laravel
interface Flash {
    success?: string;
    danger?: string;
}

// Data structure for a single Class Schedule item
interface ClassScheduleData {
    scheduleEmbedLink: string | null;
    id: number;
    department: string;
    session: string;
    batch: string;
}

// Data structure for a single Faculty Schedule item
interface FacultyScheduleData {
    scheduleEmbedLink: string | null | undefined;
    id: number;
    faculty_name: string;
    department: string;
}

// Generic paginated data structure for reusability
interface PaginatedData<T> {
    data: T[];
    meta: {
        total: number;
        from: number;
        to: number;
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
    };
}

// Props expected by the component from the Laravel Controller
interface SchedulePageProps {
    classSchedules: PaginatedData<ClassScheduleData>;
    facultySchedules: PaginatedData<FacultyScheduleData>;
    filters: {
        search_class?: string;
        search_faculty?: string;
    };
    activeTab: 'class_schedule' | 'faculty_schedule';
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Schedules',
        href: '/schedule',
    },
];

// --- REACT COMPONENT ---

export default function ScheduleIndex({ classSchedules, facultySchedules, filters, activeTab }: SchedulePageProps) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const { showConfirmation } = useConfirmation();
    const [viewingClassSchedule, setViewingClassSchedule] = useState<ClassScheduleData | null>(null);
    const [viewingFacultySchedule, setViewingFacultySchedule] = useState<FacultyScheduleData | null>(null);
    const [classSearchValue, setClassSearchValue] = useState<string>(filters.search_class || '');
    const [facultySearchValue, setFacultySearchValue] = useState<string>(filters.search_faculty || '');

    // Effect for showing toast notifications for success/error messages
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    // Sync search values with filters when they change
    useEffect(() => {
        setClassSearchValue(filters.search_class || '');
        setFacultySearchValue(filters.search_faculty || '');
    }, [filters.search_class, filters.search_faculty]);

    // --- SEARCH FUNCTIONALITY ---

    // Debounced search handler for Class Schedules
    const handleClassSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.schedule.index'), {
                tab: activeTab,
                search_class: query,
                search_faculty: facultySearchValue // Preserve faculty search
            }, { preserveState: true, replace: true });
        }, 300),
    ).current;

    // Debounced search handler for Faculty Schedules
    const handleFacultySearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.schedule.index'), {
                tab: activeTab,
                search_faculty: query,
                search_class: classSearchValue // Preserve class search
            }, { preserveState: true, replace: true });
        }, 300),
    ).current;

    // Handle class search input change
    const handleClassSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setClassSearchValue(value);
        handleClassSearch(value);
    };

    // Handle faculty search input change
    const handleFacultySearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setFacultySearchValue(value);
        handleFacultySearch(value);
    };

    // Cleanup debounce on unmount
    useEffect(() => {
        return () => {
            handleClassSearch.cancel();
            handleFacultySearch.cancel();
        };
    }, [handleClassSearch, handleFacultySearch]);

    const handleTabChange = (tab: 'class_schedule' | 'faculty_schedule') => {
        router.get(
            route('admin.schedule.index'),
            {
                tab,
                search_class: filters.search_class || '',
                search_faculty: filters.search_faculty || ''
            },
            {
                // --- THIS IS THE KEY ---
                // Specify which props to retrieve for a faster update
                only: ['activeTab', 'classSchedules', 'facultySchedules', 'filters'],
                preserveState: true,
                replace: true,
            },
        );
    };

    // View handlers
    const handleViewClassSchedule = (schedule: ClassScheduleData) => {
        setViewingClassSchedule(schedule);
    };

    const handleViewFacultySchedule = (schedule: FacultyScheduleData) => {
        setViewingFacultySchedule(schedule);
    };

    // Delete handlers with confirmation
    const handleDeleteClassSchedule = async (id: number, department: string, session: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Class Schedule',
            description: `Are you sure you want to delete the class schedule for ${department} - ${session}? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.schedule.class.destroy', id));
        }
    };

    const handleDeleteFacultySchedule = async (id: number, facultyName: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Faculty Schedule',
            description: `Are you sure you want to delete the schedule for ${facultyName}? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.schedule.faculty.destroy', id));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Manage Schedules" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Tabs value={activeTab} onValueChange={(newTab) => handleTabChange(newTab as 'class_schedule' | 'faculty_schedule')} className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="class_schedule">Class Schedule</TabsTrigger>
                        <TabsTrigger value="faculty_schedule">Faculty Schedule</TabsTrigger>
                    </TabsList>

                    {/* Tab 1: Class Schedule */}
                    <TabsContent value="class_schedule" className="space-y-4">
                        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
                            <div className="mb-4 flex items-center justify-between gap-4">
                                <div className="relative w-full max-w-sm">
                                    <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        type="search"
                                        placeholder="Search by department, session..."
                                        className="pl-8"
                                        value={classSearchValue}
                                        onChange={handleClassSearchChange}
                                    />
                                </div>
                                <Button asChild>
                                    <Link href={route('admin.schedule.class.create')}>Create Class Schedule</Link>
                                </Button>
                            </div>
                            <Card>
                                <CardContent className="p-0">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead className="w-[50px]">SL</TableHead>
                                                <TableHead>Department</TableHead>
                                                <TableHead>Session</TableHead>
                                                <TableHead>Batch</TableHead>
                                                <TableHead>Embed Link</TableHead>
                                                <TableHead className="w-[150px] text-right">Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {classSchedules.data.map((schedule, index) => (
                                                <TableRow key={schedule.id}>
                                                    <TableCell>{classSchedules.meta.from + index}</TableCell>
                                                    <TableCell>{schedule.department}</TableCell>
                                                    <TableCell>{schedule.session}</TableCell>
                                                    <TableCell>{schedule.batch}</TableCell>
                                                    <TableCell>
                                                        <TruncatedCell text={schedule.scheduleEmbedLink} maxLength={40} />
                                                    </TableCell>
                                                    <TableCell className="space-x-1 text-right">
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button variant="outline" size="icon" onClick={() => handleViewClassSchedule(schedule)}>
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>View</p>
                                                            </TooltipContent>
                                                        </Tooltip>

                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button variant="outline" size="icon" asChild>
                                                                    <Link href={route('admin.schedule.class.edit', schedule.id)}>
                                                                        <Pencil className="h-4 w-4" />
                                                                    </Link>
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Edit</p>
                                                            </TooltipContent>
                                                        </Tooltip>

                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="destructive"
                                                                    size="icon"
                                                                    onClick={() => handleDeleteClassSchedule(schedule.id, schedule.department, schedule.session)}
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Delete</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>
                            <div className="mt-4">
                                <Pagination meta={classSchedules.meta} />
                            </div>
                        </div>
                    </TabsContent>

                    {/* Tab 2: Faculty Schedule */}
                    <TabsContent value="faculty_schedule" className="space-y-4">
                        <div className="rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
                            <div className="mb-4 flex items-center justify-between gap-4">
                                <div className="relative w-full max-w-sm">
                                    <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        type="search"
                                        placeholder="Search by faculty name..."
                                        className="pl-8"
                                        value={facultySearchValue}
                                        onChange={handleFacultySearchChange}
                                    />
                                </div>
                                <Button asChild>
                                    <Link href={route('admin.schedule.faculty.create')}>Create Faculty Schedule</Link>
                                </Button>
                            </div>
                            <Card>
                                <CardContent className="p-0">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead className="w-[50px]">SL</TableHead>
                                                <TableHead>Faculty Name</TableHead>
                                                <TableHead>Department</TableHead>
                                                <TableHead>Embed Link</TableHead>
                                                <TableHead className="w-[150px] text-right">Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {facultySchedules.data.map((schedule, index) => (
                                                <TableRow key={schedule.id}>
                                                    <TableCell>{facultySchedules.meta.from + index}</TableCell>
                                                    <TableCell>{schedule.faculty_name}</TableCell>
                                                    <TableCell>{schedule.department}</TableCell>
                                                    <TableCell>
                                                        <TruncatedCell text={schedule.scheduleEmbedLink} maxLength={40} />
                                                    </TableCell>
                                                    <TableCell className="space-x-1 text-right">
                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button variant="outline" size="icon" onClick={() => handleViewFacultySchedule(schedule)}>
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>View</p>
                                                            </TooltipContent>
                                                        </Tooltip>

                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button variant="outline" size="icon" asChild>
                                                                    <Link href={route('admin.schedule.faculty.edit', schedule.id)}>
                                                                        <Pencil className="h-4 w-4" />
                                                                    </Link>
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Edit</p>
                                                            </TooltipContent>
                                                        </Tooltip>

                                                        <Tooltip>
                                                            <TooltipTrigger asChild>
                                                                <Button
                                                                    variant="destructive"
                                                                    size="icon"
                                                                    onClick={() => handleDeleteFacultySchedule(schedule.id, schedule.faculty_name)}
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </TooltipTrigger>
                                                            <TooltipContent>
                                                                <p>Delete</p>
                                                            </TooltipContent>
                                                        </Tooltip>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </CardContent>
                            </Card>
                            <div className="mt-4">
                                <Pagination meta={facultySchedules.meta} />
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>

                {/* View Class Schedule Dialog */}
                <Dialog open={!!viewingClassSchedule} onOpenChange={() => setViewingClassSchedule(null)}>
                    <DialogContent className="sm:max-w-lg">
                        <DialogHeader>
                            <DialogTitle>View Class Schedule</DialogTitle>
                        </DialogHeader>
                        {viewingClassSchedule && (
                            <div className="space-y-4">
                                <div>
                                    <Label>ID</Label>
                                    <p className="text-sm font-medium">{viewingClassSchedule.id}</p>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label>Department</Label>
                                        <p className="text-sm">{viewingClassSchedule.department}</p>
                                    </div>
                                    <div>
                                        <Label>Session</Label>
                                        <p className="text-sm">{viewingClassSchedule.session}</p>
                                    </div>
                                </div>

                                <div>
                                    <Label>Batch</Label>
                                    <p className="text-sm">{viewingClassSchedule.batch}</p>
                                </div>

                                <div>
                                    <Label>Schedule Embed Link</Label>
                                    {viewingClassSchedule.scheduleEmbedLink ? (
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-600 break-all mb-2">
                                                {viewingClassSchedule.scheduleEmbedLink}
                                            </p>
                                            <iframe
                                                src={viewingClassSchedule.scheduleEmbedLink}
                                                className="w-full h-64 border rounded"
                                                title="Schedule Preview"
                                            />
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-500">No embed link provided</p>
                                    )}
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>

                {/* View Faculty Schedule Dialog */}
                <Dialog open={!!viewingFacultySchedule} onOpenChange={() => setViewingFacultySchedule(null)}>
                    <DialogContent className="sm:max-w-lg">
                        <DialogHeader>
                            <DialogTitle>View Faculty Schedule</DialogTitle>
                        </DialogHeader>
                        {viewingFacultySchedule && (
                            <div className="space-y-4">
                                <div>
                                    <Label>ID</Label>
                                    <p className="text-sm font-medium">{viewingFacultySchedule.id}</p>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label>Faculty Name</Label>
                                        <p className="text-sm font-medium">{viewingFacultySchedule.faculty_name}</p>
                                    </div>
                                    <div>
                                        <Label>Department</Label>
                                        <p className="text-sm">{viewingFacultySchedule.department}</p>
                                    </div>
                                </div>

                                <div>
                                    <Label>Schedule Embed Link</Label>
                                    {viewingFacultySchedule.scheduleEmbedLink ? (
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-600 break-all mb-2">
                                                {viewingFacultySchedule.scheduleEmbedLink}
                                            </p>
                                            <iframe
                                                src={viewingFacultySchedule.scheduleEmbedLink}
                                                className="w-full h-64 border rounded"
                                                title="Schedule Preview"
                                            />
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-500">No embed link provided</p>
                                    )}
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
