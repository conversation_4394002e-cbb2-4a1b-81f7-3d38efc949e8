import React from 'react';
import { ArrowRight } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface AdmissionPromoImageData {
  id: number;
  left_banner_image: string;
  right_banner_image_top: string;
  right_banner_image_top_caption: string | null;
  right_banner_image_top_subtitle: string | null;
  right_banner_image_bottom: string;
  right_banner_image_bottom_caption: string | null;
  right_banner_image_bottom_subtitle: string | null;
  status: string;
}

interface AdmissionHeroBannersProps {
  admissionPromoImage?: AdmissionPromoImageData | null;
}

const AdmissionHeroBanners = ({ admissionPromoImage }: AdmissionHeroBannersProps) => (
  <section className="container mx-auto px-4">
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Left Banner (50%) */}
      <div className="group relative row-span-2 h-[620px] overflow-hidden rounded-xl">
        <img
          src={admissionPromoImage?.left_banner_image || "/images/nist/nist-admission-promo.png"}
          alt="Campus Life"
          className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        
        
      </div>

      {/* Right Top Banner (Promo) */}
      <div className="group relative h-[300px] overflow-hidden rounded-xl">
        <img
          src={admissionPromoImage?.right_banner_image_top || "https://picsum.photos/300/400"}
          alt="Promotional Offer"
          className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
       
        <div className="absolute top-0 left-0 p-8 text-white">
            <h3
            className="text-xl font-bold drop-shadow-lg text-white"
            style={{ textShadow: '2px 2px 8px #333' }}
            >
            {admissionPromoImage?.right_banner_image_top_caption || "Limited Time Promo"}
            </h3>
            <p
            className="mt-1 drop-shadow-lg text-white"
            style={{ textShadow: '2px 2px 8px #333' }}
            >
            {admissionPromoImage?.right_banner_image_top_subtitle || "Enroll now and get exclusive benefits."}
            </p>
        </div>
      </div>

      {/* Right Bottom Banner (Waiver) */}
      <div className="group relative h-[300px] overflow-hidden rounded-xl">
        <img
          src={admissionPromoImage?.right_banner_image_bottom || "https://picsum.photos/300/400"}
          alt="Admission Waiver"
          className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
       
        <div className="absolute bottom-0 right-0 p-8 text-white text-right">
            <h3 className="text-xl font-bold drop-shadow-lg text-white" style={{ textShadow: '2px 2px 8px #333' }}>
            {admissionPromoImage?.right_banner_image_bottom_caption || "Up to 50% Waiver"}
            </h3>
            <p className="mt-1 drop-shadow-lg text-white" style={{ textShadow: '2px 2px 8px #333' }}>
            {admissionPromoImage?.right_banner_image_bottom_subtitle || "Based on your academic results."}
            </p>
            <a
            href="#"
            className="mt-4 inline-flex items-center font-semibold drop-shadow-lg text-white"
            style={{ textShadow: '2px 2px 8px #333' }}
            >
            Learn More <ArrowRight className="ml-2 h-4 w-4" />
            </a>
        </div>
      </div>
    </div>
  </section>
);

export default AdmissionHeroBanners;