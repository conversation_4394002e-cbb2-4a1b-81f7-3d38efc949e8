import { ArrowRight } from 'lucide-react';

interface AdmissionPromoImageData {
    id: number;
    left_banner_image: string;
    right_banner_image_top: string;
    right_banner_image_top_caption: string | null;
    right_banner_image_top_subtitle: string | null;
    right_banner_image_bottom: string;
    right_banner_image_bottom_caption: string | null;
    right_banner_image_bottom_subtitle: string | null;
    status: string;
}

interface AdmissionHeroBannersProps {
    admissionPromoImage?: AdmissionPromoImageData | null;
}

const AdmissionHeroBanners = ({ admissionPromoImage }: AdmissionHeroBannersProps) => (
    <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Left Banner (50%) */}
            {/* Left Banner (50%) */}
            <div className="group relative row-span-2 h-auto overflow-hidden rounded-xl lg:h-[620px]">
                <img
                    src={admissionPromoImage?.left_banner_image || '/images/nist/nist-admission-promo.png'}
                    alt="Campus Life"
                    className="h-auto w-full object-contain lg:h-full lg:object-cover lg:object-center"
                />
            </div>

            {/* Right Top Banner (Promo) */}
            <div className="group relative h-[200px] overflow-hidden rounded-xl sm:h-[250px] lg:h-[300px]">
                <img
                    src={admissionPromoImage?.right_banner_image_top || 'https://picsum.photos/300/400'}
                    alt="Promotional Offer"
                    className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
                />

                <div className="absolute top-0 left-0 p-4 text-white sm:p-6 lg:p-8">
                    <h3 className="text-lg font-bold text-white drop-shadow-lg sm:text-xl" style={{ textShadow: '2px 2px 8px #333' }}>
                        {admissionPromoImage?.right_banner_image_top_caption || 'Limited Time Promo'}
                    </h3>
                    <p className="mt-1 text-sm text-white drop-shadow-lg sm:text-base" style={{ textShadow: '2px 2px 8px #333' }}>
                        {admissionPromoImage?.right_banner_image_top_subtitle || 'Enroll now and get exclusive benefits.'}
                    </p>
                </div>
            </div>

            {/* Right Bottom Banner (Waiver) */}
            <div className="group relative h-[200px] overflow-hidden rounded-xl sm:h-[250px] lg:h-[300px]">
                <img
                    src={admissionPromoImage?.right_banner_image_bottom || 'https://picsum.photos/300/400'}
                    alt="Admission Waiver"
                    className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
                />

                <div className="absolute right-0 bottom-0 p-4 text-right text-white sm:p-6 lg:p-8">
                    <h3 className="text-lg font-bold text-white drop-shadow-lg sm:text-xl" style={{ textShadow: '2px 2px 8px #333' }}>
                        {admissionPromoImage?.right_banner_image_bottom_caption || 'Up to 50% Waiver'}
                    </h3>
                    <p className="mt-1 text-sm text-white drop-shadow-lg sm:text-base" style={{ textShadow: '2px 2px 8px #333' }}>
                        {admissionPromoImage?.right_banner_image_bottom_subtitle || 'Based on your academic results.'}
                    </p>
                    <a
                        href="#"
                        className="mt-2 inline-flex items-center text-sm font-semibold text-white drop-shadow-lg sm:mt-4 sm:text-base"
                        style={{ textShadow: '2px 2px 8px #333' }}
                    >
                        Learn More <ArrowRight className="ml-2 h-3 w-3 sm:h-4 sm:w-4" />
                    </a>
                </div>
            </div>
        </div>
    </section>
);

export default AdmissionHeroBanners;
