import TuitionFees from '@/components/admission/tution-fees';
import HomepageAd from '@/components/ui/homepage-ad';
import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    // Dashboard specific props
    stats?: {
        total_notices: number;
        total_news: number;
        total_faculty: number;
        total_governing_body: number;
        total_users: number;
        total_gallery: number;
        total_image_sliders: number;
        total_class_schedule: number;
        total_admission_posts: number;
    };
    recentNotices?: Array<{
        id: number;
        title: string;
        created_at: string;
    }>;
    recentNews?: Array<{
        id: number;
        title: string;
        created_at: string;
    }>;
    recentAdmissionPosts?: Array<{
        id: number;
        title: string;
        created_at: string;
    }>;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles?: Role[];
    permissions?: Permission[];
    role_names?: string[];
    permission_names?: string[];
    [key: string]: unknown; // This allows for additional properties...
}

export interface News {
        id: number;
        slug: string;
        title: string;
        content: string;
        image: string;
        published_at: string;
        active_status: 'active' | 'inactive';
        created_at: string;
        updated_at: string;
        meta_tag_title: string;
        meta_tag_description: string;
        excerpt: string| null;
}

export interface Noticeboard {
    id: number;
    title: string;
    slug: string;
    category: string;
    content: string;
    image: string | null;
    attachment: string | null;
    published_at: string;
    active_status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}

export interface Governingbody {
    id: number;
    name: string;
    designation: string;
    display_order: number;
    profile_URL: string;
    profile_image: string;
}

export interface ClassSchedule {
    id: number;
    department: string;
    session: string;
    batch: string;
    scheduleEmbedLink: string | null;
}

export interface FacultySchedule {
    id: number;
    faculty_name: string;
    department: string;
    scheduleEmbedLink: string | null;
}

export interface FacultyInfo {
    id: number;
    name: string;
    slug: string;
    designation: string;
    department: string;
    image: string | null;
    bio: string | null;
    about: string | null;
    education: string | null;
    research: string | null;
    interests: string | null;
    official_email: string;
    secondary_email: string | null;
    primary_phone: string;
    secondary_phone: string | null;
    created_at: string;
    updated_at: string;
    active_status: 'active' | 'inactive';
}

export interface AdmissionSpot {
    id: number;
    campaign_name: string;
    session: string;
    program_name: string;
    application_start_date: string;
    application_end_date: string;
    admission_test_date: string | null;
    session_start_date: string | null;
    active_status: 'unpublished' | 'published';
    date_created: string;
    created_at: string;
    updated_at: string;
}

export interface TuitionFee {
    id: number;
    image: string;
    program_name: string;
    department: string;
    created_at: string;
    updated_at: string;
}

export interface AdmissionPost {
    id: number;
    title: string;
    admission_circular_content: string | null;
    active_status: 'unpublished' | 'published';
    published_date: string;
    created_at: string;
    updated_at: string;
}

export interface AdmissionPromoImage {
    id: number;
    left_banner_image: string;
    right_banner_image_top: string;
    right_banner_image_top_caption: string | null;
    right_banner_image_top_subtitle: string | null;
    right_banner_image_bottom: string;
    right_banner_image_bottom_caption: string | null;
    right_banner_image_bottom_subtitle: string | null;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}

export interface HomepageAd {
    id: number;
    ad_title: string | null;
    program_info_title: string | null;
    session: string | null;
    offer_title: string | null;
    offer_text: string | null;
    active_status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}

export interface AdmissionModal {
    id: number;
    campaign_name: string | null;
    title: string | null;
    active_status: 'active' | 'inactive';
    published_date: string;
    image: string;
    created_at: string;
    updated_at: string;
}

export interface Role {
    value: string;
    id: number;
    module: string;
    name: string;
    label: string;
    description: string;
    is_active: boolean;
    guard_name: string;
    created_at: string;
    updated_at: string;
    permissions?: Permission[];
    permission_names?: string[];
    users_count?: number;
}

export interface Permission {
    id: number;
    name: string;
    module: string;
    label: string;
    description: string;
    is_active: boolean;
    guard_name: string;
    created_at: string;
    updated_at: string;
    roles_count?: number;
}

// Paginated data structure for access control
export interface PaginatedData<T> {
    from: number;
    data: T[];
    meta: {
        total: number;
        from: number;
        to: number;
        current_page: number;
        last_page: number;
        per_page: number;
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
    };
}

export interface ImageSliderData {
    id: number;
    title: string;
    image: string;
    heading: string;
    caption: string;
    order: number;
    created_at: string;
    updated_at: string;
}

export interface TuitionFeesData {
    id: number;
    image: string;
    program_name: string;
    department: string;
    created_at: string;
    updated_at: string;
}

export interface GalleryData {
    id: number;
    image: string;
    created_at: string;
    updated_at: string;
}

export interface HomepageAd{
    id: number;
    ad_title: string | null;
    program_info_title: string | null;
    session: string | null;
    offer_title: string | null;
    offer_text: string | null;
    active_status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}