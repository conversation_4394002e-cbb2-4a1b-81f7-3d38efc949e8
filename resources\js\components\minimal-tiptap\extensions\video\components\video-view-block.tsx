import React, { useState } from 'react'
import { NodeViewWrapper } from '@tiptap/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from '@/components/ui/dialog'
import { Trash2, Edit, Play, AlignLeft, AlignCenter, AlignRight } from 'lucide-react'
import { getEmbedUrl } from '../video'
import { cn } from '@/lib/utils'

interface VideoViewBlockProps {
  node: {
    attrs: {
      src: string
      width: number
      height: number
      title?: string
      align?: string
    }
  }
  updateAttributes: (attrs: Record<string, any>) => void
  deleteNode: () => void
  selected: boolean
}

export const VideoViewBlock: React.FC<VideoViewBlockProps> = ({
  node,
  updateAttributes,
  deleteNode,
  selected,
}) => {
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [editSrc, setEditSrc] = useState(node.attrs.src || '')
  const [editWidth, setEditWidth] = useState(node.attrs.width || 560)
  const [editHeight, setEditHeight] = useState(node.attrs.height || 315)
  const [editTitle, setEditTitle] = useState(node.attrs.title || '')
  const align = node.attrs.align || 'center'

  const handleSave = () => {
    const embedUrl = getEmbedUrl(editSrc)
    if (embedUrl) {
      updateAttributes({
        src: embedUrl,
        width: editWidth,
        height: editHeight,
        title: editTitle,
      })
      setIsEditOpen(false)
    } else {
      alert('Please enter a valid YouTube, Vimeo, or Dailymotion URL')
    }
  }

  const handleCancel = () => {
    setEditSrc(node.attrs.src || '')
    setEditWidth(node.attrs.width || 560)
    setEditHeight(node.attrs.height || 315)
    setEditTitle(node.attrs.title || '')
    setIsEditOpen(false)
  }

  const handleAlignChange = (newAlign: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }
    updateAttributes({ align: newAlign })
  }

  const getAlignmentClass = (align: string) => {
    switch (align) {
      case 'left':
        return 'text-left'
      case 'right':
        return 'text-right'
      case 'center':
      default:
        return 'text-center'
    }
  }

  if (!node.attrs.src) {
    return (
      <NodeViewWrapper className="video-view-block">
        <div className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-center">
            <Play className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">No video source</p>
            <Button
              onClick={() => setIsEditOpen(true)}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              Add Video
            </Button>
          </div>
        </div>

        <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add Video</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="video-url">Video URL</Label>
                <Input
                  id="video-url"
                  value={editSrc}
                  onChange={(e) => setEditSrc(e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Supports YouTube, Vimeo, and Dailymotion URLs
                </p>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="video-width">Width</Label>
                  <Input
                    id="video-width"
                    type="number"
                    value={editWidth}
                    onChange={(e) => setEditWidth(Number(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="video-height">Height</Label>
                  <Input
                    id="video-height"
                    type="number"
                    value={editHeight}
                    onChange={(e) => setEditHeight(Number(e.target.value))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="video-title">Title (optional)</Label>
                <Input
                  id="video-title"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="Video title"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  Add Video
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </NodeViewWrapper>
    )
  }

  return (
    <NodeViewWrapper className={cn("video-view-block", getAlignmentClass(align))} data-align={align}>
      <div className={cn(
        "relative group max-w-full",
        align === 'left' ? 'mr-auto' : align === 'right' ? 'ml-auto' : 'mx-auto',
        selected ? 'ring-2 ring-blue-500' : ''
      )}
      style={{
        maxWidth: '100%',
        maxHeight: '400px'
      }}>
        <iframe
          src={node.attrs.src}
          width={node.attrs.width}
          height={node.attrs.height}
          title={node.attrs.title || 'Video'}
          style={{
            border: 'none',
            aspectRatio: `${node.attrs.width} / ${node.attrs.height}`
          }}
          allowFullScreen
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          className="rounded-lg w-full h-auto max-w-full"
        />
        
        {/* Overlay with controls */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg">
          {/* Alignment controls */}
          <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
            <Button
              size="sm"
              variant={align === 'left' ? 'default' : 'secondary'}
              className="h-8 w-8 p-0"
              onClick={(e) => handleAlignChange('left', e)}
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant={align === 'center' ? 'default' : 'secondary'}
              className="h-8 w-8 p-0"
              onClick={(e) => handleAlignChange('center', e)}
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant={align === 'right' ? 'default' : 'secondary'}
              className="h-8 w-8 p-0"
              onClick={(e) => handleAlignChange('right', e)}
            >
              <AlignRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Edit/Delete controls */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                  <Edit className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Edit Video</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="edit-video-url">Video URL</Label>
                    <Input
                      id="edit-video-url"
                      value={editSrc}
                      onChange={(e) => setEditSrc(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=..."
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="edit-video-width">Width</Label>
                      <Input
                        id="edit-video-width"
                        type="number"
                        value={editWidth}
                        onChange={(e) => setEditWidth(Number(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-video-height">Height</Label>
                      <Input
                        id="edit-video-height"
                        type="number"
                        value={editHeight}
                        onChange={(e) => setEditHeight(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="edit-video-title">Title (optional)</Label>
                    <Input
                      id="edit-video-title"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      placeholder="Video title"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave}>
                      Save Changes
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            
            <Button
              size="sm"
              variant="destructive"
              className="h-8 w-8 p-0"
              onClick={deleteNode}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}
