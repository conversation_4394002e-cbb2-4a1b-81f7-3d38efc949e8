<?php

namespace App\Http\Controllers;

use App\Models\AdmissionPromoImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdmissionPromoImageController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'left_banner_image' => 'required|image|max:2048',
            'right_banner_image_top' => 'required|image|max:2048',
            'right_banner_image_top_caption' => 'nullable|string|max:255',
            'right_banner_image_top_subtitle' => 'nullable|string|max:255',
            'right_banner_image_bottom' => 'required|image|max:2048',
            'right_banner_image_bottom_caption' => 'nullable|string|max:255',
            'right_banner_image_bottom_subtitle' => 'nullable|string|max:255',
        ]);

        $data['status'] = 'inactive';

        if ($request->hasFile('left_banner_image')) {
            $data['left_banner_image'] = storeSanitizedFile($request->file('left_banner_image'), 'uploads/admission-promo');
        }

        if ($request->hasFile('right_banner_image_top')) {
            $data['right_banner_image_top'] = storeSanitizedFile($request->file('right_banner_image_top'), 'uploads/admission-promo');
        }

        if ($request->hasFile('right_banner_image_bottom')) {
            $data['right_banner_image_bottom'] = storeSanitizedFile($request->file('right_banner_image_bottom'), 'uploads/admission-promo');
        }

        AdmissionPromoImage::create($data);

        return redirect(route('admin.admission.index', ['tab' => 'admission_promo_image']))
            ->with('success', 'Admission promo image created successfully');
    }

    public function update(Request $request, AdmissionPromoImage $admissionPromoImage)
    {
        // Check if this is a status-only update (from StatusToggle component)
        if ($request->has('status') && count($request->all()) === 1) {
            $data = $request->validate([
                'status' => 'required|in:active,inactive',
            ]);

            $admissionPromoImage->update($data);

            return redirect(route('admin.admission.index', ['tab' => 'admission_promo_image']))
                ->with('success', 'Admission promo image status updated successfully');
        }

        // Full update validation
        $data = $request->validate([
            'left_banner_image' => 'nullable|image|max:2048',
            'right_banner_image_top' => 'nullable|image|max:2048',
            'right_banner_image_top_caption' => 'nullable|string|max:255',
            'right_banner_image_top_subtitle' => 'nullable|string|max:255',
            'right_banner_image_bottom' => 'nullable|image|max:2048',
            'right_banner_image_bottom_caption' => 'nullable|string|max:255',
            'right_banner_image_bottom_subtitle' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
        ]);

        // Handle image uploads
        if ($request->hasFile('left_banner_image')) {
            if ($admissionPromoImage->left_banner_image) {
                Storage::disk('public')->delete($admissionPromoImage->left_banner_image);
            }
            $data['left_banner_image'] = storeSanitizedFile($request->file('left_banner_image'), 'uploads/admission-promo');
        } else {
            $data['left_banner_image'] = $admissionPromoImage->left_banner_image;
        }

        if ($request->hasFile('right_banner_image_top')) {
            if ($admissionPromoImage->right_banner_image_top) {
                Storage::disk('public')->delete($admissionPromoImage->right_banner_image_top);
            }
            $data['right_banner_image_top'] = storeSanitizedFile($request->file('right_banner_image_top'), 'uploads/admission-promo');
        } else {
            $data['right_banner_image_top'] = $admissionPromoImage->right_banner_image_top;
        }

        if ($request->hasFile('right_banner_image_bottom')) {
            if ($admissionPromoImage->right_banner_image_bottom) {
                Storage::disk('public')->delete($admissionPromoImage->right_banner_image_bottom);
            }
            $data['right_banner_image_bottom'] = storeSanitizedFile($request->file('right_banner_image_bottom'), 'uploads/admission-promo');
        } else {
            $data['right_banner_image_bottom'] = $admissionPromoImage->right_banner_image_bottom;
        }

        $admissionPromoImage->update($data);

        return redirect(route('admin.admission.index', ['tab' => 'admission_promo_image']))
            ->with('success', 'Admission promo image updated successfully');
    }

    public function destroy(AdmissionPromoImage $admissionPromoImage)
    {
        // Delete associated images
        if ($admissionPromoImage->left_banner_image) {
            Storage::disk('public')->delete($admissionPromoImage->left_banner_image);
        }
        if ($admissionPromoImage->right_banner_image_top) {
            Storage::disk('public')->delete($admissionPromoImage->right_banner_image_top);
        }
        if ($admissionPromoImage->right_banner_image_bottom) {
            Storage::disk('public')->delete($admissionPromoImage->right_banner_image_bottom);
        }

        $admissionPromoImage->delete();

        return redirect(route('admin.admission.index', ['tab' => 'admission_promo_image']))
            ->with('success', 'Admission promo image deleted successfully');
    }
}
