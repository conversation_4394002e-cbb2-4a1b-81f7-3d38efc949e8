<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NewsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    public function toArray(Request $request): array
    {
       
        return [
            "id" => $this->id,
            "slug" => $this->slug,
            "title" => $this->title,
            "content" => $this->content,
            "image" => asset('storage/'.$this->image),
            "published_at" => $this->published_at?->format('d M Y'),
            "active_status" => $this->active_status,
            "created_at" => $this->created_at->format('d M Y'),
            "meta_tag_title" => $this->meta_tag_title,
            "meta_tag_description" => $this->meta_tag_description,
            "excerpt" => $this->excerpt,
        ];
    }
}
