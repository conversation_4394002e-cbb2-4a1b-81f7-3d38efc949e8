import React, { useState, useRef } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Edit, Plus, Trash2, Search, Eye, FileText } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import Pagination from '@/components/pagination';
import InputError from '@/components/input-error';
import debounce from 'lodash/debounce';
import { useConfirmation } from '@/contexts/confirmation-context';

interface FileUploadData {
    id: number;
    title: string;
    description: string | null;
    category: string;
    file: string;
    relative_url: string;
    absolute_url: string;
    file_name: string;
    file_extension: string;
    created_at: string;
    updated_at: string;
}

interface PaginatedData {
    data: FileUploadData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface FileUploadTabProps {
    data: PaginatedData;
}

const categoryOptions = [
    { value: 'all_activity', label: 'All Activity' },
    { value: 'events', label: 'Events' },
    { value: 'cse', label: 'CSE' },
    { value: 'ece', label: 'ECE' },
    { value: 'bba', label: 'BBA' },
    { value: 'bmb', label: 'BMB' },
    { value: 'excellent_results', label: 'Excellent Results' },
];

export default function FileUploadTab({ data }: FileUploadTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<FileUploadData | null>(null);
    const [viewingItem, setViewingItem] = useState<FileUploadData | null>(null);

    const { data: formData, setData, post, processing, errors, reset } = useForm({
        title: '',
        description: '',
        category: '',
        file: null as File | null,
    });

    // Search functionality
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.file-upload.index'), { 
                tab: 'files', 
                files_search: query 
            }, { preserveState: true, replace: true });
        }, 500),
    ).current;

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        handleSearch(e.target.value);
    };

    const handleCreate = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.file-uploads.store'), {
            onSuccess: () => {
                setIsCreateOpen(false);
                reset();
            },
        });
    };

    const handleEdit = (item: FileUploadData) => {
        setEditingItem(item);
        setData({
            title: item.title,
            description: item.description || '',
            category: item.category,
            file: null,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(route('admin.file-uploads.update', editingItem.id), {
                _method: 'put',
                ...formData,
            }, {
                onSuccess: () => {
                    setIsEditOpen(false);
                    setEditingItem(null);
                    reset();
                },
            });
        }
    };

   

    const handleDelete = async (id: number, title: string) => {
            const confirmed = await showConfirmation({
                title: 'Delete File',
                description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
                confirmText: 'Delete',
                variant: 'destructive',
            });
    
            if (confirmed) {
                router.delete(route('admin.file-uploads.destroy', id));
            }
        };

    const handleView = (item: FileUploadData) => {
        setViewingItem(item);
        setIsViewOpen(true);
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        // You could add a toast notification here
    };

    const getFileIcon = (extension: string) => {
        switch (extension.toLowerCase()) {
            case 'pdf':
                return <FileText className="h-8 w-8 text-red-500" />;
            case 'doc':
            case 'docx':
                return <FileText className="h-8 w-8 text-blue-500" />;
            default:
                return <FileText className="h-8 w-8 text-gray-500" />;
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div className="relative w-full sm:w-1/3">
                    <Input 
                        id={'search'} 
                        className="peer ps-9" 
                        placeholder="Search files by title or category..." 
                        type="search" 
                        onChange={onSearchChange} 
                    />
                    <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
                        <Search size={16} aria-hidden="true" />
                    </div>
                </div>

                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Upload File
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Upload File</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="title">Title *</Label>
                                    <Input
                                        id="title"
                                        value={formData.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="Enter title"
                                    />
                                    <InputError message={errors.title} />
                                </div>
                                <div>
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={formData.category} onValueChange={(value) => setData('category', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categoryOptions.map((option) => (
                                                <SelectItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.category} />
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Enter description"
                                    rows={3}
                                />
                                <InputError message={errors.description} />
                            </div>
                            <div>
                                <Label htmlFor="file">File *</Label>
                                <Input
                                    id="file"
                                    type="file"
                                    accept=".pdf,.doc,.docx"
                                    onChange={(e) => setData('file', e.target.files?.[0] || null)}
                                />
                                <p className="text-sm text-gray-500 mt-1">Accepted formats: PDF, DOC, DOCX (Max: 10MB)</p>
                                <InputError message={errors.file} />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Upload
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>File</TableHead>
                                <TableHead>Title</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Created</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        <div className="flex items-center space-x-2">
                                            {getFileIcon(item.file_extension)}
                                            <span className="text-sm">{item.file_name}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell className="font-medium">{item.title}</TableCell>
                                    <TableCell>
                                        <span className="capitalize">{item.category.replace('_', ' ')}</span>
                                    </TableCell>
                                    <TableCell>
                                        <span className="uppercase text-xs bg-gray-100 px-2 py-1 rounded">
                                            {item.file_extension}
                                        </span>
                                    </TableCell>
                                    <TableCell>{new Date(item.created_at).toLocaleDateString()}</TableCell>
                                    <TableCell className="space-x-1">
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>View URLs</p>
                                            </TooltipContent>
                                        </Tooltip>
                                        
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Edit</p>
                                            </TooltipContent>
                                        </Tooltip>
                                        
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.title)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Delete</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit File</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit_title">Title *</Label>
                                <Input
                                    id="edit_title"
                                    value={formData.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_category">Category *</Label>
                                <Select value={formData.category} onValueChange={(value) => setData('category', value)}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categoryOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="edit_description">Description</Label>
                            <Textarea
                                id="edit_description"
                                value={formData.description}
                                onChange={(e) => setData('description', e.target.value)}
                                rows={3}
                            />
                        </div>
                        <div>
                            <Label htmlFor="edit_file">File</Label>
                            <Input
                                id="edit_file"
                                type="file"
                                accept=".pdf,.doc,.docx"
                                onChange={(e) => setData('file', e.target.files?.[0] || null)}
                            />
                            {editingItem && (
                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-2">Current File:</p>
                                    <div className="flex items-center space-x-2">
                                        {getFileIcon(editingItem.file_extension)}
                                        <span className="text-sm">{editingItem.file_name}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View URLs Dialog */}
            <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>View URLs</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div>
                                <Label>Title</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.title} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.title)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>Relative URL</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.relative_url} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.relative_url)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>Absolute URL</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.absolute_url} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.absolute_url)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>File Info</Label>
                                <div className="flex items-center space-x-2 p-3 border rounded">
                                    {getFileIcon(viewingItem.file_extension)}
                                    <div>
                                        <p className="font-medium">{viewingItem.file_name}</p>
                                        <p className="text-sm text-gray-500">Type: {viewingItem.file_extension.toUpperCase()}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>

      
        </div>
    );
}
