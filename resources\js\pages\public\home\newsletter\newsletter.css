.newsletter {
  background-color: #0c1b2e;
  padding: 50px 0;
  color: #fff;
}

.newsletter .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.newsletter .left {
  flex: 1;
  padding-right: 50px;
}

.newsletter h1 {
  color: #fff;
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 15px;
}

.newsletter span {
  display: block;
  font-size: 18px;
}

.newsletter .right {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
}

.newsletter input {
  width: 100%;
  padding: 17px 30px;
  border: none;
  outline: none;
  border-radius: 25px;
  font-size: 16px;
}

.newsletter-btn {
  text-align: center;
  background-color: #fff;
  color: #271a3d;
  font-weight: 600;
  border: none;
  margin: 0px 10px 0 10px;
  border-radius: 25px;
  padding: 16px 30px;
  cursor: pointer;
  box-shadow: 0 24px 36px -11px rgb(0 0 0 / 9%);
  transition: 0.5s;
  text-transform: uppercase;
  font-size: 16px;
}

.newsletter-btn:hover{
  background-color: #0c1b2e;
  color: #ffffff;
  border: 1px solid rgb(255, 255, 255);
}


/* Mobile Styles */
@media (max-width: 768px) {
  .newsletter .container {
    flex-direction: column;
    align-items: center;
  }

  .newsletter .left {
    padding-right: 0;
    margin-bottom: 20px;
    text-align: center;
  }

  .newsletter h1 {
    font-size: 24px;
  }

  .newsletter .right {
    width: 100%;
    flex-direction: column;
  }

  .newsletter input {
    width: calc(100% - 60px);
    margin-bottom: 10px;
  }

  .newsletter-btn {
    margin-left: 0;
    
  }
}
