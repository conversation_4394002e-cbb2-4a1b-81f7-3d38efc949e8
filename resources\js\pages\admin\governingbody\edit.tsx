import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { Governingbody, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { ChangeEvent, FormEventHandler, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Edit Governing Body',
        href: '/governingbody/edit',
    },
];

export default function GoverningBodyEdit({ currentGoverningBody }: { currentGoverningBody: Governingbody }) {
    const [name, setName] = useState<string>(currentGoverningBody.name);
    const [designation, setDesignation] = useState<string>(currentGoverningBody.designation);
    const [display_order, setDisplayOrder] = useState<number>(currentGoverningBody.display_order);
    const [profile_URL, setProfileURL] = useState<string>(currentGoverningBody.profile_URL);
    const [profile_image, setProfileImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    const { errors } = usePage().props;

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.governingbody.update', currentGoverningBody.id), {
            _method: 'put',
            name,
            designation,
            display_order,
            profile_URL,
            profile_image,
        });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setProfileImage(file);
            setImagePreview(URL.createObjectURL(file));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Governing Body" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Governing Body</div>

                        <Button>
                            <Link href="/admin/governingbody" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="name">Name</Label>
                                        <Input
                                            type="text"
                                            id="name"
                                            placeholder="Name"
                                            value={name}
                                            onChange={(e) => setName(e.target.value)}
                                            aria-invalid={!!errors.name}
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="designation">Designation</Label>
                                        <Input
                                            type="text"
                                            id="designation"
                                            placeholder="Designation"
                                            value={designation}
                                            onChange={(e) => setDesignation(e.target.value)}
                                            aria-invalid={!!errors.designation}
                                        />
                                        <InputError message={errors.designation} />
                                    </div>

                                    <div>
                                        <Label htmlFor="profile_URL">Profile/Website LINK</Label>
                                        <Input
                                            type="text"
                                            id="profile_URL"
                                            placeholder="Profile/Website LINK"
                                            value={profile_URL}
                                            onChange={(e) => setProfileURL(e.target.value)}
                                            aria-invalid={!!errors.profile_URL}
                                        />
                                        <InputError message={errors.profile_URL} />
                                    </div>

                                    <div>
                                        <Label htmlFor="display_order">Display Order</Label>
                                        <Input
                                            type="number"
                                            id="display_order"
                                            placeholder="Display Order"
                                            value={display_order}
                                            onChange={(e) => setDisplayOrder(parseInt(e.target.value))}
                                            aria-invalid={!!errors.display_order}
                                        />
                                    </div>
                                </div>

                                <div className="mt-4">
                                    <Label htmlFor="profile_image">Select Image</Label>
                                    <Input type="file" id="profile_image" onChange={handleFileChange} aria-invalid={!!errors.profile_image} />
                                    <InputError message={errors.profile_image} />
                                    <div className="flex gap-4">
                                        <div className="flex flex-col">
                                            {currentGoverningBody.profile_image && (
                                                <img
                                                    src={currentGoverningBody.profile_image}
                                                    alt="Preview"
                                                    className={`mt-2 w-48 rounded-lg object-cover transition-all duration-300 ${imagePreview ? 'brightness-50' : 'brightness-100'} `}
                                                />
                                            )}
                                        </div>
                                    
                                        <div className="flex flex-col">
                                            {profile_image && (
                                                <>
                                                    <img
                                                        src={imagePreview || currentGoverningBody.profile_image}
                                                        alt="Preview"
                                                        className="mt-2 w-48 rounded-lg object-cover"
                                                    />
                                                    <div className="text-left text-sm">
                                                        <p>
                                                            <strong className="text-neutral-12">File Name:</strong> {profile_image.name}
                                                        </p>
                                                        <p>
                                                            <strong className="text-neutral-12">File Size:</strong>{' '}
                                                            {(profile_image.size / 1024).toFixed(2)} KB
                                                        </p>
                                                    </div>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Governing Body</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
