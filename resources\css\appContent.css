@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}
body {
  font-family: "Poppins", sans-serif;
  background-color: #f8f8f8;
  /*background-color: silver;*/
}
/*------------global-----------*/
.container-nav {
  width: 100%;
	padding-right: 7.5%;
	padding-left: 7.5%;
	margin-right: auto;
	margin-left: auto;
  border-bottom: 1px solid #e4e4e4;
}
.container {
  max-width: 85%;
  margin: auto;
}
.flexSB {
  display: flex;
  justify-content: space-between;
}

/*.d-none{
  display: none;
}*/
/*.icon {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.3);
  color: #1eb2a6;
  transition: 0.5s;
}
.icon:hover {
  cursor: pointer;
  background-color: #1eb2a6;
  color: #fff;
}*/
a {
  text-decoration: none;
}
li {
  list-style-type: none;
}
.row {
  width: 50%;
}

.primary-btn {
  background: #07944a;
  border: 1px solid #07944a;
  color: #fff;
}

.primary-btn-rd{
  background: #07944a;
  color: #fff;
  padding: 14px 25px;
  font-size: 16px;
  border-radius: 30px;
  cursor: pointer;
  border: 0;
  outline: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.primary-btn-rd img{
  width: 20px;
  margin-left: 10px;
}

/*------------global-----------*/
#heading {
  text-align: center;
  padding: 20px 0;
}
#heading h2 {
  font-size: 35px;
  color: #000F38;
  margin: 10px 0;
  text-transform: capitalize;
  font-weight: 600;
}
#heading h3 {
  font-weight: 600;
  letter-spacing: 1px;
  color: #ff9800; /* old #212EA0;*/
  text-transform: uppercase;
}
#heading h1 {
  font-size: 45px;
  margin: 20px 0;
  text-transform: capitalize;
}
p {
  line-height: 30px;
  font-size: 18px;
  color: black;
}
.back {
  background: url("./components/img/entrybanner.jpg") no-repeat scroll center center / cover;

  padding: 100px 0 110px;
  color: #fff;
}
.back-container{
  align-self: center;
  
}
.back h1 {
  color: #ffffff;
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 5px;
  line-height: 1.1;
  word-wrap: break-word;
}
.back h2 {
  font-weight: 500;
  font-size: 17px;
  text-transform: uppercase;
}

.mt-25{
  margin-top: 25px;
}
.mb-25{
  margin-bottom: 25px;
}
.mb-50{
  margin-bottom: 50px;
}
.pb-50{
  padding-bottom: 50px;
}
.pt-50{
  padding-top: 50px;
}
.pb-100{
  padding-bottom: 100px;
}
.pt-100{
  padding-top: 100px;
}
.margin {
  margin-top: 40.3%;
}
.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 30px;
}
.flex {
  display: flex;
}
.grid2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 30px;
}

.gridn {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20px;
}

.outline-btn {
  margin: 0;
  box-shadow: none;
  border: 2px solid #1eb2a6;
  width: 100%;
  transition: 0.5s;
}
.outline-btn:hover {
  background-color: #1eb2a6;
  color: #fff;
}
.grid3 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20px;
}
.padding {
  padding: 80px 0;
}
.shadow {
  box-shadow: 0 5px 25px -2px rgb(0 0 0 / 6%);
  background-color: #fff;
}
@media screen and (max-width: 768px) {
  .grid2,
  .grid3,
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .gridn{
    grid-template-columns: repeat(1, 1fr);
  }
  .back {
    background-position: center;
    padding-top: 20%;
    height: 30vh;
    background-size: cover;
  }

  .container-nav{
    padding-right: 0px;
    padding-left: 0px;
    margin-right: auto;
    margin-left: auto;
  }

  .pt-100{
    padding-top: 20px;
  } 
  .pb-100{
    padding-bottom: 20px;
  }
  
}

@media screen and (max-width: 480px){
 .back h1{
  font-size: 28px;
 }
}

