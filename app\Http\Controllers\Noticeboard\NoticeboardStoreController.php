<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use App\Models\EditorUpload;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

class NoticeboardStoreController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        //
         $data = $request->validate([
            'title' => 'required',
            'content' => 'required',
            'category' => 'required',
            'image' => 'nullable|image',
            'attachment' => 'nullable|file',
            'published_at' => 'required|date',
            'draft_token' => 'required|string',
        ]);

        $data['slug'] = str($data['title'])->slug();

        // Set active_status to active by default
        $data['active_status'] = 'active';

        // Use the helper function for the image
        if ($request->hasFile('image')) {
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/notices/images');
        }

        if ($request->hasFile('attachment')) {
            $data['attachment'] = storeSanitizedFile($request->file('attachment'), 'uploads/notices/attachments');
        }

        $noticeboard = Noticeboard::create($data);

        // Handle editor uploads if draft_token is provided
        if ($request->has('draft_token') && $request->input('draft_token')) {
            // Extract image paths actually referenced in content
            $pathsInContent = $this->extractStoragePathsFromHtml($noticeboard->content);

            // Pick uploads belonging to this draft token and referenced in content
            $uploads = EditorUpload::where('draft_token', $request->input('draft_token'))
                ->whereIn('path', $pathsInContent)
                ->get();

            // Attach valid uploads to Noticeboard
            foreach ($uploads as $upload) {
                $noticeboard->editorUploads()->syncWithoutDetaching([$upload->id]);
                $upload->update(['draft_token' => null]); // clear draft
            }

            // Delete unused draft uploads for this token
            $unusedDrafts = EditorUpload::where('draft_token', $request->input('draft_token'))
                ->whereNotIn('path', $pathsInContent)
                ->get();

            foreach ($unusedDrafts as $draft) {
                if ($draft->path && Storage::disk('public')->exists($draft->path)) {
                    Storage::disk('public')->delete($draft->path);
                }
                $draft->delete();
            }
        }

        return redirect(route('admin.noticeboard.index'))->with('success', 'Notice created successfully');
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn ($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn ($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn ($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo.jpg" → "foo.jpg"
            ->values()
            ->all();
    }
}
