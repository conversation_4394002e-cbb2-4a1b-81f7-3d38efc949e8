-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Sep 02, 2025 at 04:25 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `nistwcms`
--

-- --------------------------------------------------------

--
-- Table structure for table `admission_modals`
--

CREATE TABLE `admission_modals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `campaign_name` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `active_status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `published_date` date NOT NULL,
  `image` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_modals`
--

INSERT INTO `admission_modals` (`id`, `campaign_name`, `title`, `active_status`, `published_date`, `image`, `created_at`, `updated_at`) VALUES
(1, 'Spring 2024 Campaign', 'Apply Now!', 'active', '2024-01-01', 'uploads/admission-modals/nist-admission-promo.png', '2025-08-08 12:44:30', '2025-08-25 13:13:38');

-- --------------------------------------------------------

--
-- Table structure for table `admission_posts`
--

CREATE TABLE `admission_posts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `admission_circular_content` longtext DEFAULT NULL,
  `active_status` enum('unpublished','published') NOT NULL DEFAULT 'unpublished',
  `published_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_posts`
--

INSERT INTO `admission_posts` (`id`, `title`, `admission_circular_content`, `active_status`, `published_date`, `created_at`, `updated_at`) VALUES
(1, 'Admission Circular 2025', '<p class=\"text-node\" style=\"text-align: left;\">This is a sample admission circular content.</p><img src=\"http://localhost:8000/storage/uploads/post-images/licon.PNG\" alt=\"\" title=\"\" id=\"61\" width=\"470\" height=\"245\" filename=\"licon.PNG\" data-align=\"center\"><p class=\"text-node\" style=\"text-align: left;\"></p>', 'published', '2025-08-22', '2025-08-08 12:38:41', '2025-08-31 17:23:47');

-- --------------------------------------------------------

--
-- Table structure for table `admission_promo_images`
--

CREATE TABLE `admission_promo_images` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `left_banner_image` varchar(255) NOT NULL,
  `right_banner_image_top` varchar(255) NOT NULL,
  `right_banner_image_top_caption` varchar(255) DEFAULT NULL,
  `right_banner_image_top_subtitle` varchar(255) DEFAULT NULL,
  `right_banner_image_bottom` varchar(255) NOT NULL,
  `right_banner_image_bottom_caption` varchar(255) DEFAULT NULL,
  `right_banner_image_bottom_subtitle` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_promo_images`
--

INSERT INTO `admission_promo_images` (`id`, `left_banner_image`, `right_banner_image_top`, `right_banner_image_top_caption`, `right_banner_image_top_subtitle`, `right_banner_image_bottom`, `right_banner_image_bottom_caption`, `right_banner_image_bottom_subtitle`, `status`, `created_at`, `updated_at`) VALUES
(1, 'uploads/admission-promo/nist-building.jpg', 'uploads/admission-promo/bba-cover.jpg', 'here is top title', 'here is top subtile', 'uploads/admission-promo/cse-cover.jpg', 'here is bottom caption', 'here is bottom subtitle', 'inactive', '2025-08-08 12:55:55', '2025-08-25 10:17:44'),
(2, 'uploads/admission-promo/licon.PNG', 'uploads/admission-promo/extr-1756151901.PNG', 'here is top title', 'here is top subtile', 'uploads/admission-promo/laravel12f.PNG', 'here is bottom caption', 'here is bottom subtitle', 'active', '2025-08-25 10:18:19', '2025-08-29 12:39:40');

-- --------------------------------------------------------

--
-- Table structure for table `admission_spots`
--

CREATE TABLE `admission_spots` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `campaign_name` varchar(255) NOT NULL,
  `session` varchar(255) NOT NULL,
  `program_name` varchar(255) NOT NULL,
  `application_start_date` date NOT NULL,
  `application_end_date` date NOT NULL,
  `admission_test_date` date DEFAULT NULL,
  `session_start_date` date DEFAULT NULL,
  `active_status` enum('unpublished','published') NOT NULL DEFAULT 'unpublished',
  `date_created` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admission_spots`
--

INSERT INTO `admission_spots` (`id`, `campaign_name`, `session`, `program_name`, `application_start_date`, `application_end_date`, `admission_test_date`, `session_start_date`, `active_status`, `date_created`, `created_at`, `updated_at`) VALUES
(1, 'Spring 2024 Admission', 'Spring 2024', 'Computer Science', '2024-01-01', '2024-02-15', NULL, NULL, 'unpublished', '2024-01-01', '2025-08-08 12:38:28', '2025-08-09 12:53:16'),
(2, 'BBA CSE ECE Admission', '2024-2025', 'BBA CSE ECE Professional', '2025-08-11', '2025-08-28', NULL, '2025-10-01', 'published', '2025-08-25', '2025-08-25 10:09:31', '2025-08-25 13:15:16');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `campus_activities`
--

CREATE TABLE `campus_activities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `slug` varchar(255) NOT NULL,
  `caption` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) NOT NULL,
  `published_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `class_schedules`
--

CREATE TABLE `class_schedules` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `department` varchar(255) NOT NULL,
  `session` varchar(255) NOT NULL,
  `batch` varchar(255) NOT NULL,
  `scheduleEmbedLink` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `class_schedules`
--

INSERT INTO `class_schedules` (`id`, `department`, `session`, `batch`, `scheduleEmbedLink`, `created_at`, `updated_at`) VALUES
(1, 'CSE', '2019-2020', '1', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-08-08 08:49:29', '2025-09-01 13:54:40'),
(2, 'CSE', '2020-2021', '2', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:55:32', '2025-09-01 13:55:32'),
(3, 'CSE', '2021-2022', '3', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:57:23', '2025-09-01 13:57:23'),
(4, 'CSE', '2022-2023', '4', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:58:16', '2025-09-01 13:58:16'),
(5, 'CSE', '2023-2024', '5', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:59:04', '2025-09-01 13:59:04'),
(6, 'ECE', '2020-2021', '1', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 14:03:26', '2025-09-01 14:04:01'),
(7, 'ECE', '2021-2022', '2', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 14:04:57', '2025-09-01 14:04:57'),
(8, 'ECE', '2022-2023', '3', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:47:49', '2025-09-01 19:47:49'),
(9, 'ECE', '2023-2024', '4', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:49:36', '2025-09-01 19:49:36'),
(10, 'BBA', '2020-2021', '1', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:56:08', '2025-09-01 19:56:08'),
(11, 'BBA', '2020-2021', '2', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:57:01', '2025-09-01 19:57:01'),
(12, 'BBA', '2022-2023', '3', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:58:02', '2025-09-01 19:58:02'),
(13, 'BBA', '2023-2024', '4', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 19:58:50', '2025-09-01 19:58:50'),
(14, 'BMB', '2020-2021', '1', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 20:00:53', '2025-09-01 20:00:53'),
(15, 'BMB', '2021-2022', '2', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 20:03:25', '2025-09-01 20:03:25'),
(16, 'BMB', '2022-2023', '3', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 20:04:21', '2025-09-01 20:04:21'),
(17, 'BMB', '2023-2024', '4', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 20:05:02', '2025-09-01 20:05:02');

-- --------------------------------------------------------

--
-- Table structure for table `editor_uploadables`
--

CREATE TABLE `editor_uploadables` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `editor_upload_id` bigint(20) UNSIGNED NOT NULL,
  `uploadable_type` varchar(255) NOT NULL,
  `uploadable_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `editor_uploadables`
--

INSERT INTO `editor_uploadables` (`id`, `editor_upload_id`, `uploadable_type`, `uploadable_id`, `created_at`, `updated_at`) VALUES
(2, 31, 'App\\Models\\News', 26, '2025-08-29 05:55:07', '2025-08-29 05:55:07'),
(4, 32, 'App\\Models\\News', 34, '2025-08-29 08:14:54', '2025-08-29 08:14:54'),
(23, 61, 'App\\Models\\AdmissionPost', 1, '2025-08-31 17:23:47', '2025-08-31 17:23:47');

-- --------------------------------------------------------

--
-- Table structure for table `editor_uploads`
--

CREATE TABLE `editor_uploads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `path` varchar(1024) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `draft_token` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `mime` varchar(191) DEFAULT NULL,
  `size` bigint(20) UNSIGNED DEFAULT NULL,
  `original_name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `editor_uploads`
--

INSERT INTO `editor_uploads` (`id`, `path`, `filename`, `draft_token`, `created_at`, `updated_at`, `mime`, `size`, `original_name`) VALUES
(31, 'uploads/post-images/extr-1756129679.PNG', 'extr-1756129679.PNG', NULL, '2025-08-25 07:47:59', '2025-08-25 07:48:26', 'image/png', 49153, 'extr.PNG'),
(32, 'uploads/post-images/capture-1756476514.PNG', 'capture-1756476514.PNG', NULL, '2025-08-29 08:08:34', '2025-08-29 08:14:54', 'image/png', 282075, 'Capture.PNG'),
(61, 'uploads/post-images/licon.PNG', 'licon.PNG', NULL, '2025-08-31 17:23:28', '2025-08-31 17:23:47', 'image/png', 92378, 'licon.PNG');

-- --------------------------------------------------------

--
-- Table structure for table `faculty_infos`
--

CREATE TABLE `faculty_infos` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `about` longtext DEFAULT NULL,
  `education` longtext DEFAULT NULL,
  `research` longtext DEFAULT NULL,
  `interests` text DEFAULT NULL,
  `official_email` varchar(255) NOT NULL,
  `secondary_email` varchar(255) DEFAULT NULL,
  `primary_phone` varchar(255) NOT NULL,
  `secondary_phone` varchar(255) DEFAULT NULL,
  `active_status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `faculty_infos`
--

INSERT INTO `faculty_infos` (`id`, `name`, `slug`, `designation`, `department`, `image`, `bio`, `about`, `education`, `research`, `interests`, `official_email`, `secondary_email`, `primary_phone`, `secondary_phone`, `active_status`, `created_at`, `updated_at`) VALUES
(1, 'Mannix Sears', 'mannix-sears', 'Eaque sapiente est s', 'Sed at odit officia', 'uploads/faculty/08-sandip-debnath.jpg', 'Cillum velit quam se', 'Facere ut ut et cons', 'Sed cupiditate aliqu', 'Quia esse commodi do', 'Eveniet cupiditate', '<EMAIL>', '<EMAIL>', '+****************', '+****************', 'active', '2025-08-08 08:40:41', '2025-08-08 08:44:41'),
(2, 'Rylee Levine', 'rylee-levine', 'Amet id sed molest', 'Ea quasi dicta est c', 'uploads/faculty/03-hafsa-binte-rashid.jpg', 'Non in similique ill', 'Consectetur officia', 'Nostrud ad animi of', 'Dolor accusantium om', 'Natus sapiente do se', '<EMAIL>', '<EMAIL>', '+****************', '+****************', 'active', '2025-08-08 08:45:28', '2025-08-08 08:45:28'),
(3, 'Nicholas Roberts', 'nicholas-roberts', 'Quia consectetur of', 'Aliquid pariatur Se', 'uploads/faculty/07-asmena.jpg', 'Maxime perspiciatis', '<p class=\"text-node\" style=\"text-align: left;\">Fugiat anim totam as</p>', '<p class=\"text-node\" style=\"text-align: left;\">Duis sed distinctio</p>', '<p class=\"text-node\" style=\"text-align: left;\">Maiores maxime natus</p><p class=\"text-node\" style=\"text-align: left;\"></p>', 'Non sunt suscipit do', '<EMAIL>', '<EMAIL>', '+1 (675) 837-5085', '+1 (329) 903-2889', 'active', '2025-08-08 08:45:59', '2025-08-31 16:30:59');

-- --------------------------------------------------------

--
-- Table structure for table `faculty_schedules`
--

CREATE TABLE `faculty_schedules` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `faculty_name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `scheduleEmbedLink` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `faculty_schedules`
--

INSERT INTO `faculty_schedules` (`id`, `faculty_name`, `department`, `scheduleEmbedLink`, `created_at`, `updated_at`) VALUES
(1, 'Tanjida Supta', 'BBA', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-08-08 08:49:08', '2025-09-01 13:27:11'),
(2, 'Asmena Mowla', 'BMB', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:29:14', '2025-09-01 13:29:14'),
(3, 'Shaheena Amin', 'BMB', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:31:04', '2025-09-01 13:31:04'),
(4, 'S. M. Akib Al Faisal', 'CSE', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:32:15', '2025-09-01 13:32:15'),
(5, 'Md. Tufecul Islam', 'CSE', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&ctz=Asia%2FDhaka', '2025-09-01 13:32:57', '2025-09-01 13:32:57'),
(6, 'Salma Afrose', 'MATH', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:34:19', '2025-09-01 13:34:19'),
(7, 'Sandip Debnath', 'ECE', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:35:07', '2025-09-01 13:35:07'),
(8, 'Hafsa Binta Rashid', 'BBA', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:36:07', '2025-09-01 13:36:07'),
(9, 'Mita Rani Paul', 'BBA', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:37:19', '2025-09-01 13:37:19'),
(10, 'Rozana Afsin', 'ENGLISH', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:38:32', '2025-09-01 13:38:32'),
(11, 'Shagufta Tulip', 'BMB', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:39:34', '2025-09-01 13:39:34'),
(12, 'Kazi Soha', 'BMB', 'https://www.google.com/calendar/embed?mode=WEEK&height=600&wkst=7&bgcolor=%23FFFFFF&src=<EMAIL>&color=%23865A5A&ctz=Asia%2FDhaka', '2025-09-01 13:40:16', '2025-09-01 13:40:16');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `file_uploads`
--

CREATE TABLE `file_uploads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('all_activity','events','cse','ece','bba','bmb','excellent_results') NOT NULL,
  `file` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `galleries`
--

CREATE TABLE `galleries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('all_activity','events','cse','ece','bba','bmb','excellent_results') NOT NULL,
  `image` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `galleries`
--

INSERT INTO `galleries` (`id`, `title`, `description`, `category`, `image`, `created_at`, `updated_at`) VALUES
(1, 'Sample Gallery Item', 'This is a sample gallery item for testing.', 'events', 'uploads/galleries/capture.PNG', '2025-08-09 12:37:22', '2025-08-29 13:04:27');

-- --------------------------------------------------------

--
-- Table structure for table `governing_bodies`
--

CREATE TABLE `governing_bodies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `display_order` int(11) NOT NULL,
  `profile_URL` varchar(255) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `governing_bodies`
--

INSERT INTO `governing_bodies` (`id`, `name`, `designation`, `display_order`, `profile_URL`, `profile_image`, `created_at`, `updated_at`) VALUES
(1, 'Nichole Wiley', 'Et unde ipsam non do', 1, 'https://www.lohypysahiqy.us', 'uploads/governingbody/01-principal.jpg', '2025-08-08 08:47:38', '2025-08-08 08:48:00');

-- --------------------------------------------------------

--
-- Table structure for table `homepage_ads`
--

CREATE TABLE `homepage_ads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ad_title` varchar(255) DEFAULT NULL,
  `program_info_title` varchar(255) DEFAULT NULL,
  `session` varchar(255) DEFAULT NULL,
  `offer_title` varchar(255) DEFAULT NULL,
  `offer_text` text DEFAULT NULL,
  `active_status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `homepage_ads`
--

INSERT INTO `homepage_ads` (`id`, `ad_title`, `program_info_title`, `session`, `offer_title`, `offer_text`, `active_status`, `created_at`, `updated_at`) VALUES
(1, 'Special Admission Offer', 'Computer Science Program', 'Spring 2024', '50% Scholarship Available', 'Apply now and get up to 50% scholarship on tuition fees.', 'inactive', '2025-08-08 12:38:56', '2025-08-29 12:40:01'),
(3, 'Expedita vel dolor m', 'Aut excepteur reicie', 'Earum vel elit nisi', 'Aut eveniet consequ', 'Enim sed et nulla il', 'inactive', '2025-08-08 12:58:29', '2025-08-25 13:11:56');

-- --------------------------------------------------------

--
-- Table structure for table `image_sliders`
--

CREATE TABLE `image_sliders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `heading` varchar(255) NOT NULL,
  `caption` text NOT NULL,
  `order` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `image_sliders`
--

INSERT INTO `image_sliders` (`id`, `title`, `image`, `heading`, `caption`, `order`, `created_at`, `updated_at`) VALUES
(1, 'Suscipit aut magna q', 'uploads/image-sliders/extr.PNG', 'Tempor culpa nisi pr', 'Reprehenderit enim', 34, '2025-08-29 13:05:11', '2025-08-29 13:05:11');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_06_11_164511_create_news_table', 1),
(5, '2025_07_08_195853_create_noticeboard_table', 2),
(6, '2025_07_09_194100_create_noticeboards_table', 3),
(7, '2025_07_10_175042_user_id', 4),
(8, '2025_07_10_175634_user_id', 5),
(15, '2025_07_18_102937_create_governing_bodies_table', 6),
(16, '2025_07_18_103605_create_faculty_infos_table', 6),
(17, '2025_08_05_043912_add_category_to_noticeboards_table', 6),
(18, '2025_08_06_185513_create_campus_activities_table', 6),
(19, '2025_08_07_141948_create_class_schedules_table', 6),
(20, '2025_08_07_142016_create_faculty_schedules_table', 6),
(21, '2025_08_08_181231_create_admission_spots_table', 7),
(22, '2025_08_08_181342_create_tuition_fees_table', 7),
(23, '2025_08_08_181424_create_admission_posts_table', 8),
(24, '2025_08_08_181412_create_tuition_fees_table', 9),
(25, '2025_08_08_181434_create_admission_promo_images_table', 9),
(26, '2025_08_08_181444_create_homepage_ads_table', 9),
(27, '2025_08_08_181455_create_admission_modals_table', 9),
(28, '2025_08_09_182305_create_galleries_table', 10),
(29, '2025_08_09_182339_create_file_uploads_table', 10),
(30, '2025_08_11_000001_create_permission_tables', 11),
(31, '2025_08_12_170004_add_new_field_to_permissions_table', 12),
(32, '2025_08_12_174304_add_new_field_to_roless_table', 12),
(33, '2025_08_21_085007_create_image_sliders_table', 13),
(34, '2025_08_22_101240_modify_news_table', 14),
(35, '2025_08_22_135753_editor_uploads', 14),
(36, '2025_08_23_085310_alter_editor_uploads_to_morph', 15),
(37, '2025_08_26_070451_add_title_to_image_sliders_table', 16),
(38, '2025_08_29_035237_create_editor_uploadables_table', 17),
(39, '2025_08_29_041239_alter_admission_circular_content_to_longtext_in_your_table', 17),
(40, '2025_08_29_041611_add_published_at_to_news_table', 17),
(41, '2025_08_29_053520_update_faculty_infos_table', 17),
(42, '2025_08_29_054723_add_published_at_table', 18),
(44, '2025_08_29_041611_add_published_at', 19),
(45, '2025_08_29_054723_add_published_at_noticeboard_table', 20),
(46, '2025_08_29_134723_add_active_status_to_news_table', 20);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(1, 'App\\Models\\User', 2),
(1, 'App\\Models\\User', 3),
(2, 'App\\Models\\User', 7),
(3, 'App\\Models\\User', 8),
(3, 'App\\Models\\User', 9);

-- --------------------------------------------------------

--
-- Table structure for table `news`
--

CREATE TABLE `news` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `meta_tag_title` varchar(255) DEFAULT NULL,
  `meta_tag_description` varchar(255) DEFAULT NULL,
  `excerpt` text DEFAULT NULL,
  `published_at` varchar(255) DEFAULT NULL,
  `active_status` enum('active','inactive') NOT NULL DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `news`
--

INSERT INTO `news` (`id`, `title`, `slug`, `content`, `image`, `created_at`, `updated_at`, `meta_tag_title`, `meta_tag_description`, `excerpt`, `published_at`, `active_status`) VALUES
(1, 'Laravel 12 starter kit released!', 'laravel-12-starter-kit-released', 'Laravel 12 is an awesome rendition in the latest dev update. So much potential it has.\r\nNow we will have all the power it has.', 'uploads/news/oKLDTrXb2ekwDWMJwHStWI7e43KfavZnPJL8BQhu.png', '2025-06-27 08:57:45', '2025-06-29 08:31:38', NULL, NULL, NULL, NULL, 'active'),
(2, 'IntertiaJs v2 Releases', 'intertiajs-v2-releases', 'Inertia.js v2 was officially released with a stable version published on December 13, 2024, and it introduces significant advancements, including asynchronous requests, deferred props, prefetching, and polling, all built upon a completely rewritten request handling layer. This update dropped support for Vue 2 and requires Laravel 10+ and PHP 8.1+', 'uploads/news/eAqrFq3oxWeX3Om77T0nqdjrsgyi5D5RhKgVpWlZ.png', '2025-06-27 09:36:58', '2025-06-29 08:14:33', NULL, NULL, NULL, NULL, 'active'),
(3, 'React router v6', 'react-router-v6', 'React router v6 released', 'uploads/news/734TrlvdZyLDjY8z5uTjGGU7U5WtrVrBs4ap5q9a.png', '2025-06-27 10:30:31', '2025-06-29 08:21:41', NULL, NULL, NULL, NULL, 'active'),
(9, 'Laravel admin panel released', 'laravel-admin-panel-released', 'it is a very good news', 'uploads/news/5fM5KoriCuRMsljPW7J9XMk4jFkApxD4tEGdtvwK.png', '2025-07-06 11:49:41', '2025-07-06 11:49:41', NULL, NULL, NULL, NULL, 'active'),
(11, 'Filment v4 beta released', 'filment-v4-beta-released', 'The Filament v4 Beta will officially launch on June 10, 2025 during Laravel Live UK. It\'s important to remember that this is a beta release, so don\'t use it for production apps just yet. But feel free to play around with it, explore the new features, and give feedback to the team.', 'uploads/news/6acnZECCfHKAlvgwthPpKSvqohO5kQq45cWUpy6T.png', '2025-07-06 12:21:17', '2025-07-06 12:21:27', NULL, NULL, NULL, NULL, 'active'),
(12, 'Et dolore laborum ne', 'et-dolore-laborum-ne', 'Odio incididunt quis', 'uploads/news/3qI2rfE0rzEquMuQt26o9aD9WHi5vcoYgiC99sIS.png', '2025-07-08 10:09:54', '2025-07-08 10:09:54', NULL, NULL, NULL, NULL, 'active'),
(13, 'Provident eum error', 'provident-eum-error', 'Numquam est a ipsam', 'uploads/news/dd7TcnLM2OxRynSqwv9UXCjk3na9aelE4wxN7COg.png', '2025-07-08 10:10:13', '2025-07-08 10:10:13', NULL, NULL, NULL, NULL, 'active'),
(14, 'Ut vitae cupiditate', 'ut-vitae-cupiditate', 'Atque ut velit quibu', 'uploads/news/WWlQfSL0gu0Z1ksStBs77P8LS6VYonsetsXhesyt.png', '2025-07-08 10:10:34', '2025-07-08 10:10:34', NULL, NULL, NULL, NULL, 'active'),
(15, 'Enim natus provident', 'enim-natus-provident', 'Est laudantium et', 'uploads/news/aNkCelFoEq988UMvNtnts9GMRfHNf8yBNp371I13.png', '2025-07-08 10:10:51', '2025-07-08 10:10:51', NULL, NULL, NULL, NULL, 'active'),
(16, 'Est non est dolor pl', 'est-non-est-dolor-pl', 'Aut incidunt eligen', 'uploads/news/iJWWvctMpNx2yjVNwF1R7X0HEckitWf2XS628Baa.png', '2025-07-08 10:11:07', '2025-07-08 10:11:07', NULL, NULL, NULL, NULL, 'active'),
(17, 'Omnis eaque tempore', 'omnis-eaque-tempore', 'Dolor eos in duis oc', 'uploads/news/xOqh6MkECw8ZNcKinOwSjG4uQSLtOEqqAHOoNGob.png', '2025-07-08 10:11:35', '2025-07-08 10:11:35', NULL, NULL, NULL, NULL, 'active'),
(18, 'Velit et est et aliq97', 'velit-et-est-et-aliq97', 'Rerum placeat delen', 'uploads/news/V0SSxsVH0g9KIaRJcHjZMF6PFYG9abNTP9Urctgm.png', '2025-07-08 10:11:57', '2025-07-09 12:00:27', NULL, NULL, NULL, NULL, 'active'),
(19, 'Quaerat accusamus re25', 'quaerat-accusamus-re25', 'Ut magni architecto', 'uploads/news/PnhxmYGtSrYCTFKeYLHDY2AImVROJ9TgTJ1YRY0m.png', '2025-07-09 11:58:51', '2025-07-09 11:58:51', NULL, NULL, NULL, NULL, 'active'),
(20, 'অধ্যক্ষ প্রফেসর মো: ফয়েজ হোসেন স্যারের মৃত্যুতে আমরা গভীরভাবে শোকাহত', 'odhzksh-prfesr-mo-fyej-hosen-szarer-mrritzute-amra-gveervabe-sokaht', 'ন্যাশনাল ইনস্টিটিউট অব সায়েন্স অ্যান্ড টেকনোলজি’র অধ্যক্ষ প্রফেসর মো: ফয়েজ হোসেন বৃহস্পতিবার রাত ১১.৪০ মিনিটে চিকিৎসাধীন অবস্থায় স্কায়ার হাসপাতালে মৃত্যুবরণ করেছেন (ইন্নালিল্লাহি ওয়া ইন্না ইলাইহি রাজিউন)। মৃত্যুকালে তিন কন্যা, স্ত্রীসহ অসংখ্য ভক্ত রেখে গেছেন। শুক্রবার সিদ্ধেশ্বরী কলেজ মাঠে ও দিলুরোড জামে মসজিদে জানাযা শেষে মিরপুর বুদ্ধিজীবী কবরস্থানে দাফন করা হয়। উল্লেখ্য, তিনি বাংলাদেশ কলেজ শিক্ষক সমিতি ((বাকশিস) এর সাধারণ সম্পাদক, সিদ্ধেশ্বরী কলেজের সাবেক অধ্যক্ষ, প্রফেশনাল ইনস্টিটিউট এসোসিয়েশন অব ন্যাশনাল ইউনিভার্সিটি\'র আহ্বায়ক এর দায়িত্বে ছিলেন। স্যারের মৃত্যুতে ড্যাফোডিল পরিবারের সকল সদস্য গভীর শোকাহত।', 'uploads/news/RWR4oSHzNpaPQYLgsuOM20lRDMniXjIrPr1pKzLF.jpg', '2025-08-06 10:19:59', '2025-08-06 10:19:59', NULL, NULL, NULL, NULL, 'active'),
(21, 'Minim sed nihil aspeed', 'minim-sed-nihil-aspeed', '<h1 class=\"heading-node\" style=\"text-align: left;\">key info that will show in dashboarded:</h1><ul class=\"list-node\"><li><p class=\"text-node\" style=\"text-align: left;\">1. wellcome USERNAME,</p></li><li><p class=\"text-node\" style=\"text-align: left;\">Date,</p></li><li><p class=\"text-node\" style=\"text-align: left;\">NIST wCMS , v1.1 [it will be bold font like text based logo]</p></li></ul><p class=\"text-node\" style=\"text-align: left;\"></p><ol class=\"list-node\"><li><p class=\"text-node\" style=\"text-align: left;\">2.</p></li><li><p class=\"text-node\" style=\"text-align: left;\">in two row [each row for card rounded] , eight component specific information will show, total notice, total news, totla faculty, total governingboady, total users, total gallery, total class schedule, each card will habe view/icon to go to that section, example faculty card view icon will redirect to faculty profile section.</p></li><li><p class=\"text-node\" style=\"text-align: left;\">3. three section will list 3 list item, first section recent notices, recent news, recent admission campaign post, end of each section will have a create button to redirect to notice create , news create, and admission page</p></li></ol><p class=\"text-node\" style=\"text-align: left;\"></p><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/qboWecrQVHHPFwHBb7EhQeXlYIXn5dOARFIord4v.jpg', '2025-08-23 09:15:41', '2025-08-23 11:50:44', 'Reprehenderit sapieeed', 'Voluptatem Est venied', 'Tempor aliqua Aut fed', NULL, 'active'),
(22, 'Consequat Est in qu', 'consequat-est-in-qu', '<h1 class=\"heading-node\" style=\"text-align: left;\"><strong>key info that will show in dashboarded:</strong></h1><ul class=\"list-node\"><li><p class=\"text-node\" style=\"text-align: left;\">1. wellcome USERNAME,</p></li><li><p class=\"text-node\" style=\"text-align: left;\">Date,</p></li><li><p class=\"text-node\" style=\"text-align: left;\">NIST wCMS , v1.1 [it will be bold font like text based logo]</p></li></ul><p class=\"text-node\" style=\"text-align: left;\"></p><img src=\"blob:http://localhost:8000/570e02cc-ed05-45b7-8b57-dba5b9a0105d\" alt=\"\" title=\"\" id=\"3mcwrryh0\" width=\"120\" height=\"92\" filename=\"laravel12f.PNG\" data-align=\"center\"><ol class=\"list-node\"><li><p class=\"text-node\" style=\"text-align: left;\">2.</p></li><li><p class=\"text-node\" style=\"text-align: left;\">in two row [each row for card rounded] , eight component specific information will show, total notice, total news, totla faculty, total governingboady, total users, total gallery, total class schedule, each card will habe view/icon to go to that section, example faculty card view icon will redirect to faculty profile section.</p></li></ol><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/capture.PNG', '2025-08-23 11:55:37', '2025-08-23 11:55:37', 'Voluptatem amet qua', 'Animi ipsum esse qu', 'Quis voluptatem dolo', NULL, 'active'),
(23, 'Reprehenderit offic', 'reprehenderit-offic', '<p class=\"text-node\" style=\"text-align: left;\">Iusto laborum cupida.</p><img src=\"blob:http://localhost:8000/42f5adde-021c-4279-bbb5-ad5baf2bdd17\" alt=\"\" title=\"\" id=\"j3gwbqav5\" width=\"120\" height=\"80\" filename=\"Capture.PNG\" data-align=\"center\"><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/laravel12f.PNG', '2025-08-23 19:45:12', '2025-08-23 19:45:12', 'Dolorum dolores pari', 'Est laborum hic ipsu', 'Sit vitae dolor veli', NULL, 'active'),
(24, 'Dolores odio omnis m', 'dolores-odio-omnis-m', '<p class=\"text-node\" style=\"text-align: left;\">Dolor tempora sit, r.</p><img src=\"blob:http://localhost:8000/89f03f0d-1639-47c7-b279-13485e79cc41\" alt=\"\" title=\"Upload failed: Upload failed: 419 unknown status\" id=\"uivul9zp4\" width=\"120\" height=\"80\" filename=\"Capture.PNG\" data-align=\"center\"><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/capture-1756001265.PNG', '2025-08-23 20:07:45', '2025-08-23 20:07:45', 'Nobis hic veniam as', 'Et rem ipsum nobis n', 'Tempore omnis qui d', NULL, 'active'),
(26, 'Ut qui rerum nisi ul', 'ut-qui-rerum-nisi-ul', '<p class=\"text-node\" style=\"text-align: left;\">Laborum ab corrupti.</p><p class=\"text-node\" style=\"text-align: left;\">zdxhncjh</p><p class=\"text-node\" style=\"text-align: left;\">cutu</p><p class=\"text-node\" style=\"text-align: left;\">dxhfhcgj</p><img src=\"http://localhost:8000/storage/uploads/post-images/extr-1756129679.PNG\" alt=\"\" title=\"\" id=\"31\" width=\"893\" height=\"352\" filename=\"extr.PNG\" data-align=\"center\"><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/frame-01-delay-02s.jpg', '2025-08-24 09:02:00', '2025-08-25 07:48:26', 'Voluptas similique m', 'Placeat culpa nemo', 'Velit et quo dolore', NULL, 'active'),
(31, 'Nihil hic rem dolore', 'nihil-hic-rem-dolore', '<p class=\"text-node\" style=\"text-align: left;\">Sit quibusdam deleni.</p>', 'uploads/news/laravel12f-1756466113.PNG', '2025-08-29 05:15:13', '2025-08-29 05:15:13', 'Ullam voluptatem eos', 'Velit et culpa dolo', 'Qui illum velit bla', NULL, 'active'),
(32, 'Quos quis sunt dolor', 'quos-quis-sunt-dolor', '<p class=\"text-node\" style=\"text-align: left;\">Voluptas voluptate v.jk</p>', 'uploads/news/w1FcerM1UncaM3WyRTp0czHHYUrEXp7abl0VpMhP.png', '2025-08-29 07:26:58', '2025-08-29 07:42:17', 'Fugiat fuga Ipsum', 'Quis minus neque asp', 'Sint ex aliquam qui', NULL, 'active'),
(33, 'Mollitia aut exercit', 'mollitia-aut-exercit', '<p class=\"text-node\" style=\"text-align: left;\">Laborum. Fugiat, qua.</p>', 'uploads/news/frame-00-delay-02s-1756474978.jpg', '2025-08-29 07:42:58', '2025-08-29 08:07:58', 'Dolore quas suscipit', 'Tenetur molestiae pe', 'Ad consequatur Sed', '2025-08-28 18:00:00', 'active'),
(34, 'Et itaque minima ea', 'et-itaque-minima-ea', '<p class=\"text-node\" style=\"text-align: left;\">Consequatur minim qu.</p><img src=\"http://localhost:8000/storage/uploads/post-images/capture-1756476514.PNG\" alt=\"\" title=\"\" id=\"32\" width=\"397.28000000000003\" height=\"266.0627092846271\" filename=\"Capture.PNG\" data-align=\"center\"><p class=\"text-node\" style=\"text-align: left;\"></p><p class=\"text-node\" style=\"text-align: left;\"><span>bmbm,n,</span></p><p class=\"text-node\" style=\"text-align: left;\"></p>', 'uploads/news/extr.PNG', '2025-08-29 08:14:54', '2025-08-29 08:14:54', 'Voluptas dolore quib', 'Vero tempora laboris', 'Debitis sunt et quod', '2025-08-29 00:00:00', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `noticeboards`
--

CREATE TABLE `noticeboards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `category` varchar(255) DEFAULT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `active_status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `noticeboards`
--

INSERT INTO `noticeboards` (`id`, `title`, `slug`, `category`, `content`, `image`, `attachment`, `published_at`, `active_status`, `created_at`, `updated_at`) VALUES
(14, 'Eos aspernatur sed c', 'eos-aspernatur-sed-c', NULL, 'Laboris molestias fu5', 'uploads/notices/images/capturepng', 'uploads/notices/attachments/laravel12fpng', NULL, 'active', '2025-07-13 08:15:19', '2025-07-13 08:17:16'),
(15, 'Doloremque elit rep', 'doloremque-elit-rep', NULL, 'Dolorum exercitation', 'uploads/notices/images/laravel12fpng', 'uploads/notices/attachments/licon-1752417014.PNG', NULL, 'active', '2025-07-13 08:30:14', '2025-07-13 08:30:14'),
(16, 'Consequuntur iste mo', 'consequuntur-iste-mo', NULL, 'Commodo voluptatum c', 'uploads/notices/images/frame-02-delay-02sjpg', 'uploads/notices/attachments/capture.PNG', NULL, 'active', '2025-07-13 08:30:35', '2025-07-13 08:30:35'),
(17, 'Reprehenderit earum', 'reprehenderit-earum', NULL, 'Nesciunt eius ut no', 'uploads/notices/images/liconpng', 'uploads/notices/attachments/laravel12f.PNG', NULL, 'active', '2025-07-13 08:30:55', '2025-07-13 08:30:55'),
(18, 'Velit deserunt exerc', 'velit-deserunt-exerc', NULL, 'Tempora id dolore pr', 'uploads/notices/images/frame-06-delay-02sjpg', 'uploads/notices/attachments/laravel12f-1752417306.PNG', NULL, 'active', '2025-07-13 08:35:06', '2025-07-13 08:35:06'),
(19, 'Unde accusamus tempo', 'unde-accusamus-tempo', NULL, 'Est maxime recusanda', 'uploads/notices/images/capturepng', 'uploads/notices/attachments/licon-1752417323.PNG', NULL, 'active', '2025-07-13 08:35:23', '2025-07-13 08:35:23'),
(20, 'Voluptatibus duis se', 'voluptatibus-duis-se', NULL, 'Deleniti quam quam u', 'uploads/notices/images/frame-05-delay-02sjpg', 'uploads/notices/attachments/laravel12f-1752417339.PNG', NULL, 'active', '2025-07-13 08:35:39', '2025-07-13 08:35:39'),
(21, 'Autem dolorem delect', 'autem-dolorem-delect', NULL, 'Aute deserunt ipsam', 'uploads/notices/images/laravel12fpng', 'uploads/notices/attachments/frame-06-delay-02s.jpg', NULL, 'active', '2025-07-13 08:36:04', '2025-07-13 08:36:04'),
(22, 'At reprehenderit aut', 'at-reprehenderit-aut', NULL, 'Non praesentium ex o', 'uploads/notices/images/mofi-ajpg', 'uploads/notices/attachments/frame-07-delay-02s.jpg', NULL, 'active', '2025-07-13 08:36:25', '2025-07-13 08:36:25'),
(23, 'Amet eligendi aliquist', 'amet-eligendi-aliquist', NULL, 'Fugiat optio sapie', 'uploads/notices/images/frame-02-delay-02s.jpg', 'uploads/notices/attachments/frame-02-delay-02s.jpg', NULL, 'active', '2025-07-13 08:36:46', '2025-07-13 10:58:49'),
(24, 'Accusantium voluptat', 'accusantium-voluptat', NULL, 'Minus eligendi in et', 'uploads/notices/images/laravel12fpng', 'uploads/notices/attachments/licon-1752417420.PNG', NULL, 'active', '2025-07-13 08:37:00', '2025-08-05 04:27:23'),
(25, 'Perspiciatis volupt', 'perspiciatis-volupt', NULL, 'Et doloribus animi', 'uploads/notices/images/capture.PNG', 'uploads/notices/attachments/laravel12f-1752421264.PNG', NULL, 'active', '2025-07-13 09:41:04', '2025-08-05 04:26:36'),
(26, 'Aute ipsum nulla sitlu', 'aute-ipsum-nulla-sitlu', NULL, 'Consequat Debitis n', 'uploads/notices/images/frame-06-delay-02s.jpg', 'uploads/notices/attachments/licon-1752424909.PNG', NULL, 'active', '2025-07-13 10:41:49', '2025-08-05 04:26:20'),
(27, 'Culpa quia autem re', 'culpa-quia-autem-re', NULL, 'Fugiat tempor occae', 'uploads/notices/images/frame-06-delay-02s-1752945814.jpg', 'uploads/notices/attachments/extr.PNG', NULL, 'active', '2025-07-19 11:23:34', '2025-08-05 04:26:02'),
(28, 'Qui odit elit conse', 'qui-odit-elit-conse', NULL, 'Illum eos amet dol', 'uploads/notices/images/frame-06-delay-02s-1754389285.jpg', 'uploads/notices/attachments/frame-06-delay-02s-1754389285.jpg', NULL, 'active', '2025-08-05 04:21:26', '2025-08-05 04:25:41'),
(29, 'Iure eu consectetur', 'iure-eu-consectetur', NULL, 'Qui aute accusantium', NULL, NULL, NULL, 'active', '2025-08-05 04:28:16', '2025-08-05 04:28:16'),
(30, 'Nostrud voluptatem a Nostrud voluptatem a Nostrud voluptatem a', 'nostrud-voluptatem-a-nostrud-voluptatem-a-nostrud-voluptatem-a', NULL, 'Aute molestiae volup', NULL, NULL, NULL, 'active', '2025-08-05 04:42:25', '2025-08-05 09:17:31'),
(31, 'Officia nemo exercit1175', 'officia-nemo-exercit1175', 'cse', 'Velit in est volupta', 'uploads/notices/images/ad-banner2.jpg', NULL, NULL, 'active', '2025-08-09 12:42:35', '2025-08-10 13:10:48');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `label` varchar(255) DEFAULT NULL,
  `module` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `is_active`, `description`, `label`, `module`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'view-user', 1, 'can view user', 'View user', 'users', 'web', '2025-08-11 12:51:33', '2025-08-19 07:58:19'),
(2, 'create-user', 1, 'can create user', 'create user', 'users', 'web', '2025-08-11 12:51:33', '2025-08-19 07:59:01'),
(3, 'edit-user', 1, 'can edit user', 'Edit User', 'users', 'web', '2025-08-11 12:51:33', '2025-08-19 07:59:34'),
(4, 'delete-user', 1, 'can delete user', 'delete user', 'users', 'web', '2025-08-11 12:51:33', '2025-08-19 08:00:40'),
(5, 'view-roles', 1, 'can view roles', 'view roles', 'roles', 'web', '2025-08-11 12:51:33', '2025-08-19 08:04:39'),
(6, 'create-role', 1, 'can create role', 'Create Role', 'roles', 'web', '2025-08-11 12:51:33', '2025-08-19 08:13:12'),
(7, 'edit-role', 1, 'can edit role', 'Edit Role', 'roles', 'web', '2025-08-11 12:51:33', '2025-08-19 08:17:15'),
(8, 'delete-role', 1, 'can delete roles', 'delete role', 'roles', 'web', '2025-08-11 12:51:33', '2025-08-19 08:18:00'),
(9, 'view-permissions', 1, 'can view permissions', 'view permissions', 'permissions', 'web', '2025-08-11 12:51:33', '2025-08-19 08:22:59'),
(10, 'create-permissions', 1, 'can create permissions', 'create permissions', 'permissions', 'web', '2025-08-11 12:51:33', '2025-08-19 08:24:58'),
(11, 'edit-permissions', 1, 'can edit permissions', 'edit permissions', 'permissions', 'web', '2025-08-11 12:51:33', '2025-08-19 08:26:07'),
(12, 'delete-permissions', 1, 'can delete permissions', 'delete permissions', 'permissions', 'web', '2025-08-11 12:51:33', '2025-08-19 08:27:01'),
(13, 'dashboard-access', 1, NULL, NULL, '', 'web', '2025-08-11 12:51:33', '2025-08-11 12:51:33'),
(15, 'create-post', 1, 'Can create noticeboard post', 'Create Post', 'noticeboard', 'web', '2025-08-16 07:57:45', '2025-08-16 07:57:45');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `label` varchar(255) DEFAULT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `is_active`, `description`, `label`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'super_admin', 1, NULL, 'Super Admin', 'web', '2025-08-11 12:51:33', '2025-08-11 12:51:33'),
(2, 'admin', 1, 'manage website', 'Admin', 'web', '2025-08-11 12:51:33', '2025-08-16 13:53:13'),
(3, 'editor', 1, NULL, 'Editor', 'web', '2025-08-11 12:51:33', '2025-08-11 12:51:33');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(15, 2);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('l4xjUsVHPkHimc4UB4ENmhaoP6uxe235zYYDCQq5', 3, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiWWZyYTVkVnR2U3NBUFlCcE8ya0ExWUlIWFNoQmtZeUdyRHFydmw1WCI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MztzOjY6Il9mbGFzaCI7YToyOntzOjM6Im5ldyI7YTowOnt9czozOiJvbGQiO2E6MDp7fX1zOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo1MToiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL3NjaGVkdWxlL2ZhY3VsdHkvMS9lZGl0Ijt9fQ==', 1756757112),
('vrVLTGI7VpV4ACUk99D6wZSXwbLu63jeTnUnl2AD', 3, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUDlGNFZhT25QN0M4YTBEQjVpeVRyd3FuYmM5b2Juc3EwWnJkTG02ZCI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MztzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo2ODoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluL3NjaGVkdWxlP2NsYXNzX3BhZ2U9MSZ0YWI9Y2xhc3Nfc2NoZWR1bGUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1756779853);

-- --------------------------------------------------------

--
-- Table structure for table `tuition_fees`
--

CREATE TABLE `tuition_fees` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `image` varchar(255) NOT NULL,
  `program_name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tuition_fees`
--

INSERT INTO `tuition_fees` (`id`, `image`, `program_name`, `department`, `created_at`, `updated_at`) VALUES
(2, 'uploads/tuition-fees/cse-fees-2025.jpg', 'Computer Science and Engineering', 'CSE', '2025-08-08 12:49:54', '2025-08-08 12:49:54');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'AKIB FAISAL', '<EMAIL>', NULL, '$2y$12$2Ta/24.Ee.U.yiCkb3wsCedVXcesa7Gs/JZ7OOFYoIRFv5gMruP26', '6UqarOMkrsNJ7BxUQYC2UHCXtek2PnKGazSVSai7GOpZPci3oVTOxTJ0tkhX', '2025-06-11 11:10:21', '2025-06-11 11:10:21'),
(2, 'John Diggle', '<EMAIL>', NULL, '$2y$12$9.18HaOUvZoJ9nhryrekdu1YJ5wP7sZnF/83YqGHv8do.gU3QQuHC', NULL, '2025-07-09 11:58:19', '2025-07-09 11:58:19'),
(3, 'Test Admin', '<EMAIL>', NULL, '$2y$12$Qju7Ce4ee4rkEVg5MubLQOI3b/Rrtkys.gVHuCOzdeJrpQ5aDT1XG', 'r5XQQzUKZY5RsLkpmieAok0RtWASgFa6PkMWe2HgFUZKmAezn4X2IjuG4zJR', '2025-08-11 12:55:34', '2025-08-11 12:55:34'),
(7, 'Hilel Anderson', '<EMAIL>', NULL, '$2y$12$SO.Bx8cz4HQfzA2ycZsxwOr2zZQDVu0LYuwitB2ymgPFYTedbPnwe', NULL, '2025-08-20 09:33:35', '2025-08-20 09:33:35'),
(8, 'Latifah Raymond', '<EMAIL>', NULL, '$2y$12$y88ifMI6HroExmcueTtoCOqNvxFSwSuGR.LD5n136r/7CKrXQTUn2', NULL, '2025-08-20 10:07:16', '2025-08-20 10:07:16'),
(9, 'Kathleen Peters', '<EMAIL>', NULL, '$2y$12$cfPUjfY9C2KoYUfaky0hVezmqBRMjwcbpQhbrbyB04PtMj9RJ3WsW', NULL, '2025-08-20 11:39:27', '2025-08-20 11:39:27');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admission_modals`
--
ALTER TABLE `admission_modals`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admission_posts`
--
ALTER TABLE `admission_posts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admission_promo_images`
--
ALTER TABLE `admission_promo_images`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admission_spots`
--
ALTER TABLE `admission_spots`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `campus_activities`
--
ALTER TABLE `campus_activities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `class_schedules`
--
ALTER TABLE `class_schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `editor_uploadables`
--
ALTER TABLE `editor_uploadables`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `editor_uploadables_unique` (`editor_upload_id`,`uploadable_type`,`uploadable_id`),
  ADD KEY `editor_uploadables_uploadable_type_uploadable_id_index` (`uploadable_type`,`uploadable_id`);

--
-- Indexes for table `editor_uploads`
--
ALTER TABLE `editor_uploads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `editor_uploads_draft_token_index` (`draft_token`);

--
-- Indexes for table `faculty_infos`
--
ALTER TABLE `faculty_infos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `faculty_infos_official_email_unique` (`official_email`);

--
-- Indexes for table `faculty_schedules`
--
ALTER TABLE `faculty_schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `file_uploads`
--
ALTER TABLE `file_uploads`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `galleries`
--
ALTER TABLE `galleries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `governing_bodies`
--
ALTER TABLE `governing_bodies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `homepage_ads`
--
ALTER TABLE `homepage_ads`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `image_sliders`
--
ALTER TABLE `image_sliders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `news`
--
ALTER TABLE `news`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `news_slug_unique` (`slug`);

--
-- Indexes for table `noticeboards`
--
ALTER TABLE `noticeboards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `noticeboards_slug_unique` (`slug`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `tuition_fees`
--
ALTER TABLE `tuition_fees`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admission_modals`
--
ALTER TABLE `admission_modals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admission_posts`
--
ALTER TABLE `admission_posts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `admission_promo_images`
--
ALTER TABLE `admission_promo_images`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `admission_spots`
--
ALTER TABLE `admission_spots`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `campus_activities`
--
ALTER TABLE `campus_activities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `class_schedules`
--
ALTER TABLE `class_schedules`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `editor_uploadables`
--
ALTER TABLE `editor_uploadables`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `editor_uploads`
--
ALTER TABLE `editor_uploads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `faculty_infos`
--
ALTER TABLE `faculty_infos`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `faculty_schedules`
--
ALTER TABLE `faculty_schedules`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `file_uploads`
--
ALTER TABLE `file_uploads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `galleries`
--
ALTER TABLE `galleries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `governing_bodies`
--
ALTER TABLE `governing_bodies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `homepage_ads`
--
ALTER TABLE `homepage_ads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `image_sliders`
--
ALTER TABLE `image_sliders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `news`
--
ALTER TABLE `news`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT for table `noticeboards`
--
ALTER TABLE `noticeboards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `tuition_fees`
--
ALTER TABLE `tuition_fees`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `editor_uploadables`
--
ALTER TABLE `editor_uploadables`
  ADD CONSTRAINT `editor_uploadables_editor_upload_id_foreign` FOREIGN KEY (`editor_upload_id`) REFERENCES `editor_uploads` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
