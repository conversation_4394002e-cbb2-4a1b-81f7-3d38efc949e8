import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User, type Role, type Permission, type PaginatedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { useEffect, useRef } from 'react';
import { toast } from 'sonner';

// Shadcn/UI Components
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Icons
import UsersIndex from './users/UsersIndex';
import PermissionsIndex from './permissions/PermissionsIndex';
import RolesIndex from './roles/RolesIndex';

// --- TYPE DEFINITIONS ---

interface Flash {
    success?: string;
    danger?: string;
}

interface AccessControlPageProps {
    users: PaginatedData<User>;
    roles: PaginatedData<Role>;
    permissions: PaginatedData<Permission>;
    filters: {
        search?: string;
    };
    permissionRole: any;
    activeTab: 'users' | 'roles' | 'permissions';
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Access Control',
        href: '/admin/access-control',
    },
];

export default function AccessControlIndex({ users, roles, permissions, filters, permissionRole, activeTab }: AccessControlPageProps) {
    const { flash } = usePage<{ flash: Flash }>().props;

    // Effect for showing toast notifications for success/error messages
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    const handleTabChange = (tab: 'users' | 'roles' | 'permissions') => {
        router.get(
            route('admin.access-control.index', { tab }),
            {}, 
            {
                only: ['activeTab', 'users', 'roles', 'permissions'],
                preserveState: true,
                replace: true,
            },
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Access Control Management" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Tabs value={activeTab} onValueChange={(newTab) => handleTabChange(newTab as any)} className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="users">Users</TabsTrigger>
                        <TabsTrigger value="roles">Roles</TabsTrigger>
                        <TabsTrigger value="permissions">Permissions</TabsTrigger>
                    </TabsList>

                    {/* Tab 1: Users */}
                    <TabsContent value="users" className="space-y-4">
                        <UsersIndex users={users} roles={roles} filters={filters} />
                    </TabsContent>

                    {/* Tab 2: Roles */}
                    <TabsContent value="roles" className="space-y-4">
                        <RolesIndex roles={roles}  filters={filters} />
                    </TabsContent>

                    {/* Tab 3: Permissions */}
                    <TabsContent value="permissions" className="space-y-4">
                        <PermissionsIndex permissions={permissions} filters={filters}  />
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
