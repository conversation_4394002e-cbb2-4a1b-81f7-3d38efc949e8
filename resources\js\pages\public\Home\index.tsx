import React, { useState } from 'react';
import AboutCard from "../about/AboutCard"
import Awrapper from "../about/Awrapper"
import ImageSlider from './image-slider/ImageSliderNew';
import Feature from "./feature/Feature"
import VideoPlayer from './feature/VideoPlayer';
import HighlightedMedia from './highlights/HighlightedMedia';
import OfferedCourses from '../courses/OfferedCourses';
import Newsletter from './newsletter/Newsletter';
import HomeNotice from './notice/HomeNotice';
import Navmenu from '../common/header/Navmenu';
import AdmissionModal from '@/components/ui/admission-modal';
import Footer from '../common/footer/Footer';
import LatestNews from './news/LatestNews';
import Gallery from './gallery/index';
import { News, Noticeboard, ImageSliderData, GalleryData } from '@/types';

interface AdmissionModalData {
  id: number;
  campaign_name: string | null;
  title: string | null;
  active_status: 'active' | 'inactive';
  published_date: string;
  image: string;
}

interface HomepageAdData {
  id: number;
  ad_title: string | null;
  program_info_title: string | null;
  session: string | null;
  offer_title: string | null;
  offer_text: string | null;
  active_status: 'active' | 'inactive';
}

const HomePage = ({ 
  blogs, 
  notices, 
  imageSliders, 
  galleryImages, 
  admissionModal, 
  homepageAd 
}: { 
  blogs: News[]; 
  notices: Noticeboard[]; 
  imageSliders: ImageSliderData[]; 
  galleryImages: GalleryData[];
  admissionModal: AdmissionModalData | null;
  homepageAd: HomepageAdData | null;
}) => {
  
  const [playState, setPlayState] = useState(false);


  const handleView = () => {
 
    // Redirect to admission info page
    window.location.href = '/admission-info';
  };

  const handleClose = () => {

  };


  return (
    <>
      <Navmenu />
      <ImageSlider imageSliders={imageSliders}/>
      
      <AdmissionModal 
        modalData={admissionModal} 
        onClose={handleClose} 
        onView={handleView} 
      />
      
      <AboutCard/>
      <HighlightedMedia />
      <HomeNotice notices={notices} adData={homepageAd}/>
      <Feature setPlayState={setPlayState}/>
      <OfferedCourses />
      <LatestNews blogs={blogs}/>
      <div className="space">&nbsp;</div>
      <Awrapper/>
      <Gallery galleryImages={galleryImages}/>
      <Newsletter/>
      <VideoPlayer playState={playState} setPlayState={setPlayState} videoId="pd3Ej7LE6z4"/>
      <Footer />
    </>
  )
}

export default HomePage
