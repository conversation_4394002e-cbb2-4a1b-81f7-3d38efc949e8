import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { FacultySchedule, type BreadcrumbItem } from '@/types';

type PageProps = {
    currentFacultySchedule: FacultySchedule;
};
import { Head, Link, router, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Edit Faculty Schedule',
        href: '/admin/schedule/faculty/edit',
    },
];

export default function FacultyScheduleEdit({ currentFacultySchedule }: PageProps) {
    const [faculty_name, setFacultyName] = useState<string>(currentFacultySchedule.faculty_name);
    const [department, setDepartment] = useState<string>(currentFacultySchedule.department);
    const [scheduleEmbedLink, setScheduleEmbedLink] = useState<string>(currentFacultySchedule.scheduleEmbedLink || '');

    const { errors } = usePage().props;

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.schedule.faculty.update', currentFacultySchedule.id), {
            _method: 'put',
            faculty_name,
            department,
            scheduleEmbedLink,
        });
    };



    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Faculty Schedule" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Faculty Schedule</div>

                        <Button>
                            <Link href="/admin/schedule" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="faculty_name">Faculty Name</Label>
                                        <Input
                                            type="text"
                                            id="faculty_name"
                                            placeholder="Faculty Name"
                                            value={faculty_name}
                                            onChange={(e) => setFacultyName(e.target.value)}
                                            aria-invalid={!!errors.faculty_name}
                                        />
                                        <InputError message={errors.faculty_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="department">Department</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department"
                                            value={department}
                                            onChange={(e) => setDepartment(e.target.value)}
                                            aria-invalid={!!errors.department}
                                        />
                                        <InputError message={errors.department} />
                                    </div>

                                    <div className="md:col-span-2">
                                        <Label htmlFor="scheduleEmbedLink">Schedule Embed Link</Label>
                                        <Input
                                            type="text"
                                            id="scheduleEmbedLink"
                                            placeholder="Schedule Embed Link"
                                            value={scheduleEmbedLink}
                                            onChange={(e) => setScheduleEmbedLink(e.target.value)}
                                            aria-invalid={!!errors.scheduleEmbedLink}
                                        />
                                        <InputError message={errors.scheduleEmbedLink} />
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Faculty Schedule</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
