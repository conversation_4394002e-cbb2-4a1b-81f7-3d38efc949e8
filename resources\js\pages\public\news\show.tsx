import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { FaCalendarAlt, FaFolder, FaUser } from 'react-icons/fa';
import '../../public/rtf.css';

// Assuming 'Back' component is in a relative path and styled with Tailwind
import Back from '../common/back/Back'; 
import { News } from '@/types';
import { ArrowLeft } from 'lucide-react';




// 2. MAIN COMPONENT
// ---
const NewsView = ({ newsDataItem }: { newsDataItem: News }) => {
  // Handle case where notice might not be found (though Inertia usually handles this with a 404)
 
  if (!newsDataItem) {
    return (
      <div className="flex items-center justify-center h-screen">
        <h2 className="text-2xl font-bold text-gray-700">Notice Not Found</h2>
      </div>
    );
  }

  // Format date for better readability
  const formattedDate = new Date(newsDataItem.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <>
      {/* Set the page title dynamically */}
      <Head title={newsDataItem.title} />
      
      <Back title="Post Details" />

      {/* Main container for the post */}
      <article className="max-w-4xl md:mx-auto md:my-10 p-2 mx-2 my-4 sm:p-4 bg-white rounded-xl shadow-lg">
        {/* Post Header */}
        <header className="mb-6 border-b border-gray-200 pb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 leading-tight">
            {newsDataItem.title}
          </h1>
          {/* Post Metadata */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-x-6 gap-y-2 mt-4 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <FaCalendarAlt className="text-gray-400" />
              <span>Published on: {formattedDate}</span>
            </div>
            <div className="flex items-center gap-2">
              <FaUser className="text-gray-400" />
              <span>Posted by: NIST</span>
            </div>
          </div>
        </header>

        {/* Post Body */}
        <div className="post-content">
          {/* Conditionally render the cover image */}
          {newsDataItem.image && (
            <img 
              src={newsDataItem.image} 
              alt={newsDataItem.title} 
              className="w-full h-auto object-cover rounded-lg mb-6 shadow-md" 
            />
          )}

          {/* Render HTML content safely using Tailwind's Typography plugin */}
          <div
            className="richtext-output"
            dangerouslySetInnerHTML={{ __html: newsDataItem.content }}
          />
        </div>

       
      </article>
      {/* End of main container */}
      <div className="my-6 mx-auto max-w-4xl px-6">
        <Link
          href="/news"
          className="inline-flex items-center gap-2 text-green-600 transition-colors hover:text-green-800"
        >
          <ArrowLeft size={20} />
          <span>Back to News</span>
        </Link>
      </div>
    </>
  );
};

export default NewsView;