<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use App\Models\Role;
use App\Models\Permission;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');

        $users = User::query()
            ->with(['roles', 'permissions'])
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('admin/access-control/users/UsersIndex', [
            'users' => UserResource::collection($users),
            'filters' => [
                'search' => $search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::get();
        return Inertia::render('admin/access-control/users/CreateUsers', [
            'roles' => $roles,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {


        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        if ($user) {
            $user?->syncRoles($request->roles);
            return redirect()->route('admin.access-control.index', ['tab' => 'users'])
                ->with('success', 'User created successfully.');
        }

        return redirect()->back()->with('error', 'Unable to create User. Please try again!');


    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, User $user)
    {
        if ($user) {
            $user->name = $request->name;
            $user->email = $request->email;
            $user->save();
            $user?->syncRoles($request->roles);

            return redirect()->route('admin.access-control.index', ['tab' => 'users'])
                ->with('success', 'User Updated successfully.');
        }
        return redirect()->back()->with('error', 'Unable to Update User. Please try again!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if ($user) {
            $user->delete();
            return redirect()->route('admin.access-control.index', ['tab' => 'users'])
                ->with('success', 'User deleted successfully.');
        }

        return redirect()->back()->with('error', 'Unable to delete User. Please try again!');
    }

  

    /**
     * Update user roles.
     */
    public function updateRoles(Request $request, User $user)
    {
        $validated = $request->validate([
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
        ]);

        $roles = Role::whereIn('id', $validated['roles'] ?? [])->get();
        $user->syncRoles($roles);

        return redirect()->route('admin.access-control.index', ['tab' => 'users'])
            ->with('success', 'User roles updated successfully.');
    }



    /**
     * Update user permissions.
     */
    public function updatePermissions(Request $request, User $user)
    {
        $validated = $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $permissions = Permission::whereIn('id', $validated['permissions'] ?? [])->get();
        $user->syncPermissions($permissions);

        return redirect()->route('admin.access-control.index', ['tab' => 'users'])
            ->with('success', 'User permissions updated successfully.');
    }
}
