import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';

import AppLayout from '@/layouts/app-layout';
import { AdmissionSpot, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admission Management',
        href: '/admin/admission',
    },
    {
        title: 'Edit Admission Spot',
        href: '/admin/admission/spots/edit',
    },
];

export default function AdmissionSpotEdit({ currentAdmissionSpot }: { currentAdmissionSpot: AdmissionSpot }) {
    const [campaignName, setCampaignName] = useState<string>(currentAdmissionSpot.campaign_name);
    const [session, setSession] = useState<string>(currentAdmissionSpot.session);
    const [programName, setProgramName] = useState<string>(currentAdmissionSpot.program_name);
    const [activeStatus, setActiveStatus] = useState<'unpublished' | 'published'>(currentAdmissionSpot.active_status);
    
    const [applicationStartDate, setApplicationStartDate] = useState<Date>(
        parseISO(currentAdmissionSpot.application_start_date)
    );
    const [applicationEndDate, setApplicationEndDate] = useState<Date>(
        parseISO(currentAdmissionSpot.application_end_date)
    );
    const [admissionTestDate, setAdmissionTestDate] = useState<Date | undefined>(
        currentAdmissionSpot.admission_test_date ? parseISO(currentAdmissionSpot.admission_test_date) : undefined
    );
    const [sessionStartDate, setSessionStartDate] = useState<Date | undefined>(
        currentAdmissionSpot.session_start_date ? parseISO(currentAdmissionSpot.session_start_date) : undefined
    );
    const [dateCreated, setDateCreated] = useState<Date>(
        parseISO(currentAdmissionSpot.date_created)
    );

    const { errors } = usePage().props;

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.admission.spots.update', currentAdmissionSpot.id), {
            _method: 'put',
            campaign_name: campaignName,
            session: session,
            program_name: programName,
            application_start_date: format(applicationStartDate, 'yyyy-MM-dd'),
            application_end_date: format(applicationEndDate, 'yyyy-MM-dd'),
            admission_test_date: admissionTestDate ? format(admissionTestDate, 'yyyy-MM-dd') : '',
            session_start_date: sessionStartDate ? format(sessionStartDate, 'yyyy-MM-dd') : '',
            active_status: activeStatus,
            date_created: format(dateCreated, 'yyyy-MM-dd'),
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Admission Spot" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Admission Spot</div>

                        <Button>
                            <Link href="/admin/admission?tab=admission_spot" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="campaign_name">Campaign Name *</Label>
                                        <Input
                                            type="text"
                                            id="campaign_name"
                                            placeholder="Enter campaign name"
                                            value={campaignName}
                                            onChange={(e) => setCampaignName(e.target.value)}
                                            aria-invalid={!!errors.campaign_name}
                                        />
                                        <InputError message={errors.campaign_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session">Session *</Label>
                                        <Input
                                            type="text"
                                            id="session"
                                            placeholder="Enter session"
                                            value={session}
                                            onChange={(e) => setSession(e.target.value)}
                                            aria-invalid={!!errors.session}
                                        />
                                        <InputError message={errors.session} />
                                    </div>

                                    <div>
                                        <Label htmlFor="program_name">Program Name *</Label>
                                        <Input
                                            type="text"
                                            id="program_name"
                                            placeholder="Enter program name"
                                            value={programName}
                                            onChange={(e) => setProgramName(e.target.value)}
                                            aria-invalid={!!errors.program_name}
                                        />
                                        <InputError message={errors.program_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="active_status">Status *</Label>
                                        <Select value={activeStatus} onValueChange={(value: 'unpublished' | 'published') => setActiveStatus(value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="unpublished">Unpublished</SelectItem>
                                                <SelectItem value="published">Published</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.active_status} />
                                    </div>

                                    <div>
                                        <Label htmlFor="application_start_date">Application Start Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !applicationStartDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {applicationStartDate ? format(applicationStartDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={applicationStartDate}
                                                    onSelect={(date) => {
                                                        if (date) setApplicationStartDate(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.application_start_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="application_end_date">Application End Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !applicationEndDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {applicationEndDate ? format(applicationEndDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={applicationEndDate}
                                                    onSelect={(date) => {
                                                        if (date) setApplicationEndDate(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.application_end_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="admission_test_date">Admission Test Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !admissionTestDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {admissionTestDate ? format(admissionTestDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={admissionTestDate}
                                                    onSelect={(date) => {
                                                        setAdmissionTestDate(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.admission_test_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session_start_date">Session Start Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !sessionStartDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {sessionStartDate ? format(sessionStartDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={sessionStartDate}
                                                    onSelect={(date) => {
                                                        setSessionStartDate(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.session_start_date} />
                                    </div>

                                    <div>
                                        <Label htmlFor="date_created">Date Created *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !dateCreated && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {dateCreated ? format(dateCreated, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={dateCreated}
                                                    onSelect={(date) => {
                                                        if (date) setDateCreated(date);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.date_created} />
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Admission Spot</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
