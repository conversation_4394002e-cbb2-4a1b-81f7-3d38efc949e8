<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Http\Resources\RoleResource;
use App\Http\Resources\PermissionResource;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Role;
use App\Models\Permission;

class AccessControlController extends Controller
{
    /**
     * Display the access control management page.
     */
    public function index(Request $request)
    {
        $activeTab = $request->get('tab', 'users');
        $search = $request->get('search');
        
        // Get users data //->with(['roles'])
        $users = User::query()
            ->with(['roles'])
            ->when($search && $activeTab === 'users', function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get roles data
        //
        $roles = Role::query()
            ->with('permissions')
            ->when($search && $activeTab === 'roles', function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get permissions data //->with('roles')
        $permissions = Permission::query()
            ->when($search && $activeTab === 'permissions', function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

            $permissionsData = PermissionResource::collection($permissions);
            $usersData = UserResource::collection($users);
            $rolesData = RoleResource::collection($roles);
            $permissionRole = Permission::get()->groupBy('module');

            

           

        return Inertia::render('admin/access-control/Index', [
            'users' => $usersData,
            'roles' => $rolesData,
            'permissions' => $permissionsData,
            'filters' => [
                'search' => $search,
            ],
            'permissionRole' => $permissionRole,
            
            'activeTab' => $activeTab,
        ]);
    }
}



