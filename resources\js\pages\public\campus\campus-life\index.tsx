import { Head, Link, router } from '@inertiajs/react';
import Back from '../../common/back/Back';
import Pagination from '@/components/pagination';
import { PaginatedData } from '@/types';

// Define the TypeScript interface for a gallery item
interface GalleryItem {
    id: number;
    title: string;
    description: string | null;
    category: string;
    image: string;
    created_at: string;
    updated_at: string;
}

interface CampusLifePageProps {
    galleries: PaginatedData<GalleryItem>;
    currentCategory: string;
}



const categoryOptions = [
    { value: 'all_activity', label: 'All Activity' },
    { value: 'events', label: 'Events' },
    { value: 'cse', label: 'CSE' },
    { value: 'ece', label: 'ECE' },
    { value: 'bba', label: 'BBA' },
    { value: 'bmb', label: 'BMB' },
    { value: 'excellent_results', label: 'Excellent Results' },
];

// Ensure pagination links keep the selected category as a query param
const withCategoryParam = (url: string, category: string) => {
    try {
        const u = new URL(url, window.location.origin);
        if (category) u.searchParams.set('category', category);
        return u.toString();
    } catch {
        // Fallback for relative URLs
        const hasQuery = url.includes('?');
        const sep = hasQuery ? '&' : '?';
        return `${url}${sep}category=${encodeURIComponent(category)}`;
    }
};

const GalleryCard = ({ item }: { item: GalleryItem }) => (
    <div className="group relative flex flex-col overflow-hidden squared-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl">
        {/* Image */}
        <div className="relative w-full">
            <img src={item.image} alt={item.title} className="h-auto w-full object-cover transition-transform duration-300 group-hover:scale-110" />

            {/* Date overlay */}
            <div className="absolute top-2 right-2 rounded bg-black/70 px-2 py-1 text-xs font-medium text-white">
                {new Date(item.created_at).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                })}
            </div>
        </div>

        {/* Caption */}
        <div className="flex flex-grow flex-col bg-white p-4">
            <Link href={`/campus-life/${item.id}`} className="block">
                <h3 className="line-clamp-2 text-lg font-semibold text-gray-800 transition-colors hover:text-green-600">{item.title}</h3>
                <p className="mt-1 text-sm text-gray-600">Read More ...</p>
            </Link>
        </div>
    </div>
);

const CampusLifePage = ({ galleries, currentCategory }: CampusLifePageProps) => {
    const handleCategoryChange = (category: string) => {
        // Reset to first page when switching categories
        router.get(
            '/campus-life',
            { category, page: 1 },
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    return (
        <>
            <Head title="Campus Life" />
            <Back title="Campus Life" />

            <section className="py-24">
                <div className="mx-auto w-full px-4 sm:w-[90%] lg:w-[85%]">
                    {/* Category Tabs */}
                    <div className="mb-8">
                        <div className="flex flex-wrap justify-center gap-2 border-b border-gray-200">
                            {categoryOptions.map((category) => (
                                <button
                                    key={category.value}
                                    onClick={() => handleCategoryChange(category.value)}
                                    className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
                                        currentCategory === category.value
                                            ? 'border-b-2 border-green-600 bg-green-600 text-white'
                                            : 'text-gray-600 hover:bg-gray-50 hover:text-green-600'
                                    }`}
                                >
                                    {category.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Gallery Grid */}
                    {/*  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6">
            {galleryArray.length > 0 ? (
              galleryArray.map((item) => (
                <GalleryCard key={item.id} item={item} />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
                  <p className="text-gray-500">
                    {currentCategory === 'all_activity' 
                      ? 'No images have been uploaded yet.' 
                      : `No images found in the ${categoryOptions.find(c => c.value === currentCategory)?.label} category.`
                    }
                  </p>
                </div>
              </div>
            )}
          </div>*}*/}

                    {/* Gallery Masonry */}
                    <div className="columns-1 gap-6 sm:columns-2 md:columns-3 lg:columns-3">
                        {galleries.data.length > 0 ? (
                            galleries.data.map((item) => (
                                <div key={item.id} className="mb-6 break-inside-avoid">
                                    <GalleryCard item={item} />
                                </div>
                            ))
                        ) : (
                            <div className="col-span-full py-12 text-center">
                                <div className="text-gray-500">
                                    <svg className="mx-auto mb-4 h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <h3 className="mb-2 text-lg font-medium text-gray-900">No images found</h3>
                                    <p className="text-gray-500">
                                        {currentCategory === 'all_activity'
                                            ? 'No images have been uploaded yet.'
                                            : `No images found in the ${categoryOptions.find((c) => c.value === currentCategory)?.label} category.`}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                   
                    <div className="mt-8 mb-8">
                        {(() => {
                            // Rewrite pagination URLs to include current category
                            const metaWithCategory = {
                                ...galleries.meta,
                                links: galleries.meta.links.map((link) => ({
                                    ...link,
                                    url: link.url ? withCategoryParam(link.url, currentCategory) : null,
                                })),
                            };
                            return <Pagination meta={metaWithCategory} />;
                        })()}
                    </div>
                </div>
            </section>
        </>
    );
};

export default CampusLifePage;
