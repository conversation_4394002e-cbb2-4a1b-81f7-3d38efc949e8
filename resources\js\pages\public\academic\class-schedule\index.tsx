import { Head } from '@inertiajs/react';
import React, { useMemo, useState } from 'react';
import { FaChevronDown, FaChevronRight, FaDownload } from 'react-icons/fa';
import Back from '../../common/back/Back';
// 1. DATA (Can be moved to a separate data file and imported)
// ---

// 2. TYPESCRIPT INTERFACES
// ---


interface DepartmentSchedule {
    id: number;
    dept: string;
    schedule: {
        batch: string; // Formatted accordion name
        mediaType: string;
        link: string;
        session?: string;
        isValidLink: boolean;
        dept: string;
    }[];
}

interface ClassScheduleProps {
    classSchedule: DepartmentSchedule[];
}

// 3. HELPER FUNCTION
// ---
// Transforms a Google Calendar embed URL into a printable URL

// Corrected helper function
/*const getPrintableUrl = (embedUrl: string): string => {
    const url = new URL(embedUrl);
    url.pathname = url.pathname.replace('/embed', '/printable');
    
    // Always set the mode to WEEK to override any defaults.
    url.searchParams.set('mode', 'WEEK'); 
    
    return url.toString();
};*/

const getPrintableUrl = (embedUrl: string): string => {
    const originalUrl = new URL(embedUrl);
    const calendarId = originalUrl.searchParams.get('src') || '';
    const timezone = originalUrl.searchParams.get('ctz') || 'Asia/Dhaka';

    const today = new Date();
    const endOfWeek = new Date();
    endOfWeek.setDate(today.getDate() + 7);

    const formatDate = (d: Date) => `${d.getFullYear()}${String(d.getMonth() + 1).padStart(2, '0')}${String(d.getDate()).padStart(2, '0')}`;

    const startDate = formatDate(today);
    const endDate = formatDate(endOfWeek);

    const printUrl = new URL('https://calendar.google.com/calendar/u/0/print_preview');
    printUrl.searchParams.set('dates', `${startDate}/${endDate}`);
    printUrl.searchParams.set('hl', 'en');
    printUrl.searchParams.set('ctz', timezone);
    printUrl.searchParams.set('pgsz', 'letter');
    printUrl.searchParams.set('wkst', '7');
    printUrl.searchParams.set('mode', 'WEEK');
    printUrl.searchParams.set('src', calendarId);

    return printUrl.toString();
};

// 4. MAIN COMPONENT
// ---
const ClassSchedule: React.FC<ClassScheduleProps> = ({ classSchedule }) => {
    const [selectedDept, setSelectedDept] = useState('ALL');
    const [openAccordion, setOpenAccordion] = useState<string | null>(null);

    const departments = useMemo(() => ['CSE', 'ECE', 'BBA', 'BMB'], []);


    // Flatten and sort the schedule data once using useMemo for performance
    const allSchedules = useMemo(() => {
        const flatList = classSchedule.flatMap((dept: DepartmentSchedule) =>
            dept.schedule.map((item) => ({ ...item, dept: dept.dept }))
        );

        // Sort based on the predefined department order
        return flatList.sort((a, b) => departments.indexOf(a.dept) - departments.indexOf(b.dept));
    }, [classSchedule, departments]);

    // Filter the schedules based on the selected department
    const filteredSchedules = useMemo(() => {
        if (selectedDept === 'ALL') {
            return allSchedules;
        }
        return allSchedules.filter((item) => item.dept === selectedDept);
    }, [selectedDept, allSchedules]);

    const toggleAccordion = (batch: string) => {
        setOpenAccordion(openAccordion === batch ? null : batch);
    };

    return (
        <>
            <Head title="Class Schedule" />
            <Back title="Class Schedule" />
            <div className="mx-auto w-full max-w-5xl p-4 mt-10 font-sans">
                {/* Filter Buttons */}
                <div className="mb-6 flex flex-wrap justify-center gap-2">
                    <button
                        onClick={() => setSelectedDept('ALL')}
                        className={`rounded px-5 py-2.5 text-sm font-semibold uppercase transition-colors duration-300 ${
                            selectedDept === 'ALL'
                                ? 'bg-green-800 text-white'
                                : 'border border-gray-300 bg-white text-gray-700 hover:bg-green-700 hover:text-white'
                        }`}
                    >
                        All Department
                    </button>
                    {departments.map((dept) => (
                        <button
                            key={dept}
                            onClick={() => setSelectedDept(dept)}
                            className={`rounded px-5 py-2.5 text-sm font-semibold uppercase transition-colors duration-300 ${
                                selectedDept === dept
                                    ? 'bg-green-800 text-white'
                                    : 'border border-gray-300 bg-white text-gray-700 hover:bg-green-700 hover:text-white'
                            }`}
                        >
                            {dept}
                        </button>
                    ))}
                </div>

                {/* Accordion List */}
                <div className="space-y-2 mb-12">
                    {filteredSchedules.map((item) => (
                        <div key={item.batch} className="overflow-hidden rounded-md bg-[#2c3e50]">
                            {/* Accordion Title */}
                            <div
                                className="flex cursor-pointer items-center justify-between p-4 select-none hover:bg-[#34495e]"
                                onClick={() => toggleAccordion(item.batch)}
                            >
                                <span className="text-lg font-normal text-white">{item.batch}</span>
                                <span className="text-white">{openAccordion === item.batch ? <FaChevronDown /> : <FaChevronRight />}</span>
                            </div>

                            {/* Accordion Content */}
                            <div
                                className={`grid transition-all duration-500 ease-in-out ${
                                    openAccordion === item.batch ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'
                                }`}
                            >
                                <div className="overflow-hidden">
                                    <div className="bg-[#ecf0f1] p-1">
                                        {item.isValidLink ? (
                                            <>
                                                <iframe
                                                    src={item.link}
                                                    className="h-[440px] w-full border-0"
                                                    title={`${item.batch} Schedule`}
                                                ></iframe>
                                                {/* Download Button */}
                                                <div className="mt-3 justify-end lg:hidden">
                                                    <a
                                                        href={getPrintableUrl(item.link)}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="inline-flex items-center gap-1 rounded-md bg-green-800 px-2 py-2 text-sm text-white transition-colors hover:bg-green-600"
                                                    >
                                                        <FaDownload className="h-3 w-3" />
                                                        Print/Download
                                                    </a>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="h-[440px] w-full bg-gray-100 flex items-center justify-center border-0">
                                                <div className="text-center text-gray-600">
                                                    <div className="text-xl font-semibold mb-3">Schedule Not Available</div>
                                                    <div className="text-sm max-w-md">
                                                        The class schedule link is not configured or invalid.
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default ClassSchedule;
