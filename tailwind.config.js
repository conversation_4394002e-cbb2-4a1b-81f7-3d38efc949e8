// tailwind.config.js

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
    './storage/framework/views/*.php',
    './resources/views/**/*.blade.php',
    './resources/js/**/*.tsx',
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        // Converted from --mt-* variables
        mt: {
          overlay: {
            DEFAULT: 'rgba(251, 251, 251, 0.75)',
            dark: 'rgba(31, 32, 35, 0.75)',
          },
          'transparent-foreground': {
            DEFAULT: 'rgba(0, 0, 0, 0.4)',
            dark: 'rgba(255, 255, 255, 0.4)',
          },
          'bg-secondary': {
            DEFAULT: 'rgba(251, 251, 251, 0.8)',
            dark: 'rgba(31, 32, 35, 0.8)',
          },
          'code-background': {
            DEFAULT: '#082b781f',
            dark: '#ffffff13',
          },
          'code-color': {
            DEFAULT: '#d4d4d4',
            dark: '#2c2e33',
          },
          secondary: {
            DEFAULT: '#9d9d9f',
            dark: '#595a5c',
          },
          'pre-background': {
            DEFAULT: '#ececec',
            dark: '#080808',
          },
          'pre-border': {
            DEFAULT: '#e0e0e0',
            dark: '#23252a',
          },
          'pre-color': {
            DEFAULT: '#2f2f31',
            dark: '#e3e4e6',
          },
          hr: {
            DEFAULT: '#dcdcdc',
            dark: '#26282d',
          },
          'drag-handle-hover': {
            DEFAULT: '#5c5c5e',
            dark: '#969799',
          },
          accent: {
            'bold-blue': { DEFAULT: '#05c', dark: '#85b8ff' },
            'bold-teal': { DEFAULT: '#206a83', dark: '#9dd9ee' },
            'bold-green': { DEFAULT: '#216e4e', dark: '#7ee2b8' },
            'bold-orange': { DEFAULT: '#a54800', dark: '#fec195' },
            'bold-red': { DEFAULT: '#ae2e24', dark: '#fd9891' },
            'bold-purple': { DEFAULT: '#5e4db2', dark: '#b8acf6' },
            gray: { DEFAULT: '#758195', dark: '#738496' },
            blue: { DEFAULT: '#1d7afc', dark: '#388bff' },
            teal: { DEFAULT: '#2898bd', dark: '#42b2d7' },
            green: { DEFAULT: '#22a06b', dark: '#2abb7f' },
            orange: { DEFAULT: '#fea362', dark: '#a54800' },
            red: { DEFAULT: '#c9372c', dark: '#e2483d' },
            purple: { DEFAULT: '#8270db', dark: '#8f7ee7' },
            'blue-subtler': { DEFAULT: '#cce0ff', dark: '#09326c' },
            'teal-subtler': { DEFAULT: '#c6edfb', dark: '#164555' },
            'green-subtler': { DEFAULT: '#baf3db', dark: '#164b35' },
            'yellow-subtler': { DEFAULT: '#f8e6a0', dark: '#533f04' },
            'red-subtler': { DEFAULT: '#ffd5d2', dark: '#5d1f1a' },
            'purple-subtler': { DEFAULT: '#dfd8fd', dark: '#352c63' },
          },
        },
        // Converted from --hljs-* variables for syntax highlighting
        hljs: {
          string: { DEFAULT: '#aa430f', dark: '#da936b' },
          title: { DEFAULT: '#b08836', dark: '#f1d59d' },
          comment: { DEFAULT: '#999999', dark: '#aaaaaa' },
          keyword: { DEFAULT: '#0c5eb1', dark: '#6699cc' },
          attr: { DEFAULT: '#3a92bc', dark: '#90cae8' },
          literal: { DEFAULT: '#c82b0f', dark: '#f2777a' },
          name: { DEFAULT: '#259792', dark: '#5fc0a0' },
          'selector-tag': { DEFAULT: '#c8500f', dark: '#e8c785' },
          number: { DEFAULT: '#3da067', dark: '#b6e7b6' },
        },
      },
    },
  },
  plugins: [],
};