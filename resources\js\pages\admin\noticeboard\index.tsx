import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useEffect, useRef } from 'react';
import { toast } from 'sonner';
import Pagination from '@/components/pagination';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Edit, Search, Trash2, Eye } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import debounce from 'lodash/debounce';
import { useState } from 'react';
import { useConfirmation } from '@/contexts/confirmation-context';
import '@/components/minimal-tiptap/styles/index.css';
import '../text-editor/RichTextStyles.css';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Noticeboard',
        href: route('admin.noticeboard.index'),
    },
];

interface Noticeboard {
    id: number;
    title: string;
    category: string;
    content: string;
    image: string | null;
    attachment: string | null;
    published_at: string | null;
    active_status: string;
}

interface PaginatedNoticeboardData {
    data: Noticeboard[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
}

interface Flash {
    success?: string;
    danger?: string;
}

export default function NoticeboardIndex({ noticeboardData }: { noticeboardData: PaginatedNoticeboardData }) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const { showConfirmation } = useConfirmation();
    const [viewingNotice, setViewingNotice] = useState<Noticeboard | null>(null);

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    // Search functionality
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.noticeboard.index'), { search: query }, { preserveState: true, replace: true });
        }, 500),
    ).current;

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        handleSearch(e.target.value);
    };

    // View handler
    const handleViewNotice = (notice: Noticeboard) => {
        setViewingNotice(notice);
    };

    // Delete handler with confirmation
    const handleDeleteNotice = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Notice',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.noticeboard.destroy', id));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Noticeboard" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="relative w-full sm:w-1/3">
                            <Input
                                id={'search'}
                                className="peer ps-9"
                                placeholder="Search by title or category..."
                                type="search"
                                onChange={onSearchChange}
                            />
                            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
                                <Search size={16} aria-hidden="true" />
                            </div>
                        </div>

                        <Button>
                            <Link href={route('admin.noticeboard.create')} prefetch>
                                Add Notice
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>ID</TableHead>
                                        <TableHead>Title</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Published Date</TableHead>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {noticeboardData.data.map((notice) => (
                                        <TableRow key={notice.id}>
                                            <TableCell className="font-medium">{notice.id}</TableCell>
                                            <TableCell>{notice.title}</TableCell>
                                            <TableCell>{notice.category}</TableCell>
                                            <TableCell>
                                                {notice.published_at ? (
                                                    <span className="text-sm">{notice.published_at}</span>
                                                ) : (
                                                    <span className="text-gray-500 text-sm">Not published</span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {notice.image ? (
                                                    <img src={notice.image} alt={notice.title} className="h-16 w-16 rounded object-cover" />
                                                ) : (
                                                    <span className="text-gray-500">No Image</span>
                                                )}
                                            </TableCell>
                                            <TableCell className="space-x-1">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" onClick={() => handleViewNotice(notice)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>View</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button asChild size={'sm'} variant="outline">
                                                            <Link href={route('admin.noticeboard.edit', notice.id)} prefetch>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Edit</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size={'sm'} variant={'destructive'} onClick={() => handleDeleteNotice(notice.id, notice.title)}>
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Delete</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>

                    <div className="mt-4">
                        <Pagination meta={noticeboardData.meta} />
                    </div>

                    {/* View Notice Dialog */}
                    <Dialog open={!!viewingNotice} onOpenChange={() => setViewingNotice(null)}>
                        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>View Notice</DialogTitle>
                            </DialogHeader>
                            {viewingNotice && (
                                <div className="space-y-6">
                                    {/* Basic Info */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>ID</Label>
                                            <p className="text-sm font-medium">{viewingNotice.id}</p>
                                        </div>
                                        <div>
                                            <Label>Category</Label>
                                            <p className="text-sm">{viewingNotice.category}</p>
                                        </div>
                                    </div>

                                    <div>
                                        <Label>Title</Label>
                                        <p className="text-lg font-semibold">{viewingNotice.title}</p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>Published Date</Label>
                                            <p className="text-sm">{viewingNotice.published_at || 'Not published'}</p>
                                        </div>
                                        <div>
                                            <Label>Status</Label>
                                            <p className="text-sm">
                                                <span className={`px-2 py-1 rounded text-xs ${
                                                    viewingNotice.active_status === 'active'
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {viewingNotice.active_status}
                                                </span>
                                            </p>
                                        </div>
                                    </div>

                                    {/* Content */}
                                    <div>
                                        <Label>Content</Label>
                                        <div
                                            className="richtext-output"
                                            dangerouslySetInnerHTML={{
                                                __html: viewingNotice.content
                                            }}
                                        />
                                    </div>

                                    {/* Image */}
                                    {viewingNotice.image && (
                                        <div>
                                            <Label>Image</Label>
                                            <div className="mt-2">
                                                <img
                                                    src={viewingNotice.image}
                                                    alt={viewingNotice.title}
                                                    className="max-w-md rounded border"
                                                />
                                            </div>
                                        </div>
                                    )}

                                    {/* Attachment */}
                                    {viewingNotice.attachment && (
                                        <div>
                                            <Label>Attachment</Label>
                                            <div className="mt-2">
                                                <a
                                                    href={viewingNotice.attachment}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100"
                                                >
                                                    View Attachment
                                                </a>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>
                </div>
            </div>
        </AppLayout>
    );
}

