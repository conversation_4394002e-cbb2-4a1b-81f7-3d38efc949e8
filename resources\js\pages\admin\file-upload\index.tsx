import React, { useState, useEffect } from 'react';
import { Head, router, usePage } from '@inertiajs/react';
import { toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import GalleryTab from './tabs/gallery-tab';
import FileUploadTab from './tabs/file-upload-tab';
import ImageSliderTab from './tabs/image-slider-tab';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'File Upload',
        href: route('admin.file-upload.index'),
    },
];

interface Flash {
    success?: string;
    danger?: string;
}

interface GalleryData {
    id: number;
    title: string;
    description: string | null;
    category: string;
    image: string;
    relative_url: string;
    absolute_url: string;
    created_at: string;
    updated_at: string;
}

interface FileUploadData {
    id: number;
    title: string;
    description: string | null;
    category: string;
    file: string;
    relative_url: string;
    absolute_url: string;
    file_name: string;
    file_extension: string;
    created_at: string;
    updated_at: string;
}

interface PaginatedGalleries {
    data: GalleryData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface PaginatedFileUploads {
    data: FileUploadData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface ImageSliderData {
    id: number;
    title: string;
    image: string;
    heading: string;
    caption: string;
    order: number;
    created_at: string;
    updated_at: string;
}

interface PaginatedImageSliders {
    data: ImageSliderData[];
    links: { url: string | null; label: string; active: boolean; }[];
    meta: { links: { url: string | null; label: string; active: boolean; }[]; total: number; from: number; to: number; };
}

interface FileUploadIndexProps {
    galleries: PaginatedGalleries;
    fileUploads: PaginatedFileUploads;
    imageSliders: PaginatedImageSliders;
    activeTab: string;
}

export default function FileUploadIndex({
    galleries,
    fileUploads,
    imageSliders,
    activeTab,
}: FileUploadIndexProps) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const [currentTab, setCurrentTab] = useState(activeTab);

    

    const handleTabChange = (value: string) => {
        setCurrentTab(value);
        router.get(route('admin.file-upload.index'), { tab: value }, { 
            preserveState: true, 
            replace: true 
        });
    };

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="File Upload Management" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5">
                        <h1 className="text-2xl font-bold">File Upload Management</h1>
                        <p className="text-gray-600">Manage gallery images and file uploads</p>
                    </div>

                    <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="gallery">Gallery</TabsTrigger>
                            <TabsTrigger value="image-slider">Image Slider</TabsTrigger>
                            <TabsTrigger value="files">File Upload</TabsTrigger>
                        </TabsList>

                        <TabsContent value="gallery" className="mt-6">
                            <GalleryTab data={galleries} />
                        </TabsContent>

                        <TabsContent value="image-slider" className="mt-6">
                            <ImageSliderTab data={imageSliders} />
                        </TabsContent>

                        <TabsContent value="files" className="mt-6">
                            <FileUploadTab data={fileUploads} />
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
