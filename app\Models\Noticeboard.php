<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\EditorUpload;

class Noticeboard extends Model
{
    //

    protected $fillable = [
        
        'title',
        'slug',
        'content',
        'image',
        'attachment',
        'category',
        'active_status',
        'published_at',
    ];

    protected $casts = [
        'published_at' => 'date',
    ];

     public function editorUploads()
    {
        return $this->morphToMany(
            EditorUpload::class,
            'uploadable',
            'editor_uploadables',      // pivot
            'uploadable_id',           // this model key on pivot
            'editor_upload_id'         // related key on pivot
        )->withTimestamps();
    }
}


