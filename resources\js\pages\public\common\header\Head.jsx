import React from 'react'
import { Link } from '@inertiajs/react' // Replaced react-router-dom with Inertia

const Head = () => {
  return (
    <>
      <section className='head'>
        <div className="container flexSB">
          <div className="top-contact">
            <ul>
              <li><i className="fa-solid fa-phone" style={{ color: "#FFD43B", }} ></i><a href="tel:01713493163" > 01713493163</a></li>
              <li>|</li>
              <li><i className="fa-solid fa-phone" style={{ color: "#FFD43B", }} ></i><a href="tel:01811458853" > 01811458853</a></li>
              <li>|</li>
              <li><i className="fa-solid fa-envelope" style={{ color: "#FFD43B", }} ></i><a href="mailto:<EMAIL>" > <EMAIL></a></li>
              <li>|</li>
              <li><i className="fa-solid fa-user" style={{ color: "#FFD43B", }} ></i> Student Login</li>
            </ul>
          </div>
          <div className="top-right-action">
            <ul className='top-left-nav'>
              {/* Converted to Inertia Link and applied button class directly */}
              <li>
                <Link href="/application-form" className="top-apply-button">
                  Apply Online
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </section>
    </>
  )
}

export default Head