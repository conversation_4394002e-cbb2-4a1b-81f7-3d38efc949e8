<?php

namespace App\Http\Controllers;

use App\Models\HomepageAd;
use Illuminate\Http\Request;

class HomepageAdController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'ad_title' => 'nullable|string|max:255',
            'program_info_title' => 'nullable|string|max:255',
            'session' => 'nullable|string|max:255',
            'offer_title' => 'nullable|string|max:255',
            'offer_text' => 'nullable|string',
        ]);

        $data['active_status'] = 'inactive';

        HomepageAd::create($data);

        return redirect(route('admin.admission.index', ['tab' => 'homepage_ad']))
            ->with('success', 'Homepage ad created successfully');
    }

    public function update(Request $request, HomepageAd $homepageAd)
    {
        // Check if this is a status-only update (from StatusToggle component)
        if ($request->has('active_status') && count($request->all()) === 1) {
            $data = $request->validate([
                'active_status' => 'required|in:active,inactive',
            ]);
        } else {
            // Full update validation
            $data = $request->validate([
                'ad_title' => 'nullable|string|max:255',
                'program_info_title' => 'nullable|string|max:255',
                'session' => 'nullable|string|max:255',
                'offer_title' => 'nullable|string|max:255',
                'offer_text' => 'nullable|string',
                'active_status' => 'required|in:active,inactive',
            ]);
        }

        $homepageAd->update($data);

        return redirect(route('admin.admission.index', ['tab' => 'homepage_ad']))
            ->with('success', 'Homepage ad updated successfully');
    }

    public function destroy(HomepageAd $homepageAd)
    {
        $homepageAd->delete();

        return redirect(route('admin.admission.index', ['tab' => 'homepage_ad']))
            ->with('success', 'Homepage ad deleted successfully');
    }
}
