<?php

namespace App\Http\Controllers;

use App\Models\AdmissionModal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdmissionModalController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'campaign_name' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'published_date' => 'required|date',
            'image' => 'required|image|max:2048',
        ]);

        $data['active_status'] = 'inactive';

        if ($request->hasFile('image')) {
            $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/admission-modals');
        }

        AdmissionModal::create($data);

        return redirect(route('admin.admission.index', ['tab' => 'admission_modal']))
            ->with('success', 'Admission modal created successfully');
    }

    public function update(Request $request, AdmissionModal $admissionModal)
    {
        // Check if this is a status-only update (from StatusToggle component)
        if ($request->has('active_status') && count($request->all()) === 1) {
            $data = $request->validate([
                'active_status' => 'required|in:active,inactive',
            ]);

            $admissionModal->update($data);
        } else {
            // Full update validation
            $data = $request->validate([
                'campaign_name' => 'nullable|string|max:255',
                'title' => 'nullable|string|max:255',
                'active_status' => 'required|in:active,inactive',
                'published_date' => 'required|date',
                'image' => 'nullable|image|max:2048',
            ]);

            $data['image'] = $admissionModal->image;

            if ($request->hasFile('image')) {
                if ($admissionModal->image) {
                    Storage::disk('public')->delete($admissionModal->image);
                }
                $data['image'] = storeSanitizedFile($request->file('image'), 'uploads/admission-modals');
            }

            $admissionModal->update($data);
        }

        return redirect(route('admin.admission.index', ['tab' => 'admission_modal']))
            ->with('success', 'Admission modal updated successfully');
    }

    public function destroy(AdmissionModal $admissionModal)
    {
        if ($admissionModal->image) {
            Storage::disk('public')->delete($admissionModal->image);
        }

        $admissionModal->delete();

        return redirect(route('admin.admission.index', ['tab' => 'admission_modal']))
            ->with('success', 'Admission modal deleted successfully');
    }
}
