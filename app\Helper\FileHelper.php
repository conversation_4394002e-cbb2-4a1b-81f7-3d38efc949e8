<?php

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Stores an uploaded file with a unique, sanitized name.
 *
 * @param UploadedFile $file The file object from the request.
 * @param string $path The directory to store the file in.
 * @return string The path to the stored file.
 */
function storeSanitizedFile(UploadedFile $file, string $path): string
{
    // 1. Get the filename without the extension
    $filenameWithoutExt = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

    // 2. Sanitize the original filename
    $sanitizedFilename = Str::slug($filenameWithoutExt, '-');

    // 3. Get the file extension
    $extension = $file->getClientOriginalExtension();

    // 4. Construct the initial full filename
    $currentFilename = $sanitizedFilename . '.' . $extension;

    // 5. Check for duplicates and append a timestamp if needed
    if (Storage::disk('public')->exists($path . '/' . $currentFilename)) {
        $currentFilename = $sanitizedFilename . '-' . time() . '.' . $extension;
    }

    // 6. Store the file and return its path
    return $file->storeAs($path, $currentFilename, 'public');
}