<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClassScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "department" => $this->department,
            "session" => $this->session,
            "batch" => $this->batch,
            "scheduleEmbedLink" => $this->scheduleEmbedLink,
        ];
    }
}
