import InputError from '@/components/input-error';
import Pagination from '@/components/pagination';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';
import { router, useForm } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import React, { useRef, useState } from 'react';

interface GalleryData {
    id: number;
    title: string;
    description: string | null;
    category: string;
    image: string;
    relative_url: string;
    absolute_url: string;
    created_at: string;
    updated_at: string;
}

interface PaginatedData {
    data: GalleryData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface GalleryTabProps {
    data: PaginatedData;
}

const categoryOptions = [
    { value: 'all_activity', label: 'All Activity' },
    { value: 'events', label: 'Events' },
    { value: 'cse', label: 'CSE' },
    { value: 'ece', label: 'ECE' },
    { value: 'bba', label: 'BBA' },
    { value: 'bmb', label: 'BMB' },
    { value: 'excellent_results', label: 'Excellent Results' },
];

export default function GalleryTab({ data }: GalleryTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<GalleryData | null>(null);
    const [viewingItem, setViewingItem] = useState<GalleryData | null>(null);

    const {
        data: formData,
        setData,
        post,
        processing,
        errors,
        reset,
    } = useForm({
        title: '',
        description: '',
        category: '',
        image: null as File | null,
    });

    // Search functionality
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(
                route('admin.file-upload.index'),
                {
                    tab: 'gallery',
                    gallery_search: query,
                },
                { preserveState: true, replace: true },
            );
        }, 500),
    ).current;

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        handleSearch(e.target.value);
    };

    const handleCreate = async (e: React.FormEvent) => {
        e.preventDefault();

        const confirmed = await showConfirmation({
            title: 'Create Gallery Item',
            description: 'Are you sure you want to create this gallery item?',
            confirmText: 'Create',
            cancelText: 'Cancel',
            variant: 'default',
        });

        if (confirmed) {
            post(route('admin.galleries.store'), {
                onSuccess: () => {
                    setIsCreateOpen(false);
                    reset();
                },
            });
        }
    };

    const handleEdit = (item: GalleryData) => {
        setEditingItem(item);
        setData({
            title: item.title,
            description: item.description || '',
            category: item.category,
            image: null,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(
                route('admin.galleries.update', editingItem.id),
                {
                    _method: 'put',
                    ...formData,
                },
                {
                    onSuccess: () => {
                        setIsEditOpen(false);
                        setEditingItem(null);
                        reset();
                    },
                },
            );
        }
    };

    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Gallery Item',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.galleries.destroy', id));
        }
    };

    const handleView = (item: GalleryData) => {
        setViewingItem(item);
        setIsViewOpen(true);
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        // You could add a toast notification here
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="relative w-full sm:w-1/3">
                    <Input
                        id={'search'}
                        className="peer ps-9"
                        placeholder="Search gallery by title or category..."
                        type="search"
                        onChange={onSearchChange}
                    />
                    <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
                        <Search size={16} aria-hidden="true" />
                    </div>
                </div>

                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Gallery Item
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Create Gallery Item</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="title">Title *</Label>
                                    <Input
                                        id="title"
                                        value={formData.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="Enter title"
                                    />
                                    <InputError message={errors.title} />
                                </div>
                                <div>
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={formData.category} onValueChange={(value) => setData('category', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categoryOptions.map((option) => (
                                                <SelectItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.category} />
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Enter description"
                                    rows={3}
                                />
                                <InputError message={errors.description} />
                            </div>
                            <div>
                                <Label htmlFor="image">Image *</Label>
                                <Input
                                    id="image"
                                    type="file"
                                    accept="image/jpeg,image/jpg,image/png,image/webp"
                                    onChange={(e) => setData('image', e.target.files?.[0] || null)}
                                />
                                <InputError message={errors.image} />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleCreate} type="submit" disabled={processing}>
                                    Create
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Image</TableHead>
                                <TableHead>Title</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Created</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        <img src={item.image} alt={item.title} className="h-16 w-16 rounded object-cover" />
                                    </TableCell>
                                    <TableCell className="font-medium">{item.title}</TableCell>
                                    <TableCell>
                                        <span className="capitalize">{item.category.replace('_', ' ')}</span>
                                    </TableCell>
                                    <TableCell>{new Date(item.created_at).toLocaleDateString()}</TableCell>
                                    <TableCell className="space-x-1">
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>View URLs</p>
                                            </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Edit</p>
                                            </TooltipContent>
                                        </Tooltip>

                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.title)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Delete</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit Gallery Item</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="edit_title">Title *</Label>
                                <Input id="edit_title" value={formData.title} onChange={(e) => setData('title', e.target.value)} />
                            </div>
                            <div>
                                <Label htmlFor="edit_category">Category *</Label>
                                <Select value={formData.category} onValueChange={(value) => setData('category', value)}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categoryOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="edit_description">Description</Label>
                            <Textarea
                                id="edit_description"
                                value={formData.description}
                                onChange={(e) => setData('description', e.target.value)}
                                rows={3}
                            />
                        </div>
                        <div>
                            <Label htmlFor="edit_image">Image</Label>
                            <Input
                                id="edit_image"
                                type="file"
                                accept="image/jpeg,image/jpg,image/png,image/webp"
                                onChange={(e) => setData('image', e.target.files?.[0] || null)}
                            />
                            {editingItem && (
                                <div className="mt-2">
                                    <p className="mb-2 text-sm text-gray-600">Current Image:</p>
                                    <img src={editingItem.image} alt="Current" className="h-20 w-20 rounded object-cover" />
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View URLs Dialog */}
            <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>View URLs</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div>
                                <Label>Title</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.title} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.title)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>Relative URL</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.relative_url} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.relative_url)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>Absolute URL</Label>
                                <div className="flex items-center space-x-2">
                                    <Input value={viewingItem.absolute_url} readOnly />
                                    <Button size="sm" onClick={() => copyToClipboard(viewingItem.absolute_url)}>
                                        Copy
                                    </Button>
                                </div>
                            </div>
                            <div>
                                <Label>Image Preview</Label>
                                <img src={viewingItem.image} alt={viewingItem.title} className="h-32 w-32 rounded object-cover" />
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
