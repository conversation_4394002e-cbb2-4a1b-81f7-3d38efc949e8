/* EditorStyles.css */

/* The main container for the editor */
.editor-container {
  width: 100%;
  max-width: 900px; /* Prevents it from being too wide on large screens */
  margin: 20px auto; /* Centers the editor on the page */
}

/* The Quill editor element itself */
.quill-editor .ql-toolbar {
  /* Allows the toolbar buttons to wrap onto the next line on small screens */
  flex-wrap: wrap; 
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-color: #ccc;
}

.quill-editor .ql-container {
  min-height: 300px;
  font-size: 16px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-color: #ccc;
}

/* --- Responsive Styles for Mobile --- */
@media (max-width: 768px) {
  .editor-container {
    /* Use more of the screen on mobile */
    max-width: 100%; 
    margin: 10px;
  }
  
  .quill-editor .ql-container {
    /* Reduce the minimum height on smaller devices to save space */
    min-height: 250px; 
    font-size: 14px;
  }
}