<?php


use App\Http\Resources\NewsResource;
use App\Http\Resources\NoticeboardResource;
use App\Http\Resources\ImageSliderResource;
use App\Http\Resources\GalleryResource;
use App\Http\Resources\RoleResource;
use App\Http\Resources\PermissionResource;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
//news routes
use App\Http\Controllers\News\NewsIndexController;
use App\Http\Controllers\News\NewsStoreController;
use App\Http\Controllers\News\NewsCreateController;
use App\Http\Controllers\News\NewsUpdateController;
use App\Http\Controllers\News\NewsEditController;
use App\Http\Controllers\News\NewsDestroyController;
//noticeboard routes
use App\Http\Controllers\Noticeboard\NoticeboardIndexController;
use App\Http\Controllers\Noticeboard\NoticeboardStoreController;
use App\Http\Controllers\Noticeboard\NoticeboardCreateController;
use App\Http\Controllers\Noticeboard\NoticeboardEditController;
use App\Http\Controllers\Noticeboard\NoticeboardUpdateController;
use App\Http\Controllers\Noticeboard\NoticeboardDestroyController;
//governing body routes
use App\Http\Controllers\GoverningBodyIndexController;
use App\Http\Controllers\GoverningBodyStoreController;
use App\Http\Controllers\GoverningBodyEditController;
use App\Http\Controllers\GoverningBodyUpdateController;
use App\Http\Controllers\GoverningBodyDestroyController;

//faculty routes
use App\Http\Controllers\FacultyInfoIndexController;
use App\Http\Controllers\FacultyInfoStoreController;
use App\Http\Controllers\FacultyInfoCreateController;
use App\Http\Controllers\FacultyInfoEditController;
use App\Http\Controllers\FacultyInfoUpdateController;
use App\Http\Controllers\FacultyInfoDestroyController;

//admission routes
use App\Http\Controllers\AdmissionController;
use App\Http\Controllers\AdmissionSpotController;
use App\Http\Controllers\AdmissionSpotCreateController;
use App\Http\Controllers\AdmissionSpotEditController;
use App\Http\Controllers\TuitionFeeController;
use App\Http\Controllers\AdmissionPostController;
use App\Http\Controllers\AdmissionPostCreateController;
use App\Http\Controllers\AdmissionPostEditController;
use App\Http\Controllers\AdmissionPromoImageController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\HomepageAdController;
use App\Http\Controllers\AdmissionModalController;

//user routes
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;

//import
use App\Models\News;
use App\Models\Noticeboard;
use App\Models\ImageSlider;
use App\Models\Gallery;

use App\Http\Controllers\Frontend\About\AboutUsController;
use App\Http\Controllers\Frontend\About\AtAGlanceController;
use App\Http\Controllers\Frontend\About\PrincipalMessageController;
use App\Http\Controllers\Frontend\About\GoverningBodyController;
use App\Http\Controllers\Frontend\Admission\AdmissionInfoController;
use App\Http\Controllers\Frontend\Admission\TuitionFeesController;
use App\Http\Controllers\Frontend\Admission\AdmissionApplyController;
use App\Http\Controllers\Frontend\Courses\CourseController;
use App\Http\Controllers\Frontend\Academic\ClassScheduleController;
use App\Http\Controllers\Frontend\Academic\FacultyScheduleController;
use App\Http\Controllers\Frontend\Academic\FacultyMemberController;
use App\Http\Controllers\Frontend\Noticeboard\NoticeController;
use App\Http\Controllers\Frontend\Campus\NewsController;
use App\Http\Controllers\Schedule\ScheduleController;




//editor upload routes
use App\Http\Controllers\EditorUploadController;

Route::get('/', [App\Http\Controllers\Frontend\HomepageController::class, 'index'])->name('home');

Route::get(
    '/about-us',
    [AboutUsController::class, 'index']
)->name('about-us.index');

Route::get(
    '/at-a-glance',
    [AtAGlanceController::class, 'index']
)->name('at-a-glance.index');

Route::get(
    '/principal-message',
    [PrincipalMessageController::class, 'index']
)->name('principal-message.index');

Route::get(
    '/governing-body',
    [GoverningBodyController::class, 'index']
)->name('governing-body.index');

Route::get(
    '/admission-info',
    [AdmissionInfoController::class, 'index']
)->name('admission-info.index');

Route::get(
    '/tuition-fees',
    [TuitionFeesController::class, 'index']
)->name('tuition-fees.index');

Route::get(
    '/application-form',
    [AdmissionApplyController::class, 'index']
)->name('admission-apply.index');

Route::get('/apply/success/{uid}/{department}', function ($uid, $department) {
    return Inertia::render('public/admission/apply/success', [
        'uid' => $uid,
        'department' => $department,
    ]);
})->name('application.success');

Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/courses-cse', [CourseController::class, 'cse'])->name('courses-cse.index');
Route::get('/courses-ece', [CourseController::class, 'ece'])->name('courses-ece.index');
Route::get('/courses-bba', [CourseController::class, 'bba'])->name('courses-bba.index');    
Route::get('/courses-bmb', [CourseController::class, 'bmb'])->name('courses-bmb.index');

Route::get(
    '/class-schedule',
    [ClassScheduleController::class, 'index']
)->name('class-schedule.index');

Route::get('/faculty-schedule', [FacultyScheduleController::class, 'index'])->name('faculty-schedule.index');
Route::get('/faculty-member', [FacultyMemberController::class, 'index'])->name('faculty-member.index');
Route::get('/faculty-member/{id}/{slug}', [FacultyMemberController::class, 'show'])->name('faculty-member.show');

//noticeboard
Route::get('/noticeboard', [NoticeController::class, 'index'])->name('noticeboardpage.index');
Route::get('/noticeboard/{id}/{slug}', [NoticeController::class, 'show'])->name('noticeboardpage.show');

Route::get('/news', [NewsController::class, 'index'])->name('newspage.index');
Route::get('/news/{id}/{slug}', [NewsController::class, 'show'])->name('newspage.show');

// Campus Life Routes
Route::get('/campus-life', [App\Http\Controllers\Frontend\Campus\GalleryController::class, 'index'])->name('campus-life.index');
Route::get('/campus-life/{id}', [App\Http\Controllers\Frontend\Campus\GalleryController::class, 'show'])->name('campus-life.show');


Route::get('/contact', function () {
    return Inertia::render('public/contact/Contact');
})->name('contact.index');

//admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () { {/*Route::get('dashboard', function () {
       return Inertia::render('dashboard');
   })->name('dashboard');*/
    }

    Route::get('/', DashboardController::class)->name('dashboard');

    //news routes
    Route::get('news', NewsIndexController::class)->name('news.index');
//Route::inertia('news/create', 'admin/news/create')->name('news.create');
    Route::get('news/create', NewsCreateController::class)->name('news.create');
    Route::post('news', NewsStoreController::class)->name('news.store');
    Route::get('news/{news}/edit', NewsEditController::class)->name('news.edit');
    Route::put('news/{news}', NewsUpdateController::class)->name('news.update');
    Route::delete('news/{news}', NewsDestroyController::class)->name('news.destroy');

    //noticeboard routes
    Route::get('noticeboard', NoticeboardIndexController::class)->name('noticeboard.index');
    Route::get('noticeboard/create', NoticeboardCreateController::class)->name('noticeboard.create');
    Route::post('noticeboard', NoticeboardStoreController::class)->name('noticeboard.store');
    Route::get('noticeboard/{noticeboard}/edit', NoticeboardEditController::class)->name('noticeboard.edit');
    Route::put('noticeboard/{noticeboard}', NoticeboardUpdateController::class)->name('noticeboard.update');
    Route::delete('noticeboard/{noticeboard}', NoticeboardDestroyController::class)->name('noticeboard.destroy');

    //governing body routes
    Route::get('governingbody', GoverningBodyIndexController::class)->name('governingbody.index');
    Route::inertia('governingbody/create', 'admin/governingbody/create')->name('governingbody.create');
    Route::post('governingbody', GoverningBodyStoreController::class)->name('governingbody.store');
    //Route::post('governingbody', GoverningBodyStoreController::class)->name('governingbody.store');
    Route::get('governingbody/{governingbody}/edit', GoverningBodyEditController::class)->name('governingbody.edit');
    Route::put('governingbody/{governingbody}', GoverningBodyUpdateController::class)->name('governingbody.update');
    Route::delete('governingbody/{governingbody}', GoverningBodyDestroyController::class)->name('governingbody.destroy');

    //faculty routes
    Route::get('faculty', FacultyInfoIndexController::class)->name('faculty.index');
    Route::get('faculty/create', FacultyInfoCreateController::class)->name('faculty.create');
    Route::post('faculty', FacultyInfoStoreController::class)->name('faculty.store');
    Route::get('faculty/{faculty}/edit', FacultyInfoEditController::class)->name('faculty.edit');
    Route::put('faculty/{faculty}', FacultyInfoUpdateController::class)->name('faculty.update');
    Route::delete('faculty/{faculty}', FacultyInfoDestroyController::class)->name('faculty.destroy');

    //schedule routes
    Route::get('schedule', [ScheduleController::class, 'index'])->name('schedule.index');
    Route::inertia('schedule/class/create', 'admin/schedule/class-schedule/create')->name('schedule.class.create');
    Route::post('schedule/class', [ScheduleController::class, 'storeClassSchedule'])->name('schedule.class.store');
    Route::get('schedule/class/{classSchedule}/edit', [ScheduleController::class, 'editClassSchedule'])->name('schedule.class.edit');
    Route::put('schedule/class/{classSchedule}', [ScheduleController::class, 'updateClassSchedule'])->name('schedule.class.update');
    Route::delete('schedule/class/{classSchedule}', [ScheduleController::class, 'destroyClassSchedule'])->name('schedule.class.destroy');

    //faculty schedule routes
    Route::inertia('schedule/faculty/create', 'admin/schedule/faculty-schedule/create')->name('schedule.faculty.create');
    Route::post('schedule/faculty', [ScheduleController::class, 'storeFacultySchedule'])->name('schedule.faculty.store');
    Route::get('schedule/faculty/{facultySchedule}/edit', [ScheduleController::class, 'editFacultySchedule'])->name('schedule.faculty.edit');
    Route::put('schedule/faculty/{facultySchedule}', [ScheduleController::class, 'updateFacultySchedule'])->name('schedule.faculty.update');
    Route::delete('schedule/faculty/{facultySchedule}', [ScheduleController::class, 'destroyFacultySchedule'])->name('schedule.faculty.destroy');

    //admission routes
    Route::get('admission', [AdmissionController::class, 'index'])->name('admission.index');

    // Admission Spot routes
    Route::get('admission/spots/create', AdmissionSpotCreateController::class)->name('admission.spots.create');
    Route::post('admission/spots', [AdmissionSpotController::class, 'store'])->name('admission.spots.store');
    Route::get('admission/spots/{admissionSpot}/edit', AdmissionSpotEditController::class)->name('admission.spots.edit');
    Route::put('admission/spots/{admissionSpot}', [AdmissionSpotController::class, 'update'])->name('admission.spots.update');
    Route::delete('admission/spots/{admissionSpot}', [AdmissionSpotController::class, 'destroy'])->name('admission.spots.destroy');

    // Tuition Fee routes
    Route::post('admission/tuition-fees', [TuitionFeeController::class, 'store'])->name('admission.tuition-fees.store');
    Route::put('admission/tuition-fees/{tuitionFee}', [TuitionFeeController::class, 'update'])->name('admission.tuition-fees.update');
    Route::delete('admission/tuition-fees/{tuitionFee}', [TuitionFeeController::class, 'destroy'])->name('admission.tuition-fees.destroy');

    // Admission Post routes
    Route::get('admission/posts/create', AdmissionPostCreateController::class)->name('admission.posts.create');
    Route::post('admission/posts', [AdmissionPostController::class, 'store'])->name('admission.posts.store');
    Route::get('admission/posts/{admissionPost}/edit', AdmissionPostEditController::class)->name('admission.posts.edit');
    Route::put('admission/posts/{admissionPost}', [AdmissionPostController::class, 'update'])->name('admission.posts.update');
    Route::delete('admission/posts/{admissionPost}', [AdmissionPostController::class, 'destroy'])->name('admission.posts.destroy');

    // Admission Promo Image routes
    Route::post('admission/promo-images', [AdmissionPromoImageController::class, 'store'])->name('admission.promo-images.store');
    Route::put('admission/promo-images/{admissionPromoImage}', [AdmissionPromoImageController::class, 'update'])->name('admission.promo-images.update');
    Route::delete('admission/promo-images/{admissionPromoImage}', [AdmissionPromoImageController::class, 'destroy'])->name('admission.promo-images.destroy');

    // Homepage Ad routes
    Route::post('admission/homepage-ads', [HomepageAdController::class, 'store'])->name('admission.homepage-ads.store');
    Route::put('admission/homepage-ads/{homepageAd}', [HomepageAdController::class, 'update'])->name('admission.homepage-ads.update');
    Route::delete('admission/homepage-ads/{homepageAd}', [HomepageAdController::class, 'destroy'])->name('admission.homepage-ads.destroy');

    // Admission Modal routes
    Route::post('admission/modals', [AdmissionModalController::class, 'store'])->name('admission.modals.store');
    Route::put('admission/modals/{admissionModal}', [AdmissionModalController::class, 'update'])->name('admission.modals.update');
    Route::delete('admission/modals/{admissionModal}', [AdmissionModalController::class, 'destroy'])->name('admission.modals.destroy');

    // File Upload Routes
    Route::get('file-upload', [App\Http\Controllers\FileUploadController::class, 'index'])->name('file-upload.index');
    Route::resource('galleries', App\Http\Controllers\GalleryController::class)->except(['index', 'show', 'create', 'edit']);
    Route::resource('file-uploads', App\Http\Controllers\FileUploadItemController::class)->except(['index', 'show', 'create', 'edit']);
    Route::resource('image-sliders', App\Http\Controllers\ImageSliderController::class)->except(['index', 'show', 'create', 'edit']);

    // Site Settings Routes
    Route::get('site-settings', [App\Http\Controllers\SiteSettingsController::class, 'index'])->name('site-settings.index');
    Route::put('site-settings', [App\Http\Controllers\SiteSettingsController::class, 'update'])->name('site-settings.update');
    Route::get('site-settings/download', [App\Http\Controllers\SiteSettingsController::class, 'download'])->name('site-settings.download');
    Route::post('site-settings/restore', [App\Http\Controllers\SiteSettingsController::class, 'restore'])->name('site-settings.restore');

    // Access Control Routes
    Route::get('access-control', [App\Http\Controllers\Admin\AccessControlController::class, 'index'])->name('access-control.index');
   

    // User Management Routes
    
    //Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::get('access-control/users/create', [UserController::class, 'create'])->name('acl.users.create');
    Route::post('access-control/users', [UserController::class, 'store'])->name('acl.users.store');
    Route::get('access-control/users/{user}/edit', [UserController::class, 'edit'])->name('acl.users.edit');
    Route::put('access-control/users/{user}', [UserController::class, 'update'])->name('acl.users.update');
    Route::delete('access-control/users/{user}', [UserController::class, 'destroy'])->name('acl.users.destroy');
   

    // Role Management Routes
    Route::resource('roles', App\Http\Controllers\Admin\RoleController::class);
   
    // Permission Management Routes
    Route::resource('permissions', App\Http\Controllers\Admin\PermissionController::class);

    //editor upload routes
   // Route::post('editor-uploads', [EditorUploadController::class, 'store'])->name('editor.uploads.store');
    //Route::delete('editor-uploads/{upload}', [EditorUploadController::class, 'destroy'])->name('editor.uploads.destroy');
    Route::post('/editor-uploads', [EditorUploadController::class, 'store'])->withoutMiddleware([\Inertia\Middleware::class])->name('editor.uploads.store');
    Route::delete('/editor-uploads/{upload}', [EditorUploadController::class, 'destroy'])->withoutMiddleware([\Inertia\Middleware::class])->name('editor.uploads.destroy');


});



require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
