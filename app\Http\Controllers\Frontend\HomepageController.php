<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Controllers\SiteSettingsController;
use App\Http\Resources\NewsResource;
use App\Http\Resources\NoticeboardResource;
use App\Http\Resources\ImageSliderResource;
use App\Http\Resources\GalleryResource;
use App\Http\Resources\AdmissionModalResource;
use App\Http\Resources\HomepageAdResource;
use App\Models\News;
use App\Models\Noticeboard;
use App\Models\ImageSlider;
use App\Models\Gallery;
use App\Models\AdmissionModal;
use App\Models\HomepageAd;
use Illuminate\Http\Request;
use Inertia\Inertia;

class HomepageController extends Controller
{
    public function index(Request $request)
    {
        // Get latest news
        $blogs = NewsResource::collection(News::latest()->take(5)->get());

        // Get latest notices
        $notices = NoticeboardResource::collection(Noticeboard::latest()->take(5)->get());

        // Get image sliders ordered by order column
        $imageSliders = ImageSliderResource::collection(ImageSlider::orderBy('order', 'asc')->get());

        // Get latest gallery images
        $galleryImages = GalleryResource::collection(Gallery::latest()->take(6)->get());

        // Get active admission modal (most recent if multiple active)
        $admissionModal = AdmissionModal::where('active_status', 'active')
            ->orderBy('updated_at', 'desc')
            ->first();

        // Get active homepage ad (most recent if multiple active)
        $homepageAd = HomepageAd::where('active_status', 'active')
            ->orderBy('updated_at', 'desc')
            ->first() ?? (object) [
                'updated_at' => now(),
                'created_at' => now(),
                'active_status' => 'inactive',
                'id' => 0,
                'ad_title' => 'Default Ad Title',
                'program_info_title' => 'CSE | BBA | ECE (Professional)| BMB (Honors)',
                'session' => 'SESSION STARTS SOON',
                'offer_title' => 'NIST OFFERS WAIVER ON ADMISSION FEES',
                'offer_text' => 'Advanced and spot admission ongoing for all programs. Please contact us for more information.',
            ];

        // Get site settings for frontend
        $siteSettings = SiteSettingsController::getSettingsForFrontend();

        return Inertia::render('welcome', [
            'blogs' => $blogs,
            'notices' => $notices,
            'imageSliders' => $imageSliders,
            'galleryImages' => $galleryImages,
            'admissionModal' => $admissionModal ? new AdmissionModalResource($admissionModal) : null,
            'homepageAd' => $homepageAd ? new HomepageAdResource($homepageAd) : null,
            'siteSettings' => $siteSettings,
        ]);
    }
}
