<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Inertia\Inertia;
use Carbon\Carbon;

class SiteSettingsController extends Controller
{
    private $settingsPath = 'app/settings/siteSetting.json';
    private $backupPath = 'app/settings/siteSetting_backup.json';

    public function index()
    {
        $settings = $this->getSettings();
        $hasBackup = $this->hasBackup();
        $backupDate = $hasBackup ? $this->getBackupDate() : null;

        return Inertia::render('admin/site-settings/index', [
            'settings' => $settings,
            'hasBackup' => $hasBackup,
            'backupDate' => $backupDate,
        ]);
    }

    public function update(Request $request)
    {
        try {
            $newSettings = $request->validate([
                'youtubeLink' => 'nullable|string|max:500',
                'primaryPhone' => 'nullable|string|max:20',
                'secondaryPhone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:100',
                'facebook' => 'nullable|url|max:500',
                'youtube' => 'nullable|url|max:500',
                'linkedin' => 'nullable|url|max:500',
                'address' => 'nullable|string|max:500',
                'footerMenu1Heading' => 'nullable|string|max:100',
                'footerMenu1Paragraph' => 'nullable|string|max:500',
            ]);

            // Create backup of current settings before updating
            $this->createBackup();

            // Update settings
            $this->saveSettings($newSettings);

            return redirect()->back()->with('success', 'Site settings updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update settings: ' . $e->getMessage());
        }
    }

    public function download()
    {
        try {
            $settings = $this->getSettings();
            $filename = 'site-settings-' . Carbon::now()->format('Y-m-d-H-i-s') . '.json';
            
            return response()->json($settings)
                ->header('Content-Type', 'application/json')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to download settings: ' . $e->getMessage());
        }
    }

    public function restore()
    {
        try {
            if (!$this->hasBackup()) {
                return redirect()->back()->with('error', 'No backup available to restore');
            }

            $backupSettings = $this->getBackupSettings();
            $this->saveSettings($backupSettings);

            return redirect()->back()->with('success', 'Settings restored from backup successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to restore backup: ' . $e->getMessage());
        }
    }

    private function getSettings()
    {
        $fullPath = base_path($this->settingsPath);
        
        if (!File::exists($fullPath)) {
            // Create default settings if file doesn't exist
            $defaultSettings = [
                'youtubeLink' => '',
                'primaryPhone' => '',
                'secondaryPhone' => '',
                'email' => '',
                'facebook' => '',
                'youtube' => '',
                'linkedin' => '',
                'address' => '',
                'footerMenu1Heading' => '',
                'footerMenu1Paragraph' => '',
            ];
            $this->saveSettings($defaultSettings);
            return $defaultSettings;
        }

        $content = File::get($fullPath);
        return json_decode($content, true) ?? [];
    }

    private function saveSettings($settings)
    {
        $fullPath = base_path($this->settingsPath);
        $directory = dirname($fullPath);
        
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        File::put($fullPath, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
    }

    private function createBackup()
    {
        try {
            $currentSettings = $this->getSettings();
            $backupPath = base_path($this->backupPath);
            $directory = dirname($backupPath);
            
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
            }

            File::put($backupPath, json_encode($currentSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        } catch (\Exception $e) {
            // Log error but don't fail the main operation
            \Log::error('Failed to create backup: ' . $e->getMessage());
        }
    }

    private function hasBackup()
    {
        return File::exists(base_path($this->backupPath));
    }

    private function getBackupDate()
    {
        if (!$this->hasBackup()) {
            return null;
        }

        $timestamp = File::lastModified(base_path($this->backupPath));
        return Carbon::createFromTimestamp($timestamp);
    }

    private function getBackupSettings()
    {
        $backupPath = base_path($this->backupPath);
        
        if (!File::exists($backupPath)) {
            throw new \Exception('Backup file not found');
        }

        $content = File::get($backupPath);
        return json_decode($content, true) ?? [];
    }

    /**
     * Get settings for frontend use (can be called from other controllers)
     */
    public static function getSettingsForFrontend()
    {
        $controller = new self();
        return $controller->getSettings();
    }
}
