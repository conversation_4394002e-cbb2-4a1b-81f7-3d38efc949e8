import Pagination from '@/components/pagination';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import StatusToggle from '@/components/ui/status-toggle';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Edit, Search, Trash2, Eye } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import '../text-editor/RichTextStyles.css';
import AdminLayout from '@/layouts/admin-layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'News',
        href: route('admin.news.index'),
    },
];

interface Flash {
    success?: string;
    danger?: string;
}

interface News {
    published_at: string;
    active_status: 'active' | 'inactive';
    id: number;
    title: string;
    content: string;
    image: string;
}

interface PaginatedNewsData {
    data: News[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
}

export default function NewsIndex({ news }: { news: PaginatedNewsData }) {
    const { flash } = usePage<{ flash: Flash }>().props;
   const { showConfirmation } = useConfirmation();
    
    const [viewingNews, setViewingNews] = useState<News | null>(null);

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    // Search functionality
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.news.index'), { search: query }, { preserveState: true, replace: true });
        }, 500),
    ).current;

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        handleSearch(e.target.value);
    };

    const handleView = (newsItem: News) => {
        setViewingNews(newsItem);
    };

    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete News',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.news.destroy', id));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="News" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="relative w-full sm:w-1/3">
                            <Input
                                id={'search'}
                                className="peer ps-9"
                                placeholder="Search news by title..."
                                type="search"
                                onChange={onSearchChange}
                            />
                            <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
                                <Search size={16} aria-hidden="true" />
                            </div>
                        </div>

                        <Button>
                            <Link href={route('admin.news.create')} prefetch>
                                Add News
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="min-w-[60px]">ID</TableHead>
                                            <TableHead className="min-w-[200px]">Title</TableHead>
                                            <TableHead className="min-w-[100px]">Image</TableHead>
                                            <TableHead className="min-w-[120px]">Published At</TableHead>
                                            <TableHead className="min-w-[100px]">Status</TableHead>
                                            <TableHead className="min-w-[150px]">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                <TableBody>
                                    {news.data.map((newsItem) => (
                                        <TableRow key={newsItem.id}>
                                            <TableCell className="font-medium">{newsItem.id}</TableCell>
                                            <TableCell className="whitespace-normal break-words text-sm">{newsItem.title}</TableCell>
                                            <TableCell>
                                                {newsItem.image ? (
                                                    <img src={newsItem.image} alt={newsItem.title} className="h-16 w-16 rounded object-cover" />
                                                ) : (
                                                    <span className="text-gray-500">No Image</span>
                                                )}
                                            </TableCell>
                                            <TableCell>{newsItem.published_at}</TableCell>
                                           
                                            <TableCell>
                                                <StatusToggle
                                                    id={newsItem.id}
                                                    currentStatus={newsItem.active_status}
                                                    updateRoute={route('admin.news.update', newsItem.id)}
                                                    activeLabel="Published"
                                                    inactiveLabel="Unpublished"
                                                    confirmTitle="Change Publication Status"
                                                    confirmDescription="Are you sure you want to change the publication status of this news?"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex flex-col gap-1 md:flex-row md:space-x-1">
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button size="sm" variant="outline" onClick={() => handleView(newsItem)} className="w-full md:w-auto">
                                                                <Eye className="h-4 w-4 md:mr-0 mr-2" />
                                                                <span className="md:hidden">View</span>
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>View</p>
                                                        </TooltipContent>
                                                    </Tooltip>

                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button asChild size={'sm'} variant="outline" className="w-full md:w-auto">
                                                                <Link href={route('admin.news.edit', newsItem.id)} prefetch>
                                                                    <Edit className="h-4 w-4 md:mr-0 mr-2" />
                                                                    <span className="md:hidden">Edit</span>
                                                                </Link>
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>Edit</p>
                                                        </TooltipContent>
                                                    </Tooltip>

                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button
                                                                size={'sm'}
                                                                variant={'destructive'}
                                                                onClick={() => handleDelete(newsItem.id, newsItem.title)}
                                                                className="w-full md:w-auto"
                                                            >
                                                                <Trash2 className="h-4 w-4 md:mr-0 mr-2" />
                                                                <span className="md:hidden">Delete</span>
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>Delete</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="mt-4">
                        <Pagination meta={news.meta} />
                    </div>
                </div>
            </div>

            {/* View News Dialog */}
            <Dialog open={!!viewingNews} onOpenChange={() => setViewingNews(null)}>
                <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="text-xl font-bold">View News</DialogTitle>
                    </DialogHeader>
                    {viewingNews && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 className="text-lg font-semibold mb-2">News Details</h3>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="font-medium text-gray-700">ID:</span>
                                            <span className="ml-2">{viewingNews.id}</span>
                                        </div>
                                        <div>
                                            <span className="font-medium text-gray-700">Title:</span>
                                            <span className="ml-2">{viewingNews.title}</span>
                                        </div>
                                        <div>
                                            <span className="font-medium text-gray-700">Published At:</span>
                                            <span className="ml-2">{viewingNews.published_at}</span>
                                        </div>
                                        <div>
                                            <span className="font-medium text-gray-700">Status:</span>
                                            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                                                viewingNews.active_status === 'active'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {viewingNews.active_status === 'active' ? 'Published' : 'Unpublished'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold mb-2">Featured Image</h3>
                                    {viewingNews.image ? (
                                        <img
                                            src={viewingNews.image}
                                            alt={viewingNews.title}
                                            className="w-full max-w-md h-auto rounded border object-cover"
                                        />
                                    ) : (
                                        <div className="w-full max-w-md h-48 bg-gray-100 rounded border flex items-center justify-center">
                                            <span className="text-gray-500">No Image</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className='bg-white p-4 rounded-lg shadow-lg'>
                                <h3 className="text-lg font-semibold mb-2">Content</h3>
                                <div
                                    className="richtext-output"
                                    dangerouslySetInnerHTML={{ __html: viewingNews.content }}
                                />
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}

