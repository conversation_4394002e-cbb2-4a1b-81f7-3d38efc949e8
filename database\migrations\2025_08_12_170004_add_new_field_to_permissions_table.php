<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            //
            $table->string('module')->after('name');
            $table->string('label')->nullable()->after('name');
            $table->string('description')->nullable()->after('name');
            $table->boolean('is_active')->default(true)->after('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            //
            $table->dropColumn('module');
            $table->dropColumn('label');
            $table->dropColumn('description');
            $table->dropColumn('is_active');
        });
    }
};
