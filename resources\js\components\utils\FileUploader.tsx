import { Button } from '@/components/ui/button';
import { LoaderCircle } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

const FileUpload = ({ imagePreviewUrl = null, showRemoveFileButton = true, onUpdateModelValue, onRemoveFile }) => {
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [file, setFile] = useState(null);
    const [imagePreview, setImagePreview] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const acceptedFormats = 'image/*';

    useEffect(() => {
        if (imagePreviewUrl) {
            setImagePreview(imagePreviewUrl);
            onUpdateModelValue(null);
        }
    }, [imagePreviewUrl, onUpdateModelValue]);

    const triggerFileInput = () => {
    if (isLoading || !fileInputRef.current) return;

    setIsLoading(true);

    const input = fileInputRef.current;

    // Clear any previous value to ensure onChange fires even if same file selected
    input.value = '';

    let changeFired = false;

    const handleChange = (e: Event) => {
        changeFired = true;
        input.removeEventListener('change', handleChange);
    };

    const handleFocusBack = () => {
        setTimeout(() => {
            if (!changeFired) {
                setIsLoading(false); // User canceled file dialog
            }
            window.removeEventListener('focus', handleFocusBack);
        }, 300); // slight delay to ensure change fires first if applicable
    };

    input.addEventListener('change', handleChange);
    window.addEventListener('focus', handleFocusBack);

    input.click();
};

    const removeFile = () => {
        setFile(null);
        setImagePreview(null);
        setIsLoading(false);
        onUpdateModelValue(null);
        if (imagePreviewUrl) {
            onRemoveFile(true);
        }
    };

    const handleFileChange = (event) => {
        const uploadedFile = event.target.files[0];
        onUpdateModelValue(uploadedFile);

        if (!uploadedFile) {
            setIsLoading(false);
            return;
        }

        setFile(uploadedFile);
        const reader = new FileReader();
        reader.onload = (e) => {
            setImagePreview(e.target.result);
        };
        reader.readAsDataURL(uploadedFile);
        setIsLoading(false);
       // event.target.value = null; // prevents errors if the same file is uploaded twice
    };

    const cancelFileChange = () => {
        setIsLoading(false);
    };

    return (
        <div className="border-neutral-6 flex flex-col items-center space-y-4 rounded-lg border-2 border-dashed p-4">
            <input ref={fileInputRef} type="file" accept={acceptedFormats} hidden onChange={handleFileChange} />

            <div className="flex flex-row space-x-3">
                <Button className="bg-blue-600 hover:bg-blue-700" onClick={triggerFileInput}>
                    <span>{!isLoading ? 'Upload File' : <LoaderCircle className="h-4 w-4 animate-spin" />}</span>
                </Button>

                {showRemoveFileButton && (file || imagePreview) && (
                    <Button className="bg-red-500 hover:bg-red-600" onClick={removeFile}>
                        Remove File
                    </Button>
                )}
            </div>

            {file && (
                <div className="text-center text-sm">
                    <p>
                        <strong className="text-neutral-12">File Name:</strong> {file.name}
                    </p>
                    <p>
                        <strong className="text-neutral-12">File Size:</strong> {file.size / 1024} KB
                    </p>
                </div>
            )}
            {imagePreview && (
                <div className="flex w-full justify-center">
                    <img src={imagePreview} alt="image preview" className="h-auto max-w-full rounded-sm shadow-md" />
                </div>
            )}
        </div>
    );
};

export default FileUpload;
