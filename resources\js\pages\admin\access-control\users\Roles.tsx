import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User, type Role } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface UserRolesProps {
    user: User;
    allRoles: Role[];
    userRoles: number[];
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Access Control',
        href: '/admin/access-control',
    },
    {
        title: 'User Roles',
        href: '#',
    },
];

export default function UserRoles({ user, allRoles, userRoles }: UserRolesProps) {
    const { data, setData, put, processing, errors } = useForm({
        roles: userRoles,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.access-control.users.roles.update', user.id));
    };

    const handleRoleChange = (roleId: number, checked: boolean) => {
        if (checked) {
            setData('roles', [...data.roles, roleId]);
        } else {
            setData('roles', data.roles.filter(id => id !== roleId));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`User Roles - ${user.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Card className="mx-auto w-full max-w-2xl">
                    <CardHeader>
                        <CardTitle>Assign Roles to: {user.name}</CardTitle>
                        <CardDescription>
                            Select the roles you want to assign to this user. Roles define what the user can access and do in the system.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid gap-4">
                                <Label className="text-base font-medium">Available Roles</Label>
                                {allRoles.length === 0 ? (
                                    <p className="text-sm text-muted-foreground">No roles available. Create roles first.</p>
                                ) : (
                                    <div className="grid gap-3">
                                        {allRoles.map((role) => (
                                            <div key={role.id} className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`role-${role.id}`}
                                                    checked={data.roles.includes(role.id)}
                                                    onCheckedChange={(checked) => handleRoleChange(role.id, checked as boolean)}
                                                />
                                                <Label 
                                                    htmlFor={`role-${role.id}`}
                                                    className="text-sm font-normal cursor-pointer"
                                                >
                                                    {role.name}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {errors.roles && <p className="text-sm text-red-600">{errors.roles}</p>}
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('admin.access-control.index', { tab: 'users' })}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Saving...' : 'Save Roles'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
