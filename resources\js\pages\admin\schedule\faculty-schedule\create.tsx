import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Add Faculty Schedule',
        href: 'schedule/faculty/create',
    },
];

export default function ClassScheduleCreate() {
    const { data, setData, post, errors, processing } = useForm<{
        faculty_name: string;
        department: string;
        scheduleEmbedLink: string| null;
    }>({
        faculty_name: '',
        department: '',
        scheduleEmbedLink: null,
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        post(route('admin.schedule.faculty.store'));
    }


    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Faculty Schedule" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Faculty Schedule</div>

                        <Button>
                            <Link href={route('admin.schedule.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="faculty_name">Faculty Name</Label>
                                        <Input
                                            type="text"
                                            id="faculty_name"
                                            placeholder="Faculty Name"
                                            value={data.faculty_name}
                                            onChange={(e) => setData('faculty_name', e.target.value)}
                                            aria-invalid={!!errors.faculty_name}
                                        />
                                        <InputError message={errors.faculty_name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="department">Department</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department"
                                            value={data.department}
                                            onChange={(e) => setData('department', e.target.value)}
                                            aria-invalid={!!errors.department}
                                        />
                                        <InputError message={errors.department} />
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Label htmlFor="scheduleEmbedLink">Schedule Embed Link</Label>
                                    <Input
                                        type="text"
                                        id="scheduleEmbedLink"
                                        placeholder="Schedule Embed Link"
                                        value={data.scheduleEmbedLink ?? ''}
                                        onChange={(e) => setData('scheduleEmbedLink', e.target.value)}
                                        aria-invalid={!!errors.scheduleEmbedLink}
                                    />
                                </div>
                               

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Faculty Schedule</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
