<?php

namespace App\Http\Controllers\Frontend\Academic;

use App\Http\Controllers\Controller;
use App\Models\FacultySchedule;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FacultyScheduleController extends Controller
{
    //
    public function index(Request $request)
    {
        // Get all faculty schedules from database ordered by creation (first created shows first)
        $facultySchedule = FacultySchedule::orderBy('created_at', 'asc')
            ->get()
            ->map(function ($schedule) {
                // Validate if the embed link is a proper Google Calendar link
                $isValidEmbedLink = $this->isValidGoogleCalendarLink($schedule->scheduleEmbedLink);

                return [
                    'id' => $schedule->id,
                    'dept' => $schedule->department,
                    'facultyName' => $schedule->faculty_name,
                    'mediaType' => 'embed',
                    'loadURL' => $schedule->scheduleEmbedLink,
                    'isValidLink' => $isValidEmbedLink,
                ];
            })
            ->toArray();

        return Inertia::render('public/academic/faculty-schedule/index', [
            'facultySchedule' => $facultySchedule,
        ]);
    }

    /**
     * Validate if the provided link is a valid Google Calendar embed link
     */
    private function isValidGoogleCalendarLink($link)
    {
        if (empty($link) || !is_string($link)) {
            return false;
        }

        // Check if it's a valid URL and contains Google Calendar embed pattern
        return filter_var($link, FILTER_VALIDATE_URL) &&
               (strpos($link, 'google.com/calendar/embed') !== false ||
                strpos($link, 'calendar.google.com/calendar/embed') !== false);
    }
}


