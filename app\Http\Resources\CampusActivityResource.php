<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CampusActivityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "slug" => $this->slug,
            "caption" => $this->caption,
            "category" => $this->category,
            "description" => $this->description,
            "image" => asset('storage/'.$this->image),
            "published_date" => $this->published_date->format('d M Y'),
        ];
    }
}
