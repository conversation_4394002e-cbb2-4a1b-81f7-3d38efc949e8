import type { ImageOptions } from "@tiptap/extension-image"
import { Image as TiptapImage } from "@tiptap/extension-image"
import type { Editor } from "@tiptap/react"
import { ReactNodeViewRenderer } from "@tiptap/react"
import { ImageViewBlock } from "./components/image-view-block"
import {
  filterFiles,
  randomId,
  type FileError,
  type FileValidationOptions,
} from "../../utils"
import { ReplaceStep } from "@tiptap/pm/transform"
import type { Attrs } from "@tiptap/pm/model"

type ImageAction = "download" | "copyImage" | "copyLink"

interface DownloadImageCommandProps {
  src: string
  alt?: string
}

interface ImageActionProps extends DownloadImageCommandProps {
  action: ImageAction
}

export type UploadReturnType =
  | string
  | {
      id: string | number
      src: string
    }

interface CustomImageOptions
  extends ImageOptions,
    Omit<FileValidationOptions, "allowBase64"> {
  uploadFn?: (file: File, editor: Editor) => Promise<UploadReturnType>
  onImageRemoved?: (props: Attrs) => void
  onActionSuccess?: (props: ImageActionProps) => void
  onActionError?: (error: Error, props: ImageActionProps) => void
  downloadImage?: (
    props: ImageActionProps,
    options: CustomImageOptions
  ) => Promise<void>
  copyImage?: (
    props: ImageActionProps,
    options: CustomImageOptions
  ) => Promise<void>
  copyLink?: (
    props: ImageActionProps,
    options: CustomImageOptions
  ) => Promise<void>
  onValidationError?: (errors: FileError[]) => void
  // NOTE: we keep onToggle optional, but we'll route to setImages in the hook
  onToggle?: (editor: Editor, files: File[], pos: number) => void
}

declare module "@tiptap/react" {
  interface Commands<ReturnType> {
    setImages: {
      setImages: (
        attrs: { src: string | File; alt?: string; title?: string }[]
      ) => ReturnType
    }
    downloadImage: {
      downloadImage: (attrs: DownloadImageCommandProps) => ReturnType
    }
    copyImage: {
      copyImage: (attrs: DownloadImageCommandProps) => ReturnType
    }
    copyLink: {
      copyLink: (attrs: DownloadImageCommandProps) => ReturnType
    }
    toggleImage: {
      toggleImage: () => ReturnType
    }
  }
}

/** Helper: find image node by its `id` attr and update attributes */
function updateImageAttrsById(
  editor: Editor,
  nodeTypeName: string,
  id: string | number,
  next: Record<string, any>
) {
  const { state, view } = editor
  let found = false

  state.doc.descendants((node, pos) => {
    if (node.type.name === nodeTypeName && node.attrs?.id == id) {
      const attrs = { ...node.attrs, ...next }
      const tr = state.tr.setNodeMarkup(pos, undefined, attrs)
      view.dispatch(tr)
      found = true
      return false // stop traversal
    }
    return true
  })

  return found
}

/** Internal: handle upload for a single file node that we just inserted */
async function uploadAndReplace(
  editor: Editor,
  nodeTypeName: string,
  tempId: string | number,
  file: File,
  uploadFn?: (file: File, editor: Editor) => Promise<UploadReturnType>,
  onError?: (error: Error) => void
) {
  if (!uploadFn) return

  try {
    console.log("Starting upload for file:", file.name, "tempId:", tempId)
    const res = await uploadFn(file, editor)
    console.log("Upload completed:", res)

    const payload =
      typeof res === "string" ? { id: tempId, src: res } : { id: res.id, src: res.src }

    // Revoke the existing blob if present
    let oldSrc: string | null = null
    editor.state.doc.descendants((node) => {
      if (node.type.name === nodeTypeName && node.attrs?.id == tempId) {
        oldSrc = node.attrs?.src ?? null
        return false
      }
      return true
    })
    if (oldSrc && typeof oldSrc === "string" && oldSrc.startsWith("blob:")) {
      try {
        URL.revokeObjectURL(oldSrc)
      } catch {
        // Ignore revoke errors
      }
    }

    // Replace attributes on that node (also swap temp id -> server id)
    const updated = updateImageAttrsById(editor, nodeTypeName, tempId, payload)
    if (!updated) {
      console.warn("Failed to update image node with tempId:", tempId)
    }
  } catch (error) {
    // Keep blob so the user at least sees the image, but log error
    console.error("Image upload failed:", error)

    // Call error handler if provided
    if (onError) {
      onError(error as Error)
    }

    // Optionally mark the image as failed in the UI
    updateImageAttrsById(editor, nodeTypeName, tempId, {
      'data-upload-failed': 'true',
      title: `Upload failed: ${(error as Error).message}`
    })
  }
}

export const Image = TiptapImage.extend<CustomImageOptions>({
  atom: true,

  addOptions() {
    return {
      ...this.parent?.(),
      allowedMimeTypes: [],
      maxFileSize: 0,
      uploadFn: undefined,
      onToggle: undefined,
      downloadImage: undefined,
      copyImage: undefined,
      copyLink: undefined,
    }
  },

  addAttributes() {
    return {
      src: { default: null },
      alt: { default: null },
      title: { default: null },
      id: { default: null },
      width: { default: null },
      height: { default: null },
      fileName: { default: null },
      align: {
        default: "center",
        parseHTML: (el) => el.getAttribute("data-align") || "center",
        renderHTML: (attrs) => (attrs.align ? { "data-align": attrs.align } : {}),
      },
    }
  },

  addCommands() {
    return {
      setImages:
        (attrs) =>
        ({ commands }) => {
          const [validImages, errors] = filterFiles(attrs, {
            allowedMimeTypes: this.options.allowedMimeTypes,
            maxFileSize: this.options.maxFileSize,
            allowBase64: this.options.allowBase64,
          })

          if (errors.length > 0 && this.options.onValidationError) {
            this.options.onValidationError(errors)
          }
          if (validImages.length === 0) return false

          // 1) Build nodes (blob for File, direct src for string)
          const pendingUploads: Array<{ tempId: string; file: File }> = []

          const nodes = validImages.map((image) => {
            if (image.src instanceof File) {
              const file = image.src
              const blobUrl = URL.createObjectURL(file)
              const tempId = String(randomId())

              pendingUploads.push({ tempId, file })

              return {
                type: this.type.name,
                attrs: {
                  id: tempId,
                  src: blobUrl,
                  alt: image.alt,
                  title: image.title,
                  fileName: file.name,
                },
              }
            } else {
              return {
                type: this.type.name,
                attrs: {
                  id: String(randomId()),
                  src: image.src,
                  alt: image.alt,
                  title: image.title,
                  fileName: null,
                },
              }
            }
          })

          const ok = commands.insertContent(nodes)
          if (!ok) return false

          // 2) Kick off uploads for files (async replace per node)
          for (const u of pendingUploads) {
            void uploadAndReplace(
              this.editor,
              this.type.name,
              u.tempId,
              u.file,
              this.options.uploadFn,
              this.options.onActionError ? (error) => {
                this.options.onActionError?.(error, { action: 'download', src: '', alt: '' })
              } : undefined
            )
          }

          return true
        },

      downloadImage: (attrs) => () => {
        const fn = this.options.downloadImage
        if (!fn) return true
        void fn({ ...attrs, action: "download" }, this.options)
        return true
      },

      copyImage: (attrs) => () => {
        const fn = this.options.copyImage
        if (!fn) return true
        void fn({ ...attrs, action: "copyImage" }, this.options)
        return true
      },

      copyLink: (attrs) => () => {
        const fn = this.options.copyLink
        if (!fn) return true
        void fn({ ...attrs, action: "copyLink" }, this.options)
        return true
      },

      toggleImage:
        () =>
        ({ editor }) => {
          // Native file picker → route through same setImages flow
          const input = document.createElement("input")
          input.type = "file"
          input.accept = this.options.allowedMimeTypes.join(",")
          input.onchange = () => {
            const files = input.files
            if (!files) return

            const [validImages, errors] = filterFiles(Array.from(files), {
              allowedMimeTypes: this.options.allowedMimeTypes,
              maxFileSize: this.options.maxFileSize,
              allowBase64: this.options.allowBase64,
            })

            if (errors.length > 0 && this.options.onValidationError) {
              this.options.onValidationError(errors)
              return
            }
            if (validImages.length === 0) return

            // Prefer our own pipeline (upload via setImages)
            editor
              .chain()
              .focus()
              .setImages(validImages.map((f) => ({ src: f, alt: f.name, title: f.name })))
              .run()
          }
          input.click()
          return true
        },
    }
  },

  onTransaction({ transaction }) {
    // Detect image node deletions and notify consumer (for server delete)
    transaction.steps.forEach((step) => {
      if (step instanceof ReplaceStep && step.slice.size === 0) {
        const deleted = transaction.before.content.cut(step.from, step.to)

        deleted.forEach((node) => {
          if (node.type.name === "image") {
            const attrs = node.attrs
            if (attrs.src && typeof attrs.src === "string" && attrs.src.startsWith("blob:")) {
              try {
                URL.revokeObjectURL(attrs.src)
              } catch {
                // Ignore revoke errors
              }
            }
            this.options.onImageRemoved?.(attrs)
          }
        })
      }
    })
  },

  addNodeView() {
    return ReactNodeViewRenderer(ImageViewBlock, { className: "block-node" })
  },
})
