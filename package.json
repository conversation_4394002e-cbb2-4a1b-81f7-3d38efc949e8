{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/lodash": "^4.17.20", "@types/node": "^22.13.5", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "typescript-eslint": "^8.23.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "@headlessui/react": "^2.2.0", "@inertiajs/react": "^2.0.12", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tiptap/extension-bubble-menu": "^3.0.5", "@tiptap/extension-code-block-lowlight": "^3.0.3", "@tiptap/extension-color": "^3.0.3", "@tiptap/extension-heading": "^3.0.3", "@tiptap/extension-horizontal-rule": "^3.0.3", "@tiptap/extension-image": "^3.0.3", "@tiptap/extension-link": "^3.0.3", "@tiptap/extension-placeholder": "^3.0.3", "@tiptap/extension-table": "^3.0.1", "@tiptap/extension-table-cell": "^3.0.1", "@tiptap/extension-table-header": "^3.0.1", "@tiptap/extension-table-row": "^3.0.1", "@tiptap/extension-text-align": "^3.0.9", "@tiptap/extension-text-style": "^3.0.3", "@tiptap/extension-typography": "^3.0.3", "@tiptap/extension-underline": "^3.0.3", "@tiptap/extension-youtube": "^3.0.1", "@tiptap/pm": "^3.0.3", "@tiptap/react": "^3.0.3", "@tiptap/starter-kit": "^3.0.3", "@types/react": "^19.0.3", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.0.1", "date-fns": "^4.1.0", "globals": "^15.14.0", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.11", "jspdf": "^3.0.1", "laravel-vite-plugin": "^1.0", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "quill": "^2.0.3", "quill-blot-formatter": "^1.0.5", "quill-image-resize-module-react": "^3.0.0", "react": "^19.0.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0", "react-floating-whatsapp": "^5.0.8", "react-icons": "^5.5.0", "react-medium-image-zoom": "^5.3.0", "remixicon": "^4.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}