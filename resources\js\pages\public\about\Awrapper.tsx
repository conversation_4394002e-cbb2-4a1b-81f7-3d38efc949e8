import { Award, BookChe<PERSON>, Users, BookOpenText } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface AwardItem {
  icon: React.ElementType;
  count: string;
  title: string;
}

const awardsData: AwardItem[] = [
  { icon: Award, count: '300', title: 'SUCCESS STORIES' },
  { icon: Users, count: '30', title: 'EXPERIENCED FACULTY' },
  { icon: BookCheck, count: '400', title: 'ENROLLED' },
  { icon: BookOpenText, count: '6', title: 'COURSES' },
];

const Counter = ({ target }: { target: number }) => {
  const [count, setCount] = useState(0);
  const ref = useRef<HTMLDivElement | null>(null);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !hasAnimated) {
          let start = 0;
          const duration = 2000; // 2 seconds
          const stepTime = Math.abs(Math.floor(duration / target));

          const timer = setInterval(() => {
            start += 1;
            setCount(start);
            if (start >= target) {
              clearInterval(timer);
              setHasAnimated(true);
            }
          }, stepTime);
        }
      },
      { threshold: 0.5 }
    );

    if (ref.current) observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, [target, hasAnimated]);

  return (
    <div ref={ref}>
      {count.toLocaleString()}+
    </div>
  );
};

const Awrapper = () => {
  return (
    <section className="bg-[#0a4727] py-12">
      <div className="container mx-auto flex flex-wrap justify-around gap-5">
        {awardsData.map((item) => {
          const Icon = item.icon;
          return (
            <div
              key={item.title}
              className="flex-1 basis-full p-4 text-center text-white sm:basis-[48%] xl:basis-[22%]"
            >
              <div className="mb-5 flex justify-center">
                <Icon size={60} />
              </div>
              <div className="text">
                <h1 className="text-4xl font-bold sm:text-3xl xl:text-5xl">
                  <Counter target={parseInt(item.count)} />
                </h1>
                <h3 className="mt-2.5 text-base font-normal uppercase sm:text-sm">
                  {item.title}
                </h3>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};

export default Awrapper;
