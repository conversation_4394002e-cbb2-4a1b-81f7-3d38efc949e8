<?php

namespace App\Http\Controllers\Frontend\About;

use App\Models\GoverningBody;
use App\Http\Controllers\Controller;
use App\Http\Resources\GoverningbodyResource;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GoverningBodyController extends Controller
{
    //
    public function index()
    {
        $governingbody = GoverningbodyResource::collection(GoverningBody::orderBy('display_order', 'asc')->get());
        return Inertia::render('public/about/governing-body/index', [
            'governingbody' => $governingbody,
        ]);
    }
}




