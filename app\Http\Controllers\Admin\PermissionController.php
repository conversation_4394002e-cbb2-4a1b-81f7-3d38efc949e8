<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Http\Requests\PermissionRequest;
use App\Http\Resources\PermissionResource;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use App\Models\Permission;
use Illuminate\Support\Str;

class PermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');

        $permissions = Permission::query()
            ->when($search, function ($query, $search) {
                return $query->where('module', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(5)
            ->withQueryString();

        return Inertia::render('admin/access-control/permissions/PermissionsIndex', [
            'permissions' => $permissions,
            'filters' => [
                'search' => $search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/AccessControl/Permissions/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PermissionRequest $request)
    {

        $permission = Permission::create([
            'module' => $request->module,
            'label' => $request->label,
            'name' => Str::slug($request->label),
            'description' => $request->description,
        ]);

        if ($permission) {
            return redirect()->route('admin.access-control.index', ['tab' => 'permissions'])
                ->with('success', 'Permission created successfully.');
        }
        return redirect()->back()->with('error', 'Unable to create Permission. Please try again!');
    }

    /**
     * Display the specified resource.
     */


    /**
     * Show the form for editing the specified resource.
     */

    /**
     * Update the specified resource in storage.
     */
    public function update(PermissionRequest $request, Permission $permission)
    {

        if ($permission) {
            $permission->module = $request->module;
            $permission->label = $request->label;
            $permission->name = Str::slug($request->label);
            $permission->description = $request->description;
            $permission->save();

            return redirect()->route('admin.access-control.index', ['tab' => 'permissions'])
                ->with('success', 'Permission Updated successfully.');
        }
        return redirect()->back()->with('error', 'Unable to create Permission. Please try again!');

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission)
    {
        if ($permission) {
            $permission->delete();
            return redirect()->route('admin.access-control.index', ['tab' => 'permissions'])
                ->with('success', 'Permission deleted successfully.');
        }
        return redirect()->back()->with('error', 'Unable to delete Permission. Please try again!');
    }
}
