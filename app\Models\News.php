<?php


namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\EditorUpload;

class News extends Model
{
    protected $fillable = [
        'title','slug','content','image',
        'meta_tag_title','meta_tag_description','excerpt',
        'active_status',
        'published_at',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];

    public function editorUploads()
    {
        return $this->morphToMany(
            EditorUpload::class,
            'uploadable',
            'editor_uploadables',      // pivot
            'uploadable_id',           // this model key on pivot
            'editor_upload_id'         // related key on pivot
        )->withTimestamps();
    }

    /*public function uploads()
    {
        return $this->morphMany(EditorUpload::class, 'uploadable');
    }*/

    /*protected static function booted()
    {
        static::deleting(function ($news) {
            foreach ($news->uploads as $upload) {
                // Delete file from storage if exists
                if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                    Storage::disk('public')->delete($upload->path);
                }
                // Delete DB record for the upload
                $upload->delete();
            }
        });
    }*/
}
