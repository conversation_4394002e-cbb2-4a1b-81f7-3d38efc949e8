<?php

namespace App\Http\Controllers;

use App\Models\AdmissionSpot;
use App\Http\Resources\AdmissionSpotResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdmissionSpotController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'campaign_name' => 'required|string|max:255',
            'session' => 'required|string|max:255',
            'program_name' => 'required|string|max:255',
            'application_start_date' => 'required|date',
            'application_end_date' => 'required|date',
            'admission_test_date' => 'nullable|date',
            'session_start_date' => 'nullable|date',
            'date_created' => 'required|date',
        ]);

        $data['active_status'] = 'unpublished';

        AdmissionSpot::create($data);

        return redirect(route('admin.admission.index', ['tab' => 'admission_spot']))
            ->with('success', 'Admission spot created successfully');
    }

    public function update(Request $request, AdmissionSpot $admissionSpot)
    {
        // Check if this is a status-only update (from StatusToggle component)
        if ($request->has('active_status') && count($request->all()) === 1) {
            $data = $request->validate([
                'active_status' => 'required|in:unpublished,published',
            ]);
        } else {
            // Full update validation
            $data = $request->validate([
                'campaign_name' => 'required|string|max:255',
                'session' => 'required|string|max:255',
                'program_name' => 'required|string|max:255',
                'application_start_date' => 'required|date',
                'application_end_date' => 'required|date',
                'admission_test_date' => 'nullable|date',
                'session_start_date' => 'nullable|date',
                'active_status' => 'required|in:unpublished,published',
                'date_created' => 'required|date',
            ]);
        }

        $admissionSpot->update($data);

        return redirect(route('admin.admission.index', ['tab' => 'admission_spot']))
            ->with('success', 'Admission spot updated successfully');
    }

    public function destroy(AdmissionSpot $admissionSpot)
    {
        $admissionSpot->delete();

        return redirect(route('admin.admission.index', ['tab' => 'admission_spot']))
            ->with('success', 'Admission spot deleted successfully');
    }
}
