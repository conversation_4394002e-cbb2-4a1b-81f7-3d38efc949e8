import React, { useState } from 'react';
import { router, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import Pagination from '@/components/pagination';
import { useConfirmation } from '@/contexts/confirmation-context';
import StatusToggle from '@/components/ui/status-toggle';

interface AdmissionSpotData {
    id: number;
    campaign_name: string;
    session: string;
    program_name: string;
    application_start_date: string;
    application_end_date: string;
    admission_test_date: string | null;
    session_start_date: string | null;
    active_status: 'unpublished' | 'published';
    date_created: string;
}

interface PaginatedData {
    data: AdmissionSpotData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface AdmissionSpotTabProps {
    data: PaginatedData;
}

export default function AdmissionSpotTab({ data }: AdmissionSpotTabProps) {
    const { showConfirmation } = useConfirmation();
    const [viewingItem, setViewingItem] = useState<AdmissionSpotData | null>(null);

    const handleDelete = async (id: number, campaignName: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Admission Spot',
            description: `Are you sure you want to delete "${campaignName}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.spots.destroy', id));
        }
    };

    const handleView = (item: AdmissionSpotData) => {
        setViewingItem(item);
    };

    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'Not set';
        return format(new Date(dateString), 'MMM dd, yyyy');
    };

    return (
        <div className="space-y-4 md:space-y-6">
            <div className="flex flex-col gap-3 md:flex-row md:justify-between md:items-center">
                <h2 className="text-lg md:text-xl font-semibold">Admission Spots</h2>
                <Button asChild className="w-full md:w-auto">
                    <Link href={route('admin.admission.spots.create')}>
                        <Plus className="mr-2 h-4 w-4" />
                        <span className="hidden sm:inline">Add Admission Spot</span>
                        <span className="sm:hidden">Add Spot</span>
                    </Link>
                </Button>
            </div>

            <Card>
                <CardContent className="p-0">
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="min-w-[150px]">Campaign</TableHead>
                                    <TableHead className="min-w-[100px]">Session</TableHead>
                                    <TableHead className="min-w-[120px]">Program</TableHead>
                                    <TableHead className="min-w-[180px]">Application Period</TableHead>
                                    <TableHead className="min-w-[100px]">Status</TableHead>
                                    <TableHead className="min-w-[120px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell className="font-medium">{item.campaign_name}</TableCell>
                                    <TableCell>{item.session}</TableCell>
                                    <TableCell>{item.program_name}</TableCell>
                                    <TableCell>
                                        {item.application_start_date} to {item.application_end_date}
                                    </TableCell>
                                    <TableCell>
                                        <StatusToggle
                                            id={item.id}
                                            currentStatus={item.active_status}
                                            updateRoute={route('admin.admission.spots.update', item.id)}
                                            activeLabel="Published"
                                            inactiveLabel="Unpublished"
                                            confirmTitle="Change Publication Status"
                                            confirmDescription="Are you sure you want to change the publication status of this admission spot?"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex flex-col gap-1 md:flex-row md:space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)} className="w-full md:w-auto">
                                                        <Eye className="h-4 w-4 md:mr-0 mr-2" />
                                                        <span className="md:hidden">View</span>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" asChild className="w-full md:w-auto">
                                                        <Link href={route('admin.admission.spots.edit', item.id)}>
                                                            <Edit className="h-4 w-4 md:mr-0 mr-2" />
                                                            <span className="md:hidden">Edit</span>
                                                        </Link>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.campaign_name)} className="w-full md:w-auto">
                                                        <Trash2 className="h-4 w-4 md:mr-0 mr-2" />
                                                        <span className="md:hidden">Delete</span>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    </div>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="text-lg md:text-xl">View Admission Spot</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Campaign Name</Label>
                                    <p className="text-sm font-medium">{viewingItem.campaign_name}</p>
                                </div>
                                <div>
                                    <Label>Session</Label>
                                    <p className="text-sm font-medium">{viewingItem.session}</p>
                                </div>
                            </div>

                            <div>
                                <Label>Program Name</Label>
                                <p className="text-sm font-medium">{viewingItem.program_name}</p>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Application Start Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.application_start_date)}</p>
                                </div>
                                <div>
                                    <Label>Application End Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.application_end_date)}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Admission Test Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.admission_test_date)}</p>
                                </div>
                                <div>
                                    <Label>Session Start Date</Label>
                                    <p className="text-sm">{formatDate(viewingItem.session_start_date)}</p>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Status</Label>
                                    <Badge variant={viewingItem.active_status === 'published' ? 'default' : 'secondary'}>
                                        {viewingItem.active_status === 'published' ? 'Published' : 'Unpublished'}
                                    </Badge>
                                </div>
                                <div>
                                    <Label>Date Created</Label>
                                    <p className="text-sm">{formatDate(viewingItem.date_created)}</p>
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
