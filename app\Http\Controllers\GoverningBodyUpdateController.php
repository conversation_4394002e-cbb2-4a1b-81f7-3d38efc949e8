<?php

namespace App\Http\Controllers;

use App\Models\GoverningBody;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class GoverningBodyUpdateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, GoverningBody $governingbody)
    {
        $data = $request->validate([
            'name' => 'required',
            'designation' => 'required',
            'display_order' => 'required|integer|min:0',
            'profile_URL' => 'nullable|url',
            'profile_image' => 'nullable|image',
        ]);

        $data['profile_image'] = $governingbody->profile_image;

        if ($request->hasFile('profile_image')) {
            if ($governingbody->profile_image) {
                Storage::disk('public')->delete($governingbody->profile_image);
            }
            $data['profile_image'] = storeSanitizedFile($request->file('profile_image'), 'uploads/governingbody');
        }

        $governingbody->update($data);

        return redirect(route('admin.governingbody.index'))->with('success', 'Entry Updated successfully');
    }
}
