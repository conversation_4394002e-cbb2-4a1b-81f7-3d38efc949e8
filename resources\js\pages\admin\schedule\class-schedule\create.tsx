import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Add Class Schedule',
        href: 'schedule/class/create',
    },
];

export default function ClassScheduleCreate() {
    const { data, setData, post, errors, processing } = useForm<{
        department: string;
        session: string;
        batch: string;
        scheduleEmbedLink: string | null;
    }>({
        department: '',
        session: '',
        batch: '',
        scheduleEmbedLink: null,
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        post(route('admin.schedule.class.store'));
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Class Schedule" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Class Schedule</div>

                        <Button>
                            <Link href={route('admin.schedule.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="department">Department</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department"
                                            value={data.department}
                                            onChange={(e) => setData('department', e.target.value)}
                                            aria-invalid={!!errors.department}
                                        />
                                        <InputError message={errors.department} />
                                    </div>

                                    <div>
                                        <Label htmlFor="session">Session</Label>
                                        <Input
                                            type="text"
                                            id="session"
                                            placeholder="Session"
                                            value={data.session}
                                            onChange={(e) => setData('session', e.target.value)}
                                            aria-invalid={!!errors.session}
                                        />
                                        <InputError message={errors.session} />
                                    </div>

                                    <div>
                                        <Label htmlFor="batch">Batch</Label>
                                        <Input
                                            type="text"
                                            id="batch"
                                            placeholder="Batch"
                                            value={data.batch}
                                            onChange={(e) => setData('batch', e.target.value)}
                                            aria-invalid={!!errors.batch}
                                        />
                                        <InputError message={errors.batch} />
                                    </div>
                                </div>

                                <div className="mt-4">
                                    <Label htmlFor="scheduleEmbedLink">Schedule Embed Link</Label>
                                    <Input
                                        type="text"
                                        id="scheduleEmbedLink"
                                        placeholder="Schedule Embed Link"
                                        value={data.scheduleEmbedLink ?? ''}
                                        onChange={(e) => setData('scheduleEmbedLink', e.target.value)}
                                        aria-invalid={!!errors.scheduleEmbedLink}
                                    />
                                    <InputError message={errors.scheduleEmbedLink} />
                                </div>
                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Class Schedule</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
