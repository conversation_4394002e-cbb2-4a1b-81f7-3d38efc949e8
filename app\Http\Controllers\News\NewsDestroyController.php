<?php

namespace App\Http\Controllers\News;
use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class NewsDestroyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(News $news)
    {
       if ($news->image) {
           Storage::disk("public")->delete($news->image);
       }
        
        DB::transaction(function () use ($news) {
            // Collect related uploads
            $uploads = $news->editorUploads()->get();

            // Detach pivot first
            $news->editorUploads()->detach();

            // Delete the faculty profile itself
            $news->delete();

            // Cleanup orphaned uploads
            foreach ($uploads as $upload) {
                $stillUsed = $upload->admissionPosts()->exists() || $upload->news()->exists() || $upload->facultyProfiles()->exists()|| $upload->noticeboards()->exists(); // check if used by any other model
                if (!$stillUsed) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
            }
        });


        return redirect(route('admin.news.index'))->with('success', 'News deleted successfully');
    }
}
