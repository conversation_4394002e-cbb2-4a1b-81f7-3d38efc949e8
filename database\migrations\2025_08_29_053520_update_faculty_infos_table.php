<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('faculty_infos', function (Blueprint $table) {
            // Convert columns to longText
            $table->longText('about')->nullable()->change();
            $table->longText('education')->nullable()->change();
            $table->longText('research')->nullable()->change();

            // Add active_status column, default 'active'
            $table->enum('active_status', ['active', 'inactive'])->default('active')->after('secondary_phone');
        });
    }

    public function down(): void
    {
        Schema::table('faculty_infos', function (Blueprint $table) {
            // Revert back to text
            $table->text('about')->nullable()->change();
            $table->text('education')->nullable()->change();
            $table->text('research')->nullable()->change();

            // Drop the active_status column
            $table->dropColumn('active_status');
        });
    }
};
