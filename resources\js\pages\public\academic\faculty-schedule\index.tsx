import { Head } from '@inertiajs/react';
import React, { useState } from 'react';
import { FaChevronDown, FaChevronRight } from 'react-icons/fa';
import Back from '../../common/back/Back';

// 1. TYPESCRIPT INTERFACE
// Defines the shape of a single faculty schedule object for type safety.
interface Faculty {
  id: number;
  dept: string;
  facultyName: string;
  mediaType: string;
  loadURL: string;
  isValidLink: boolean; // New field to check if link is valid
}

interface FacultyScheduleProps {
  facultySchedule: Faculty[];
}

// 2. DATA SOURCE
// This data can be imported from another file (e.g., ../scheduleData).


// 3. REACT COMPONENT
const FacultySchedule: React.FC<FacultyScheduleProps> = ({ facultySchedule }) => {
  // State to track the currently open accordion item using its unique ID.
  const [openAccordionId, setOpenAccordionId] = useState<number | null>(null);

  // Function to toggle the accordion's open/closed state.
  const toggleItem = (id: number) => {
    setOpenAccordionId(openAccordionId === id ? null : id);
  };

  return (
    <>
      <Head title="Faculty Schedule" />
      <Back title="Faculty Schedule" />
    <section className="w-full max-w-5xl mx-auto p-4 mt-15 mb-15 font-sans">
      <div className="space-y-2">
        {facultySchedule.map((faculty) => (
          <div key={faculty.id} className="bg-[#2c3e50] rounded-md overflow-hidden">
            {/* Accordion Title */}
            <div
              className="flex justify-between items-center p-4 cursor-pointer select-none hover:bg-[#34495e] transition-colors"
              onClick={() => toggleItem(faculty.id)}
            >
              <span className="text-white font-normal text-lg">{faculty.facultyName}</span>
              <span className="text-white">
                {openAccordionId === faculty.id ? <FaChevronDown /> : <FaChevronRight />}
              </span>
            </div>

            {/* Accordion Content with smooth animation */}
            <div
              className={`transition-all duration-500 ease-in-out grid ${
                openAccordionId === faculty.id
                  ? 'grid-rows-[1fr] opacity-100'
                  // The content is hidden by setting grid-rows to 0fr
                  : 'grid-rows-[0fr] opacity-0'
              }`}
            >
              <div className="overflow-hidden">
                <div className="bg-[#ecf0f1] p-4">
                  {faculty.isValidLink ? (
                    <iframe
                      src={faculty.loadURL}
                      className="w-full h-[440px] border-0"
                      title={`${faculty.facultyName}'s Schedule`}
                    ></iframe>
                  ) : (
                    <div className="w-full h-[440px] bg-gray-100 flex items-center justify-center border-0">
                      <div className="text-center text-gray-600">
                        <div className="text-xl font-semibold mb-3">Schedule Not Available</div>
                        <div className="text-sm max-w-md">
                          The faculty schedule for {faculty.facultyName} is not available or not configured.
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
    </>
  );
};

export default FacultySchedule;