// Import necessary libraries and components
import { Link } from '@inertiajs/react';
import { ChevronsLeft, ChevronsRight } from 'lucide-react'; // Icons are already imported
import { cn } from '@/lib/utils'; // Assuming you have a utility for class names

// Define the props that the Pagination component expects
type PaginationProps = {
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
};

// Helper component to render each pagination link or button
const PaginationLink = ({ link }: { link: PaginationProps['meta']['links'][0] }) => {
    // Decode HTML entities to get clean text
    const decodedLabel = new DOMParser().parseFromString(link.label, 'text/html').body.textContent || '';
    
    // --- Determine what content to render: Icon + Text, or Number ---
    let content;
    if (decodedLabel.includes('Previous')) {
        content = (
            <span className="flex items-center gap-1.5">
                <ChevronsLeft className="h-4 w-4" />
                Prev
            </span>
        );
    } else if (decodedLabel.includes('Next')) {
        content = (
            <span className="flex items-center gap-1.5">
                Next
                <ChevronsRight className="h-4 w-4" />
            </span>
        );
    } else {
        content = decodedLabel; // It's a number
    }

    // Render a disabled span if there's no URL
    if (!link.url) {
        return (
            <span className="cursor-not-allowed rounded-md border px-3 py-1.5 text-sm text-muted-foreground">
                {content}
            </span>
        );
    }
    
    // Render an active Inertia Link if there is a URL
    return (
        <Link
            href={link.url}
            preserveState
            preserveScroll
            className={cn(
                'cursor-pointer rounded-md border px-3 py-1.5 text-sm hover:bg-accent',
                link.active && 'border-green-600 font-semibold text-primary'
            )}
        >
            {content}
        </Link>
    );
};

// The main Pagination component
const Pagination = ({ meta }: PaginationProps) => {
    // Don't render pagination if there's only one page
    if (meta.links.length <= 3) {
        return null; 
    }

    return (
        <div className="mt-4 flex flex-wrap items-center justify-center gap-y-2 sm:justify-between">
            <p className="text-sm text-muted-foreground">
                Showing <strong>{meta.from}</strong> to <strong>{meta.to}</strong> of <strong>{meta.total}</strong> results
            </p>

            <nav className="flex items-center space-x-1">
                {meta.links.map((link, index) => (
                    <PaginationLink key={index} link={link} />
                ))}
            </nav>
        </div>
    );
};

export default Pagination;