import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TruncatedCellProps {
  text?: string | null;
  maxLength?: number;
}

const TruncatedCell: React.FC<TruncatedCellProps> = ({
  text,
  maxLength = 50,
}) => {
  if (!text) return <>N/A</>; // Return "N/A" if text is null or undefined

  const truncatedText =
    text.length > maxLength ? text.slice(0, maxLength) + "..." : text;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-help">{truncatedText}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs break-words">{text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default TruncatedCell;
