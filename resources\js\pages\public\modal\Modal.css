/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  /* Modal Content */
  .modal-content {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
    text-align: center;
  }
  
  /* Modal Close Button */
  .modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
  }
  
  /* Modal Image */
  .modal-image {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  /* Modal Footer Buttons */
  .modal-footer {
    display: flex;
    justify-content: space-between;
  }
  
  .modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
  }
  
  .view-btn {
    background-color: #28a745;
    color: white;
  }
  
  .close-btn {
    background-color: #dc3545;
    color: white;
  }
  