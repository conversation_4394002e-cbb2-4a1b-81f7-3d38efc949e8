<?php

namespace App\Http\Controllers\News;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\News;
use App\Models\EditorUpload;
use App\Helper\SlugHelper;

class NewsStoreController extends Controller
{
    public function __invoke(Request $request)
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'image' => ['required', 'image', 'max:2048'], // featured image
            'meta_tag_title' => ['nullable', 'string', 'max:255'],
            'meta_tag_description' => ['nullable', 'string', 'max:255'],
            'excerpt' => ['nullable', 'string'],
            'draft_token' => ['required', 'uuid'],
            'published_date' => ['required', 'date'],
            'active_status' => ['required', 'in:active,inactive'],
        ]);

        // Generate slug from title
        $validated['slug'] = SlugHelper::makeSlug($validated['title'], 'news', 'slug', 70);
        $validated['content'] = str_replace(url('/'), '', $validated['content'] );
        return DB::transaction(function () use ($request, $validated) {
            // Handle featured image
            $featuredPath = null;
            if ($request->hasFile('image')) {
                $featuredPath = storeSanitizedFile($request->file('image'), 'uploads/news');
            }

            // Create news record
            $news = News::create([
                'title' => $validated['title'],
                'slug' => $validated['slug'],
                'content' => $validated['content'] ?? '',
                'image' => $featuredPath,
                'meta_tag_title' => $validated['meta_tag_title'] ?? null,
                'meta_tag_description' => $validated['meta_tag_description'] ?? null,
                'excerpt' => $validated['excerpt'] ?? null,
                'published_at' => $validated['published_date'],
                'active_status' => $validated['active_status'],
            ]);

            // Extract image paths actually referenced in content
            $pathsInContent = $this->extractStoragePathsFromHtml($news->content);

            // Pick uploads belonging to this draft token and referenced in content
            $uploads = EditorUpload::where('draft_token', $validated['draft_token'])
                ->whereIn('path', $pathsInContent)
                ->get();

            // Attach valid uploads to News
            foreach ($uploads as $upload) {
                $news->editorUploads()->syncWithoutDetaching([$upload->id]);
                $upload->update(['draft_token' => null]); // clear draft
            }

            // Delete unused draft uploads for this token
            $unusedDrafts = EditorUpload::where('draft_token', $validated['draft_token'])
                ->whereNotIn('path', $pathsInContent)
                ->get();

            foreach ($unusedDrafts as $img) {
                if ($img->path && Storage::disk('public')->exists($img->path)) {
                    Storage::disk('public')->delete($img->path);
                }
                $img->delete();
            }

            return redirect()->route('admin.news.index')
                ->with('success', 'News created successfully');
        });
    }

 

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html)
            return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo.jpg" → "foo.jpg"
            ->values()
            ->all();
    }
}
