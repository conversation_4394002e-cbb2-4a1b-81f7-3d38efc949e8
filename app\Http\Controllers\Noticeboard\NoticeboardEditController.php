<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use App\Http\Resources\NoticeboardResource;
use Illuminate\Support\Str;

class NoticeboardEditController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Noticeboard $noticeboard)
    {
        //
        $draftToken = Str::uuid()->toString();
         return inertia('admin/noticeboard/edit', ['currentNoticeboard' => new NoticeboardResource($noticeboard),
         'draftToken' => $draftToken,]);
    }
}
