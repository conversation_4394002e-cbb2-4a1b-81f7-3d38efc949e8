<?php

use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware('auth')->group(function () {
    Route::redirect('accounts/settings', 'accounts/settings/profile');

    Route::get('accounts/settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('accounts/settings/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('accounts/settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('accounts/settings/password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::put('accounts/settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('accounts/settings/appearance', function () {
        return Inertia::render('settings/appearance');
    })->name('appearance');
});
