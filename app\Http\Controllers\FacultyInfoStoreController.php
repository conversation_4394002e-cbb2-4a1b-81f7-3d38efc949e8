<?php

namespace App\Http\Controllers;

use App\Models\FacultyInfo;
use App\Models\EditorUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class FacultyInfoStoreController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'image' => 'nullable|image|max:2048',
            'bio' => 'nullable|string',
            'about' => 'nullable|string',
            'education' => 'nullable|string',
            'research' => 'nullable|string',
            'interests' => 'nullable|string',
            'official_email' => 'required|email|unique:faculty_infos,official_email',
            'secondary_email' => 'nullable|email',
            'primary_phone' => 'required|string|max:20',
            'secondary_phone' => 'nullable|string|max:20',
            'active_status' => 'required|in:active,inactive',
            'draft_token' => 'required|uuid',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        return DB::transaction(function () use ($request, $validated) {
            // Handle featured image
            $imagePath = null;
            if ($request->hasFile('image')) {
                $imagePath = storeSanitizedFile($request->file('image'), 'uploads/faculty');
            }

            // Create faculty record
            $faculty = FacultyInfo::create([
                'name' => $validated['name'],
                'slug' => $validated['slug'],
                'designation' => $validated['designation'],
                'department' => $validated['department'],
                'image' => $imagePath,
                'bio' => $validated['bio'] ?? '',
                'about' => $validated['about'] ?? '',
                'education' => $validated['education'] ?? '',
                'research' => $validated['research'] ?? '',
                'interests' => $validated['interests'] ?? '',
                'official_email' => $validated['official_email'],
                'secondary_email' => $validated['secondary_email'],
                'primary_phone' => $validated['primary_phone'],
                'secondary_phone' => $validated['secondary_phone'],
                'active_status' => $validated['active_status'],
            ]);

            // Extract image paths actually referenced in tiptap editor content
            $pathsInContent = $this->extractStoragePathsFromHtml($faculty->about);
            $pathsInContent = array_merge($pathsInContent, $this->extractStoragePathsFromHtml($faculty->education));
            $pathsInContent = array_merge($pathsInContent, $this->extractStoragePathsFromHtml($faculty->research));
            $pathsInContent = array_unique($pathsInContent);

            // Pick uploads belonging to this draft token and referenced in content
            $uploads = EditorUpload::where('draft_token', $validated['draft_token'])
                ->whereIn('path', $pathsInContent)
                ->get();

            // Attach valid uploads to Faculty
            foreach ($uploads as $upload) {
                $faculty->editorUploads()->syncWithoutDetaching([$upload->id]);
                $upload->update(['draft_token' => null]); // clear draft
            }

            // Delete unused draft uploads for this token
            $unusedDrafts = EditorUpload::where('draft_token', $validated['draft_token'])
                ->whereNotIn('path', $pathsInContent)
                ->get();

            foreach ($unusedDrafts as $img) {
                if ($img->path && Storage::disk('public')->exists($img->path)) {
                    Storage::disk('public')->delete($img->path);
                }
                $img->delete();
            }

            return redirect()->route('admin.faculty.index')
                ->with('success', 'Faculty member added successfully');
        });
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn ($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn ($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn ($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo.jpg" → "foo.jpg"
            ->values()
            ->all();
    }
}
