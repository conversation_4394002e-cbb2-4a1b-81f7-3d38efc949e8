import React from 'react';
import { CalendarDays, CheckCircle, PencilLine, Clock } from 'lucide-react';

interface AdmissionSpotData {
  id: number;
  campaign_name: string;
  session: string;
  program_name: string;
  application_start_date: string;
  application_end_date: string;
  admission_test_date: string | null;
  session_start_date: string | null;
  active_status: string;
  date_created: string;
}

interface AdmissionDeadlinesProps {
  admissionSpot?: AdmissionSpotData | null;
}

const AdmissionDeadlines = ({ admissionSpot }: AdmissionDeadlinesProps) => {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "TBA";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatus = (dateString: string | null | undefined) => {
    if (!dateString) return "TBA";
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (targetDate < today) return "Completed";
    if (targetDate.getTime() === today.getTime()) return "Today";
    return "Upcoming";
  };

  const deadlines = [
    {
      title: "Application Starts",
      date: formatDate(admissionSpot?.application_start_date || null),
      status: getStatus(admissionSpot?.application_start_date || null),
      icon: PencilLine
    },
    {
      title: "Application Deadline",
      date: formatDate(admissionSpot?.application_end_date || null),
      status: getStatus(admissionSpot?.application_end_date || null),
      icon: Clock
    },
    {
      title: "Admission Test",
      date: formatDate(admissionSpot?.admission_test_date),
      status: getStatus(admissionSpot?.admission_test_date),
      icon: CalendarDays
    },
    {
      title: "Session Starts",
      date: formatDate(admissionSpot?.session_start_date),
      status: getStatus(admissionSpot?.session_start_date),
      icon: CheckCircle
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-center text-3xl font-bold text-gray-800">
          Admission Info: SESSION {admissionSpot?.session || "2024-2025"}
        </h2>
        {admissionSpot?.program_name && (
          <p className="text-center text-xl text-gray-600 mt-2 font-medium">
            {admissionSpot.program_name}
          </p>
        )}
      <div className="mt-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
        {deadlines.map((item, index) => {
          const Icon = item.icon;
          return (
            <div key={index} className="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-lg">
              <Icon className="mx-auto h-12 w-12 text-green-700" strokeWidth={1.5} />
              <h3 className="mt-4 text-xl font-semibold">{item.title}</h3>
              <p className="mt-2 text-2xl font-bold text-gray-600">{item.date}</p>
              <span className="mt-4 inline-block rounded-full bg-green-100 px-3 py-1 text-sm font-semibold text-green-800">{item.status}</span>
            </div>
          );
        })}
      </div>
       <div className="text-center mt-12">
            <a href="#" className="rounded-full bg-yellow-500 px-10 py-4 text-lg font-bold text-green-900 transition hover:bg-yellow-400">
                APPLY NOW
            </a>
        </div>
      </div>
    </section>
  );
};

export default AdmissionDeadlines;