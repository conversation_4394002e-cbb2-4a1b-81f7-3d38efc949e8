<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\EditorUpload;

class AdmissionPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'admission_circular_content',
        'active_status',
        'published_date',
    ];

    protected $casts = [
        'published_date' => 'date',
    ];

    public function editorUploads()
    {
        return $this->morphToMany(
            EditorUpload::class,
            'uploadable',
            'editor_uploadables',      // pivot
            'uploadable_id',           // this model key on pivot
            'editor_upload_id'         // related key on pivot
        )->withTimestamps();
    }

}
