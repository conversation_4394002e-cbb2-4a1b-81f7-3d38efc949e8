<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admission_promo_images', function (Blueprint $table) {
            $table->id();
            $table->string('left_banner_image');
            $table->string('right_banner_image_top');
            $table->string('right_banner_image_top_caption')->nullable();
            $table->string('right_banner_image_top_subtitle')->nullable();
            $table->string('right_banner_image_bottom');
            $table->string('right_banner_image_bottom_caption')->nullable();
            $table->string('right_banner_image_bottom_subtitle')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('inactive');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admission_promo_images');
    }
};
