import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Mail, Phone } from 'lucide-react';
import Back from '../../common/back/Back';

// Define the TypeScript interface for a faculty member from database
interface FacultyMember {
  id: number;
  name: string;
  slug: string;
  designation: string;
  department: string;
  image: string | null;
  bio: string;
  about: string;
  education: string;
  research: string;
  interests: string;
  official_email: string;
  secondary_email: string | null;
  primary_phone: string;
  secondary_phone: string | null;
  active_status: string;
  created_at: string;
  updated_at: string;
}

interface FacultyPageProps {
  facultyMembers: {
    data: FacultyMember[];
  };
}
const FacultyCard = ({ member }: { member: FacultyMember }) => (
    <div className="group m-2.5 w-full overflow-hidden rounded-lg text-center shadow-lg transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl sm:w-[45%] lg:w-[23%]">
        <div className="relative">
            <img
                src={member.image || '/images/default-avatar.png'}
                alt={member.name}
                className="h-auto w-full object-cover aspect-square"
            />
            <div className="absolute bottom-5 left-1/2 flex -translate-x-1/2 gap-2.5 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                {member.official_email && <SocialIcon href={`mailto:${member.official_email}`} icon={Mail} />}
                {member.primary_phone && <SocialIcon href={`tel:${member.primary_phone}`} icon={Phone} />}
            </div>
        </div>
        <div className="p-4">
            <Link href={`/faculty-member/${member.id}/${member.slug}`} className="group-hover:text-blue-700">
                <h3 className="mb-1 cursor-pointer text-lg font-semibold text-gray-800 transition-colors">
                    {member.name}
                </h3>
            </Link>
            <p className="text-sm text-gray-600">{member.designation}</p>
            <p className="text-xs text-gray-500">{member.department}</p>
        </div>
    </div>
);

const SocialIcon = ({ href, icon: Icon }: { href: string; icon: React.ElementType }) => (
    <a href={href} target="_blank" rel="noopener noreferrer" className="flex h-10 w-10 items-center justify-center rounded-full bg-slate-800/70 text-white transition-colors duration-300 hover:bg-blue-800">
        <Icon size={20} />
    </a>
);




const FacultyPage = ({ facultyMembers }: FacultyPageProps) => {
    // Debug
    console.log('Faculty Members received:', facultyMembers);

    // Unwrap data
    const facultyArray = facultyMembers?.data ?? [];

    // Separate principal from other faculty members
    const principalMembers = facultyArray.filter((faculty) =>
        faculty.designation?.toLowerCase().includes('principal')
    );

    const otherMembers = facultyArray.filter((faculty) =>
        !faculty.designation?.toLowerCase().includes('principal')
    );

    return (
        <>
            <Head title="Faculty Members" />
            <Back title='Faculty Members' />
            <section className="py-24">
                <div className="mx-auto flex w-full flex-col items-center px-4 sm:w-[85%]">
                    {/* Principal/Head of Department (Centered) */}
                    {principalMembers.length > 0 && (
                        <div className="flex w-full justify-center mb-8">
                            {principalMembers.map((faculty) => (
                                <FacultyCard key={faculty.id} member={faculty} />
                            ))}
                        </div>
                    )}

                    {/* Other Faculty Members */}
                    <div className="flex w-full flex-wrap justify-center lg:justify-start">
                        {otherMembers.length > 0 ? (
                            otherMembers.map((faculty) => (
                                <FacultyCard key={faculty.id} member={faculty} />
                            ))
                        ) : (
                            <div className="w-full text-center py-8">
                                <p className="text-gray-500 text-lg">No faculty members found.</p>
                            </div>
                        )}
                    </div>
                </div>
            </section>
        </>
    );
};

export default FacultyPage;