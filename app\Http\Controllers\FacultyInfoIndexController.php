<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\FacultyInfo;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Resources\FacultyInfoResource;

class FacultyInfoIndexController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $facultyInfo = FacultyInfo::latest();

        if ($request->has('search') && $request->search !== null) {
            $facultyInfo->whereAny(['name', 'designation', 'department'], 'like', '%' . $request->search . '%');
        }

        $facultyInfo = $facultyInfo->paginate(5);

        return Inertia::render('admin/faculty/index', [
            'facultyData' => FacultyInfoResource::collection($facultyInfo),
        ]);
    }
}
