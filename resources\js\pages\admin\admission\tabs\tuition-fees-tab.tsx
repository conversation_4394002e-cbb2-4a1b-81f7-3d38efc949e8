import React, { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Edit, Plus, Trash2, Eye } from 'lucide-react';
import Pagination from '@/components/pagination';
import InputError from '@/components/input-error';
import { useConfirmation } from '@/contexts/confirmation-context';

interface TuitionFeeData {
    id: number;
    image: string;
    program_name: string;
    department: string;
}

interface PaginatedData {
    data: TuitionFeeData[];
    meta: {
        links: any[];
        total: number;
        from: number;
        to: number;
    };
}

interface TuitionFeesTabProps {
    data: PaginatedData;
}

export default function TuitionFeesTab({ data }: TuitionFeesTabProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<TuitionFeeData | null>(null);
    const [viewingItem, setViewingItem] = useState<TuitionFeeData | null>(null);

    const { data: formData, setData, post, processing, errors, reset } = useForm({
        image: null as File | null,
        program_name: '',
        department: '',
    });

    const handleCreate = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.admission.tuition-fees.store'), {
            onSuccess: () => {
                setIsCreateOpen(false);
                reset();
            },
        });
    };

    const handleEdit = (item: TuitionFeeData) => {
        setEditingItem(item);
        setData({
            image: null,
            program_name: item.program_name,
            department: item.department,
        });
        setIsEditOpen(true);
    };

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(route('admin.admission.tuition-fees.update', editingItem.id), {
                _method: 'put',
                ...formData,
            }, {
                onSuccess: () => {
                    setIsEditOpen(false);
                    setEditingItem(null);
                    reset();
                },
            });
        }
    };

    const handleDelete = async (id: number, programName: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Tuition Fee',
            description: `Are you sure you want to delete "${programName}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.admission.tuition-fees.destroy', id));
        }
    };

    const handleView = (item: TuitionFeeData) => {
        setViewingItem(item);
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Tuition Fees</h2>
                <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Tuition Fee
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Create Tuition Fee</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div>
                                <Label htmlFor="image">Image *</Label>
                                <Input
                                    id="image"
                                    type="file"
                                    accept="image/*"
                                    onChange={(e) => setData('image', e.target.files?.[0] || null)}
                                />
                                <InputError message={errors.image} />
                            </div>
                            <div>
                                <Label htmlFor="program_name">Program Name *</Label>
                                <Input
                                    id="program_name"
                                    value={formData.program_name}
                                    onChange={(e) => setData('program_name', e.target.value)}
                                    placeholder="Enter program name"
                                />
                                <InputError message={errors.program_name} />
                            </div>
                            <div>
                                <Label htmlFor="department">Department *</Label>
                                <Input
                                    id="department"
                                    value={formData.department}
                                    onChange={(e) => setData('department', e.target.value)}
                                    placeholder="Enter department"
                                />
                                <InputError message={errors.department} />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    Create
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            <Card>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Image</TableHead>
                                <TableHead>Program Name</TableHead>
                                <TableHead>Department</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data.data.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>
                                        {item.image ? (
                                            <img src={item.image} alt={item.program_name} className="w-16 h-16 object-cover rounded" />
                                        ) : (
                                            <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                <span className="text-xs text-gray-500">No Image</span>
                                            </div>
                                        )}
                                    </TableCell>
                                    <TableCell className="font-medium">{item.program_name}</TableCell>
                                    <TableCell>{item.department}</TableCell>
                                    <TableCell>
                                        <div className="flex space-x-1">
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>View</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Edit</p>
                                                </TooltipContent>
                                            </Tooltip>

                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.program_name)}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Delete</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />

            {/* Edit Dialog */}
            <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Tuition Fee</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleUpdate} className="space-y-4">
                        <div>
                            <Label htmlFor="edit_image">Image</Label>
                            <Input
                                id="edit_image"
                                type="file"
                                accept="image/*"
                                onChange={(e) => setData('image', e.target.files?.[0] || null)}
                            />
                            {editingItem?.image && (
                                <div className="mt-2">
                                    <img src={editingItem.image} alt="Current" className="w-20 h-20 object-cover rounded" />
                                </div>
                            )}
                        </div>
                        <div>
                            <Label htmlFor="edit_program_name">Program Name *</Label>
                            <Input
                                id="edit_program_name"
                                value={formData.program_name}
                                onChange={(e) => setData('program_name', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="edit_department">Department *</Label>
                            <Input
                                id="edit_department"
                                value={formData.department}
                                onChange={(e) => setData('department', e.target.value)}
                            />
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                Update
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-lg">
                    <DialogHeader>
                        <DialogTitle>View Tuition Fee</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div>
                                <Label>Program Name</Label>
                                <p className="text-sm font-medium">{viewingItem.program_name}</p>
                            </div>

                            <div>
                                <Label>Department</Label>
                                <p className="text-sm">{viewingItem.department}</p>
                            </div>

                            <div>
                                <Label>Image</Label>
                                {viewingItem.image ? (
                                    <img
                                        src={viewingItem.image}
                                        alt={viewingItem.program_name}
                                        className="w-full max-w-sm h-auto rounded border object-cover"
                                    />
                                ) : (
                                    <div className="w-32 h-32 bg-gray-200 rounded flex items-center justify-center">
                                        <span className="text-sm text-gray-500">No Image</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
