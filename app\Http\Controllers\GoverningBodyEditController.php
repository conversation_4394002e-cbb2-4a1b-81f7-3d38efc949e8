<?php

namespace App\Http\Controllers;

use App\Http\Resources\GoverningbodyResource;
use App\Models\GoverningBody;
use Inertia\Inertia;

class GoverningBodyEditController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GoverningBody $governingbody)
    {
        return Inertia::render('admin/governingbody/edit', ['currentGoverningBody' => new GoverningbodyResource($governingbody),]);
    }
}
