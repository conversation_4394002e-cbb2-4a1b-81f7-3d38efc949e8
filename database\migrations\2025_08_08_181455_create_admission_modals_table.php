<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admission_modals', function (Blueprint $table) {
            $table->id();
            $table->string('campaign_name')->nullable();
            $table->string('title')->nullable();
            $table->enum('active_status', ['active', 'inactive'])->default('inactive');
            $table->date('published_date');
            $table->string('image');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admission_modals');
    }
};
