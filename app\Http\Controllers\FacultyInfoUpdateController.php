<?php

namespace App\Http\Controllers;

use App\Models\FacultyInfo;
use App\Models\EditorUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FacultyInfoUpdateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, FacultyInfo $faculty)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'image' => 'nullable|image|max:2048',
            'bio' => 'nullable|string',
            'about' => 'nullable|string',
            'education' => 'nullable|string',
            'research' => 'nullable|string',
            'interests' => 'nullable|string',
            'official_email' => 'required|email|unique:faculty_infos,official_email,' . $faculty->id,
            'secondary_email' => 'nullable|email',
            'primary_phone' => 'required|string|max:20',
            'secondary_phone' => 'nullable|string|max:20',
            'active_status' => 'required|in:active,inactive',
            'draft_token' => 'nullable|uuid',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        return DB::transaction(function () use ($request, $faculty, $validated) {
            // Handle featured image (optional)
            if ($request->hasFile('image')) {
                if ($faculty->image && Storage::disk('public')->exists($faculty->image)) {
                    Storage::disk('public')->delete($faculty->image);
                }
                $faculty->image = storeSanitizedFile($request->file('image'), 'uploads/faculty');
            }

            // Save main fields
            $faculty->fill([
                'name' => $validated['name'],
                'slug' => $validated['slug'],
                'designation' => $validated['designation'],
                'department' => $validated['department'],
                'bio' => $validated['bio'] ?? '',
                'about' => $validated['about'] ?? '',
                'education' => $validated['education'] ?? '',
                'research' => $validated['research'] ?? '',
                'interests' => $validated['interests'] ?? '',
                'official_email' => $validated['official_email'],
                'secondary_email' => $validated['secondary_email'],
                'primary_phone' => $validated['primary_phone'],
                'secondary_phone' => $validated['secondary_phone'],
                'active_status' => $validated['active_status'],
            ])->save();

            // Collect paths from tiptap editor content
            $pathsInContent = $this->extractStoragePathsFromHtml($faculty->about);
            $pathsInContent = array_merge($pathsInContent, $this->extractStoragePathsFromHtml($faculty->education));
            $pathsInContent = array_merge($pathsInContent, $this->extractStoragePathsFromHtml($faculty->research));
            $pathsInContent = array_unique($pathsInContent);

            $attachedIds = [];

            // (A) Attach draft uploads if draft_token provided
            if (!empty($validated['draft_token']) && !empty($pathsInContent)) {
                $drafts = EditorUpload::where('draft_token', $validated['draft_token'])
                    ->whereIn('path', $pathsInContent)
                    ->get();

                foreach ($drafts as $upload) {
                    $faculty->editorUploads()->syncWithoutDetaching([$upload->id]);
                    $upload->update(['draft_token' => null]);
                    $attachedIds[] = $upload->id;
                }
            }

            // (B) Keep existing uploads that are still referenced in content
            $existingUploads = $faculty->editorUploads()
                ->whereIn('path', $pathsInContent)
                ->get();

            foreach ($existingUploads as $upload) {
                $attachedIds[] = $upload->id;
            }

            // (C) Remove stale uploads that are no longer in content
            $currentIds = $faculty->editorUploads()->pluck('editor_uploads.id')->all();
            $staleIds = array_diff($currentIds, $attachedIds);

            if (!empty($staleIds)) {
                $staleUploads = EditorUpload::whereIn('id', $staleIds)->get();
                foreach ($staleUploads as $upload) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
                $faculty->editorUploads()->detach($staleIds);
            }

            return redirect()->route('admin.faculty.index')
                ->with('success', 'Faculty member updated successfully');
        });
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn ($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn ($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn ($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo.jpg" → "foo.jpg"
            ->values()
            ->all();
    }
}
