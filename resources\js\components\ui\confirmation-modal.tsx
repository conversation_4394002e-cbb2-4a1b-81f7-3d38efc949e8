import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle, Loader2 } from 'lucide-react';

export interface ConfirmationModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void | Promise<void>;
    title?: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
    variant?: 'default' | 'destructive';
    icon?: React.ReactNode;
    isLoading?: boolean;
}

export default function ConfirmationModal({
    isOpen,
    onClose,
    onConfirm,
    title = 'Confirm Action',
    description = 'Are you sure you want to proceed with this action?',
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    variant = 'default',
    icon,
    isLoading = false,
}: ConfirmationModalProps) {
    const [processing, setProcessing] = useState(false);

    const handleConfirm = async () => {
        setProcessing(true);
        try {
            await onConfirm();
        } finally {
            setProcessing(false);
        }
    };

    const defaultIcon = variant === 'destructive' 
        ? <AlertTriangle className="h-6 w-6 text-red-500" />
        : null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <div className="flex items-center space-x-3">
                        {icon || defaultIcon}
                        <DialogTitle>{title}</DialogTitle>
                    </div>
                    <DialogDescription>
                        {description}
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={onClose}
                        disabled={processing || isLoading}
                    >
                        {cancelText}
                    </Button>
                    <Button
                        variant={variant}
                        onClick={handleConfirm}
                        disabled={processing || isLoading}
                    >
                        {(processing || isLoading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {confirmText}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

// Hook for easier usage
export function useConfirmationModal() {
    const [isOpen, setIsOpen] = useState(false);
    const [config, setConfig] = useState<Partial<ConfirmationModalProps>>({});

    const showConfirmation = (options: Partial<ConfirmationModalProps> & { onConfirm: () => void | Promise<void> }) => {
        setConfig(options);
        setIsOpen(true);
    };

    const hideConfirmation = () => {
        setIsOpen(false);
        setConfig({});
    };

    const ConfirmationModalComponent = () => (
        <ConfirmationModal
            isOpen={isOpen}
            onClose={hideConfirmation}
            onConfirm={async () => {
                if (config.onConfirm) {
                    await config.onConfirm();
                }
                hideConfirmation();
            }}
            {...config}
        />
    );

    return {
        showConfirmation,
        hideConfirmation,
        ConfirmationModal: ConfirmationModalComponent,
    };
}
