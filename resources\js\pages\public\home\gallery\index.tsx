import React from "react";
import { ArrowRight } from "lucide-react";
import Heading from "../../common/heading/Heading";
import { GalleryData } from "@/types";
import { Link } from "@inertiajs/react";

interface GalleryProps {
  galleryImages: { data: GalleryData[] };
}

const Gallery: React.FC<GalleryProps> = ({ galleryImages: { data } }) => {
  return (
    <section className="pt-20 pb-20">
      <Heading subtitle="GALLERY" title="Campus Life" />
      <p className="text-center max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-gray-700 mb-8">NIST Campus Life is full of joy and eventfulness. There are always somethings happening in our campus. Catch a glimse of our campus life here.</p>

      <div className="campus flex flex-col items-center gap-8">
        {/* Grid layout with padding on mobile */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl px-4 sm:px-6 lg:px-0">
          {data.map((item) => (
            <div
              key={item.id}
              className="relative overflow-hidden rounded-xl shadow-md cursor-pointer"
            >
              <img
                src={item.image}
                alt={item.title ?? `gallery-${item.id}`}
                className="w-full h-64 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          ))}
        </div>

        {/* Button (use Link directly) */}
        <Link
          href="/campus-life"
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full font-semibold transition-colors duration-300 shadow-md"
        >
          See More
          <ArrowRight size={16} className="w-4 h-4" />
        </Link>
      </div>
    </section>
  );
};

export default Gallery;
