<?php

namespace App\Http\Controllers;

use App\Models\GoverningBody;
use Illuminate\Http\Request;

class GoverningBodyStoreController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        //
         $data = $request->validate([
            'name' => 'required',
            'designation' => 'required',
            'display_order' => 'required|integer|min:0',
            'profile_URL' => 'nullable|url',
            'profile_image' => 'required|image',
        ]);

        if($request->hasFile('profile_image')){
            $data['profile_image'] = storeSanitizedFile($request->file('profile_image'), 'uploads/governingbody');
        }

        GoverningBody::create($data);

        return redirect(route('admin.governingbody.index'))->with('success', 'Entry Added successfully');

    }
}
