<?php

namespace App\Http\Controllers\News;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\EditorUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class NewsUpdateController extends Controller
{
    public function __invoke(Request $request, News $news)
    {
        // status-only toggle
        if ($request->has('active_status') && count($request->all()) === 1) {
            $validated = $request->validate([
                'active_status' => 'required|in:active,inactive',
            ]);

            $news->update(['active_status' => $validated['active_status']]);
            return back()->with('success', 'Status updated');
        }

        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'content' => ['nullable', 'string'],
            'image' => ['nullable', 'image', 'max:5120'],
            'meta_tag_title' => ['nullable', 'string', 'max:255'],
            'meta_tag_description' => ['nullable', 'string', 'max:255'],
            'excerpt' => ['nullable', 'string'],
            'draft_token' => ['nullable', 'uuid'],
            'published_at' => ['required', 'date'],
            'active_status' => ['required', 'in:active,inactive'],
        ]);
        
        $validated['content'] = str_replace(url('/'), '', $validated['content'] );


        return DB::transaction(function () use ($request, $news, $validated) {
            // Featured image (optional)
            if ($request->hasFile('image')) {
                if ($news->image && Storage::disk('public')->exists($news->image)) {
                    Storage::disk('public')->delete($news->image);
                }
                $news->image = $request->file('image')->store('uploads/news', 'public');
            }

            // Save main fields
            $news->fill([
                'title' => $validated['title'],
                'content' => $validated['content'] ?? '',
                'meta_tag_title' => $validated['meta_tag_title'] ?? null,
                'meta_tag_description' => $validated['meta_tag_description'] ?? null,
                'excerpt' => $validated['excerpt'] ?? null,
                'published_at' => $validated['published_at'],
                'active_status' => $validated['active_status'],
            ])->save();

            // Parse image paths from HTML (returns storage-relative: "uploads/…")
            $pathsInContent = $this->extractStoragePathsFromHtml($news->content);
            $pathsInContent = array_values(array_unique($pathsInContent));

            // Track old & new IDs for sync/cleanup
            $previousIds = $news->editorUploads()->pluck('editor_uploads.id')->all();
            $attachIds   = [];

            // For each path in content, pick ONE upload row to represent it:
            foreach ($pathsInContent as $path) {
                $chosen = EditorUpload::query()
                    ->where('path', $path)
                    // prefer the one with this draft_token if available
                    ->orderByRaw(
                        '(CASE WHEN draft_token = ? THEN 0 ELSE 1 END)',
                        [$validated['draft_token'] ?? '___no_token___']
                    )
                    // newest first as a tie-breaker
                    ->orderByDesc('id')
                    ->first();

                if (!$chosen) {
                    // If no DB row exists for this path, skip safely
                    continue;
                }

                // Clear draft token now that it's attached to a real model
                if (!empty($validated['draft_token']) && $chosen->draft_token === $validated['draft_token']) {
                    $chosen->update(['draft_token' => null]);
                }

                $attachIds[] = $chosen->id;

                // Optional: delete duplicate DB rows for the same path that are unclaimed & unused
                EditorUpload::where('path', $path)
                    ->where('id', '!=', $chosen->id)
                    ->whereNull('draft_token')
                    ->whereDoesntHave('news')              // update these if you add more models
                    ->whereDoesntHave('admissionPosts')    // idem
                    ->whereDoesntHave('facultyProfiles')   // idem
                    ->delete();
            }

            // Make the pivot match exactly what the content references now
            $news->editorUploads()->sync($attachIds);

            // Anything we removed from this model:
            $removed = array_diff($previousIds, $attachIds);

            if (!empty($removed)) {
                // Delete files/rows that are no longer used anywhere
                // i.e., no pivot rows exist for those upload IDs
                $stillUsedIds = DB::table('editor_uploadables')
                    ->whereIn('editor_upload_id', $removed)
                    ->pluck('editor_upload_id')
                    ->unique()
                    ->all();

                $orphans = array_diff($removed, $stillUsedIds);

                if (!empty($orphans)) {
                    $toDelete = EditorUpload::whereIn('id', $orphans)->get();
                    foreach ($toDelete as $upload) {
                        if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                            Storage::disk('public')->delete($upload->path);
                        }
                        $upload->delete();
                    }
                }
            }

            return redirect()->route('admin.news.index')
                ->with('success', 'News updated successfully');
        });
    }

    private function extractStoragePathsFromHtml(?string $html): array
    {
        if (!$html) return [];

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\']/', $html, $m);
        $urls = $m[1] ?? [];

        return collect($urls)
            ->map(fn ($url) => parse_url($url, PHP_URL_PATH))
            ->filter(fn ($path) => is_string($path) && str_starts_with($path, '/storage/'))
            ->map(fn ($path) => ltrim(substr($path, strlen('/storage/')), '/')) // "/storage/foo" → "foo"
            ->values()
            ->all();
    }
}
