<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Resources\NoticeboardResource;

class NoticeboardIndexController extends Controller
{
    /**
     * Handle the incoming request.
     */
    
    public function __invoke(Request $request)
    {
        $noticeboard = Noticeboard::latest();

        if ($request->has('search') && $request->search !== null) {
            $noticeboard->whereAny(['title', 'category'], 'like', '%' . $request->search . '%');
        }

        $noticeboard = $noticeboard->paginate(5);

        return Inertia::render('admin/noticeboard/index', [
            'noticeboardData'=> NoticeboardResource::collection($noticeboard),
        ]);
    }
}
