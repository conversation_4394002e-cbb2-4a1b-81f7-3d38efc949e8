import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type Role } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';

// Shadcn/UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface EditRoleProps {
    role: Role;
}

// Breadcrumbs for the AppLayout
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Access Control',
        href: '/admin/access-control',
    },
    {
        title: 'Edit Role',
        href: '#',
    },
];

export default function EditRole({ role }: EditRoleProps) {
    const { data, setData, put, processing, errors } = useForm({
        name: role.name || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.access-control.roles.update', role.id));
    };

    const isCoreRole = ['super_admin', 'admin', 'editor'].includes(role.name);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit Role - ${role.name}`} />
            <div className="flex h-full flex-1 flex-col gap-4 p-4 lg:p-6">
                <Card className="mx-auto w-full max-w-2xl">
                    <CardHeader>
                        <CardTitle>Edit Role: {role.name}</CardTitle>
                        <CardDescription>
                            {isCoreRole 
                                ? 'This is a core system role. Editing is restricted to maintain system integrity.'
                                : 'Update the role name. Be careful when changing role names as it may affect existing users.'
                            }
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid gap-2">
                                <Label htmlFor="name">Role Name</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., moderator, content-manager, viewer"
                                    required
                                    disabled={isCoreRole}
                                />
                                {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                <p className="text-sm text-muted-foreground">
                                    {isCoreRole 
                                        ? 'Core system roles cannot be renamed to maintain system stability.'
                                        : 'Use descriptive names like "moderator", "content-manager", or "viewer".'
                                    }
                                </p>
                            </div>

                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" asChild>
                                    <Link href={route('admin.access-control.index', { tab: 'roles' })}>
                                        Cancel
                                    </Link>
                                </Button>
                                <Button type="submit" disabled={processing || isCoreRole}>
                                    {processing ? 'Updating...' : 'Update Role'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
