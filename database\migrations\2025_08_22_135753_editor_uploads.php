<?php

// database/migrations/2025_08_22_000200_create_editor_uploads_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('editor_uploads', function (Blueprint $table) {
            $table->id();
            $table->string('path'); // file path
            $table->string('filename'); // sanitized filename
            $table->string('draft_token')->nullable()->index(); // temp identifier
            $table->foreignId('news_id')->nullable()->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('editor_uploads');
    }
};
