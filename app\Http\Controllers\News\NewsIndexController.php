<?php

namespace App\Http\Controllers\News;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;
use Inertia\Inertia;
use App\Http\Resources\NewsResource;


class NewsIndexController extends Controller
{
  public function __invoke(Request $request){
    $news = News::latest();

    if ($request->has('search') && $request->search !== null) {
        $news->where('title', 'like', '%' . $request->search . '%');
    }

    $news = $news->paginate(5);

    return Inertia::render('admin/news/index',[
        'news'=> NewsResource::collection($news),
    ]);
  }

}
