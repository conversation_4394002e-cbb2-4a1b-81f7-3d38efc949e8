import React from 'react';
import { Link } from '@inertiajs/react'; // Using Inertia's Link for SPA navigation

// Define the type for a course for type safety
interface Course {
  id: number;
  title: string;
  cover: string;
  items: string[];
  link: string; // Add a direct link property for cleaner navigation
}

// A simple placeholder for your Heading component
const Heading = ({ subtitle, title }: { subtitle: string; title:string }) => (
    <div className="text-center mb-6">
        <h3 className="text-lg font-medium uppercase text-orange-500">{subtitle}</h3>
        <h2 className="text-4xl font-bold text-gray-800">{title}</h2>
    </div>
);


const OfferedCourses = () => {
  const courses: Course[] = [
    {
      id: 1,
      title: 'B.Sc. in Computer Science and Engineering (CSE)',
      cover: '/images/cover/cse_cover.jpg',
      items: [
        'Strong foundation in computing',
        'Programming and algorithms',
        'Computer networks and security',
      ],
      link: '/courses-cse',
    },
    {
      id: 2,
      title: 'B.Sc. in Electronics and Communication Engineering (ECE)',
      cover: '/images/cover/ece_cover.jpg',
      items: [
        'Advanced communication systems',
        'Signal processing and microelectronics',
        'Embedded systems',
      ],
      link: '/courses-ece',
    },
    {
      id: 3,
      title: 'B.Sc. in Biochemistry & Molecular Biology (BMB)',
      cover: '/images/cover/bmb_cover.jpg',
      items: [
        'Biochemistry, research',
        'Hands-on training on laboratory techniques',
        'Biotechnology, healthcare',
      ],
      link: '/courses-bmb',
    },
    {
      id: 4,
      title: 'Bachelor of Business Administration (BBA)',
      cover: '/images/cover/bba_cover.jpg',
      items: [
        'Accounting and finance',
        'Marketing and management',
        'Human resources',
      ],
      link: '/courses-bba',
    },
  ];

  return (
    <section className="container mx-auto px-4 pt-24">
      <Heading subtitle="Our Program" title="Undergraduate Programs" />
      
      <div className="mx-auto mb-5 w-full text-center text-black md:w-3/5">
        <p className="text-lg font-medium">
          NIST is affiliated to the National University for conducting the B.Sc. (Professional) in CSE, ECE, BBA and B.Sc. (Hons.) in BMB programs.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {courses.map(course => (
          <div key={course.id} className="group flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-lg transition-transform duration-300 ease-in-out hover:-translate-y-2">
            <div className="overflow-hidden">
              <img 
                src={course.cover} 
                alt={course.title} 
                className="h-52 w-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-110 sm:h-64" 
              />
            </div>
            
            <div className="flex flex-grow flex-col p-4">
              <h3 className="text-xl font-bold text-gray-800">
                {course.title}
              </h3>
              
              <ul className="flex-grow py-4 pl-4 text-gray-600">
                {course.items.slice(0, 3).map((item, index) => (
                  <li key={index} className="mb-2 list-disc font-medium leading-tight">
                    {item}
                  </li>
                ))}
              </ul>

              <Link 
                href={course.link}
                className="mt-auto block w-full rounded-md bg-green-600 px-4 py-2.5 text-center text-base font-semibold text-white transition-colors duration-300 ease-in-out hover:bg-green-700 sm:text-sm"
              >
                DETAILS
              </Link>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default OfferedCourses;