<?php

namespace App\Http\Controllers;

use App\Http\Resources\GalleryResource;
use App\Http\Resources\FileUploadResource;
use App\Http\Resources\ImageSliderResource;
use App\Models\Gallery;
use App\Models\FileUpload;
use App\Models\ImageSlider;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FileUploadController extends Controller
{
    public function index(Request $request)
    {
        $activeTab = $request->get('tab', 'gallery');

        $galleries = Gallery::latest();
        if ($request->has('gallery_search') && $request->gallery_search !== null) {
            $galleries->where('title', 'like', '%' . $request->gallery_search . '%')
                     ->orWhere('category', 'like', '%' . $request->gallery_search . '%');
        }
        $galleries = $galleries->paginate(10);

        $fileUploads = FileUpload::latest();
        if ($request->has('files_search') && $request->files_search !== null) {
            $fileUploads->where('title', 'like', '%' . $request->files_search . '%')
                        ->orWhere('category', 'like', '%' . $request->files_search . '%');
        }
        $fileUploads = $fileUploads->paginate(10);

        // Get image sliders with search functionality
        $imageSliders = ImageSlider::ordered();
        if ($request->has('slider_search') && $request->slider_search !== null) {
            $imageSliders->where('title', 'like', '%' . $request->slider_search . '%')
                        ->orWhere('heading', 'like', '%' . $request->slider_search . '%');
        }
        $imageSliders = ImageSliderResource::collection($imageSliders->paginate(5));

        return Inertia::render('admin/file-upload/index', [
            'galleries' => GalleryResource::collection($galleries),
            'fileUploads' => FileUploadResource::collection($fileUploads),
            'imageSliders' => $imageSliders,
            'activeTab' => $activeTab,
        ]);
    }
}
