<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanupOrphanEditorUploadables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * Example: php artisan cleanup:editor-uploadables
     */
    protected $signature = 'cleanup:editor-uploadables';

    /**
     * The console command description.
     */
    protected $description = 'Remove orphaned rows from editor_uploadables table where related model no longer exists';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting orphan cleanup for editor_uploadables...');

        $count = 0;

        // Get all pivot rows
        $rows = DB::table('editor_uploadables')->get();

        foreach ($rows as $row) {
            $modelClass = $row->uploadable_type;

            if (!class_exists($modelClass)) {
                // Model class does not exist → delete pivot
                DB::table('editor_uploadables')->where('id', $row->id)->delete();
                $count++;
                continue;
            }

            $exists = $modelClass::where('id', $row->uploadable_id)->exists();

            if (!$exists) {
                DB::table('editor_uploadables')->where('id', $row->id)->delete();
                $count++;
            }
        }

        $this->info("Cleanup complete. Removed {$count} orphaned pivot rows.");
        return 0;
    }
}