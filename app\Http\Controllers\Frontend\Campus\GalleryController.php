<?php

namespace App\Http\Controllers\Frontend\Campus;

use App\Http\Controllers\Controller;
use App\Http\Resources\GalleryResource;
use App\Models\Gallery;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GalleryController extends Controller
{
    public function index(Request $request)
    {
        $category = $request->get('category', 'all_activity');

        // Get galleries based on category, latest first
        $query = Gallery::orderBy('created_at', 'desc');

        if ($category !== 'all_activity') {
            $query->where('category', $category);
        }

        $galleries = $query->paginate(21);

        // Debug: Log the data
        \Log::info('Campus Life Galleries:', [
            'category' => $category,
            'count' => $galleries->count(),
            'galleries' => $galleries->toArray()
        ]);

        return Inertia::render('public/campus/campus-life/index', [
            'galleries' => GalleryResource::collection($galleries),
            'currentCategory' => $category,
        ]);
    }

    public function show(string $id)
    {
        $gallery = Gallery::findOrFail($id);

        return Inertia::render('public/campus/campus-life/show', [
            'gallery' => new GalleryResource($gallery),
        ]);
    }
}
