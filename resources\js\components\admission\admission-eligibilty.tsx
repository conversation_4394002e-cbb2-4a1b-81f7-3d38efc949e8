import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const eligibilityData = [
  {
    program: 'B.Sc. in Computer Science & Engineering (CSE)',
    criteria: ['Minimum GPA of 2.5 in both SSC and HSC.', 'Science background with Math and Physics is mandatory.', 'Combined GPA of 6.0 or higher for waiver eligibility.'],
  },
  {
    program: 'Bachelor of Business Administration (BBA)',
    criteria: ['Minimum GPA of 2.5 in both SSC and HSC from any group.', 'Math or Accounting background is preferred.', 'Minimum "B" grade in English.'],
  },
  {
    program: 'B.Sc. in Electronics & Communication Engineering (ECE)',
    criteria: ['Minimum GPA of 2.5 in both SSC and HSC.', 'Science background with Math and Physics is mandatory.', 'Excellent problem-solving skills.'],
  },
  {
    program: 'B.Sc. in Biochemistry & Molecular Biology (BMB)',
    criteria: ['SSC or Equivalent with Minimum GPA 2.75 from Science Group', 'HSC or Equivalent with Minimum GPA 2.5 from Science Group', 'Minimum GPA 2.5 in Chemistry/Biology/Psychology.','Students from English medium have to complete in “O” level minimum 3B grades out of minimum 04 subjects and in “A” level minimum 1B grade out of minimum 02 subjects.'],
  },
];



const EligibilityAccordion = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16">
      <div className="container mx-auto max-w-4xl px-4">
        <h2 className="text-center text-3xl font-bold text-gray-800">Eligibility Criteria</h2>
        <div className="mt-8 space-y-4">
          {eligibilityData.map((item, index) => (
            <div key={index} className="rounded-lg border border-gray-200 bg-white">
              <button
                onClick={() => handleToggle(index)}
                className="flex w-full items-center justify-between p-6 text-left text-xl font-semibold text-gray-800"
              >
                <span>{item.program}</span>
                {openIndex === index ? <ChevronUp /> : <ChevronDown />}
              </button>
              <div className={`overflow-hidden transition-all duration-500 ease-in-out ${openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                <div className="border-t border-gray-200 px-6 pb-6 pt-4">
                  <ul className="list-disc space-y-2 pl-5 text-gray-600">
                    {item.criteria.map((c, i) => <li key={i}>{c}</li>)}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default EligibilityAccordion;