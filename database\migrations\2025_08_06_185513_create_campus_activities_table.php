<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campus_activities', function (Blueprint $table) {
            $table->id();
            $table->string('slug');
            $table->string('caption');
            $table->string('category');
            $table->text('description')->nullable();
            $table->string('image');
            $table->date('published_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campus_activities');
    }
};
