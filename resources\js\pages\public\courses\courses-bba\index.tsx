import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Back from '../../common/back/Back';
import { FaDownload } from 'react-icons/fa';

const CourseBbaPage = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const accordionData = [
    {
      id: 0,
      title: 'Entry Requirements',
      content: (
        <ul className="list-[square] space-y-2 pl-5">
          <li>SSC or Equivalent with Minimum GPA 3 from any Group.</li>
          <li>HSC or Equivalent with Minimum GPA 2.5 from any Group.</li>
          <li>Students from English medium must complete “O” level with minimum 3B grades out of 4 subjects and “A” level with minimum 1B grade out of 2 subjects.</li>
        </ul>
      ),
    },
    {
      id: 1,
      title: 'Grading System',
      content: (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse bg-[#E9F7FE] text-center text-[#004422]">
            <thead>
              <tr className="bg-[#B7DA9B]">
                <th className="border border-black p-2">Number Grade</th>
                <th className="border border-black p-2">Letter Grade</th>
                <th className="border border-black p-2">Grade Point</th>
                <th className="border border-black p-2">Remarks</th>
              </tr>
            </thead>
            <tbody>
              <tr><td className="border p-2">80 - 100</td><td className="border p-2">A+</td><td className="border p-2">4.00</td><td className="border p-2">Outstanding</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">75 - 79</td><td className="border p-2">A</td><td className="border p-2">3.75</td><td className="border p-2">Excellent</td></tr>
              <tr><td className="border p-2">70 - 74</td><td className="border p-2">A-</td><td className="border p-2">3.50</td><td className="border p-2">Very Good</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">65 - 69</td><td className="border p-2">B+</td><td className="border p-2">3.25</td><td className="border p-2">Good</td></tr>
              <tr><td className="border p-2">60 - 64</td><td className="border p-2">B</td><td className="border p-2">3.00</td><td className="border p-2">Satisfactory</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">55 - 59</td><td className="border p-2">B-</td><td className="border p-2">2.75</td><td className="border p-2">Above Average</td></tr>
              <tr><td className="border p-2">50 - 54</td><td className="border p-2">C+</td><td className="border p-2">2.50</td><td className="border p-2">Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">45 - 49</td><td className="border p-2">C</td><td className="border p-2">2.25</td><td className="border p-2">Below Average</td></tr>
              <tr><td className="border p-2">40 - 44</td><td className="border p-2">D</td><td className="border p-2">2.00</td><td className="border p-2">Below Average</td></tr>
              <tr className="bg-[#B7DA9B]"><td className="border p-2">Less Than 40</td><td className="border p-2">F</td><td className="border p-2">0.00</td><td className="border p-2">Fail</td></tr>
            </tbody>
          </table>
        </div>
      ),
    },
    {
      id: 2,
      title: 'Fee Structure',
      content: <img src="/images/fees/BBA_FEES_2025.jpg" alt="BBA Fee Structure" />,
    },
    {
      id: 3,
      title: 'Course Curriculum',
      content: (
        <iframe
          src="https://docs.google.com/gview?url=https://nist.edu.bd/contents/uploads/syllabus/BBA-Course-Curriculam.pdf&embedded=true"
          className="h-[800px] w-full"
        ></iframe>
      ),
    },
  ];

  const bbaFeatures = [
    'Skilled, expert faculty with university-level teaching standards.',
    'Dedicated modern labs, AC classroom.',
    'Fully Wi-Fi supported Campus.',
    'Outstanding academic performance by NIST students.',
    'Club facilities.',
    'Central library with extensive resources.',
    'Active participation in national and international competition, contests.',
    'Consultation center for top-performing students.',
    'Regular seminars, workshops, and webinars by industry professionals.',
  ];

  return (
    <>
      <Head title="Department of BBA" />
      <Back title="Department of Business Administration" />

      {/* Intro Section */}
      <section className="container mx-auto px-4 py-12">
        <h3 className="text-lg font-medium text-center text-orange-500">WELLCOME</h3>
        <h2 className="text-4xl py-4 text-center uppercase font-bold text-[#000F38]">Department of Business Administration</h2>
        <p className="text-justify mt-4 text-xl font-medium text-gray-600">
          Enter the dynamic world of business and leadership with the Department of Business Administration at NIST. Our program is designed to equip future leaders with the knowledge, skills, and ethical values needed to make a meaningful impact in the business world. Learn, innovate, and drive a prosperous Bangladesh and a better global economy. We are dedicated to nurturing graduates who can lead with integrity and transform industries for the greater good.
        </p>
      </section>

      {/* Why Study BBA Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row">
        <div className="basis-full md:basis-[56%]">
          <h2 className="text-4xl font-bold text-[#000F38]">Why Study BBA at NIST?</h2>
          <p className="my-4 text-justify text-gray-600">
            The Bachelor of Business Administration (BBA) program aims to cultivate a new generation of business professionals who are not only skilled and innovative but also deeply committed to ethical practices and sustainable development. By integrating rigorous academic training with hands-on experience, the program equips students with the knowledge and tools necessary to address the challenges and opportunities unique to Bangladesh's rapidly evolving economic landscape.
          </p>
          <div className="mt-6 space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-orange-500">MISSION</h3>
              <p className="text-justify text-gray-600">
                To provide a transformative education in business administration that empowers students with essential knowledge, skills, and values. Our mission is to nurture future leaders who can make impactful contributions to the economy and society, aligning with the unique needs and opportunities of Bangladesh.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-orange-500">VISION</h3>
              <p className="text-justify text-gray-600">
                To become a leading BBA program in Bangladesh, recognized for producing globally competitive, socially responsible, and entrepreneurial graduates who drive positive change in business and society.
              </p>
            </div>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/bba/bba-1.jpg" alt="BBA Program" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      {/* Facilities Section */}
      <section className="mx-auto flex w-[90%] flex-col items-center justify-between gap-8 py-10 md:flex-row-reverse">
        <div className="basis-full md:basis-[56%]">
          <h3 className="text-base font-semibold text-orange-500">WHY CHOOSE NIST?</h3>
          <h2 className="my-2.5 max-w-xl text-4xl font-bold text-[#000F38]">Facilities at NIST</h2>
          <div className="mt-4 rounded-lg bg-gray-50 p-4">
            <ul className="space-y-1.5">
              {bbaFeatures.map((feature, index) => (
                <li key={index} className="border-l-4 border-[#07944a] bg-white px-2 py-1.5 text-base font-medium text-[#333] shadow-sm">
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="basis-full md:basis-2/5">
          <img src="/images/courses/bba/bba-2.jpg" alt="NIST Facilities" className="w-full rounded-lg shadow-lg" />
        </div>
      </section>

      {/* Accordion Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-5xl px-4 md:w-[95%]">
          <h2 className="mb-10 text-center text-3xl font-bold uppercase text-[#000F38]">BBA Course Information</h2>
          <div className="space-y-2.5">
            {accordionData.map((item) => (
              <div key={item.id} className="overflow-hidden rounded-md bg-[#2c3e50] text-white">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="flex w-full cursor-pointer select-none items-center justify-between p-4 text-left text-lg font-medium transition-colors hover:bg-[#34495e] md:text-base"
                  aria-expanded={activeIndex === item.id}
                >
                  <span>{item.title}</span>
                  <span>{activeIndex === item.id ? <ChevronDown /> : <ChevronRight />}</span>
                </button>
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${activeIndex === item.id ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0'}`}>
                  <div className="bg-[#ecf0f1] p-4 text-[#2c3e50]">
                    {item.content}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
    </>
  );
};

export default CourseBbaPage;
