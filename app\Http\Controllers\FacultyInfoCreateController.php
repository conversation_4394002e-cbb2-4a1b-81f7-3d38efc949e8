<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;

class FacultyInfoCreateController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        //
        $draftToken = Str::uuid()->toString();
        return inertia('admin/faculty/create', [
            'draftToken' => $draftToken,
        ]);

    }
}
