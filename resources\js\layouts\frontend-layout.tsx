// resources/js/Layouts/FrontendLayout.tsx
import Navmenu from "@/pages/public/common/header/Navmenu";
import Footer from "@/pages/public/common/footer/Footer";
import { type ReactNode } from 'react';
import { FloatingWhatsApp } from 'react-floating-whatsapp';

export default function FrontendLayout({ children }: { children: ReactNode }) {
  return (
    <div>
      <Navmenu />
      <main>{children}</main>
      <Footer />
      <FloatingWhatsApp
          phoneNumber="+*************"
          accountName="NIST"
          avatar="/images/NIST-logo.png"
          statusMessage="We are online"
          darkMode={false}
          allowClickAway={true}
          notificationDelay={1000}
          notificationSound={true}
        />
    </div>
  );
}
