<?php


namespace App\Console\Commands;

use App\Models\EditorUpload;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupOrphanEditorUploads extends Command
{
    protected $signature = 'cleanup:editor-uploads {--dry-run}';
    protected $description = 'Remove orphaned editor_uploads (no related editor_uploadables) and delete orphaned files from storage';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $countDb = 0;
        $countFs = 0;

        // --- Phase 1: DB orphans ---
        $orphans = EditorUpload::whereDoesntHave('admissionPosts')
            ->whereDoesntHave('news')
            ->whereDoesntHave('facultyProfiles')
            ->get();

        foreach ($orphans as $upload) {
            $filePath = $upload->path; // e.g. uploads/post-images/capture-1234.png

            if ($dryRun) {
                $this->info("[Dry Run] Would delete DB orphan: {$filePath}");
                continue;
            }

            if ($filePath && Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                $this->info("Deleted DB orphan file: {$filePath}");
            }

            $upload->delete();
            $countDb++;
        }

        // --- Phase 2: FS orphans (files without DB entry) ---
        $allDbFiles = EditorUpload::pluck('path')->filter()->toArray();

        // scan all files in the uploads/post-images directory
        $allFsFiles = Storage::disk('public')->allFiles('uploads/post-images');

        foreach ($allFsFiles as $fsFile) {
            if (!in_array($fsFile, $allDbFiles)) {
                if ($dryRun) {
                    $this->info("[Dry Run] Would delete FS orphan: {$fsFile}");
                    continue;
                }

                Storage::disk('public')->delete($fsFile);
                $this->info("Deleted FS orphan file: {$fsFile}");
                $countFs++;
            }
        }

        // Summary
        if (!$dryRun) {
            $this->info("Deleted {$countDb} DB orphan uploads and {$countFs} FS orphan files.");
        } else {
            $this->info("Dry run complete. No files or records deleted.");
        }
    }
}
