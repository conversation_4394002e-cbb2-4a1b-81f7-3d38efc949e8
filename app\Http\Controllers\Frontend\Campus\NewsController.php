<?php

namespace App\Http\Controllers\Frontend\Campus;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Http\Resources\NewsResource;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NewsController extends Controller
{
    //
    public function index()
    {
        $news = NewsResource::collection(News::latest()->paginate(5));
        return Inertia::render('public/news/index', [
            'news' => $news,
        ]);
    }


    public function show($id, $slug)
    {
        $newsDataItem = News::where('id', $id)->where('slug', $slug)->first();
        $newsData = NewsResource::make($newsDataItem);
        return Inertia::render('public/news/show', [
            'newsDataItem' => $newsData,
        ]);
    }
}
