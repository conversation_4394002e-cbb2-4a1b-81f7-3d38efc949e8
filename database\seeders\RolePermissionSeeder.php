<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spa<PERSON>\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User management
            'users-view',
            'users-create',
            'users-edit',
            'users-delete',
            'users-assign-roles',
            'users-assign-permissions',
            
            // Role management
            'roles-view',
            'roles-create',
            'roles-edit',
            'roles-delete',
            'roles-assign-permissions',
            
            // Permission management
            'permissions-view',
            'permissions-create',
            'permissions-edit',
            'permissions-delete',
            
            // News management
            'news-view',
            'news-create',
            'news-edit',
            'news-delete',
            'news-publish',
            
            // Noticeboard management
            'noticeboard-view',
            'noticeboard-create',
            'noticeboard-edit',
            'noticeboard-delete',
            'noticeboard-publish',
            
            // Faculty management
            'faculty-view',
            'faculty-create',
            'faculty-edit',
            'faculty-delete',
            
            // Governing body management
            'governing-body-view',
            'governing-body-create',
            'governing-body-edit',
            'governing-body-delete',
            
            // Schedule management
            'schedule-view',
            'schedule-create',
            'schedule-edit',
            'schedule-delete',
            
            // Admission management
            'admission-view',
            'admission-create',
            'admission-edit',
            'admission-delete',
            
            // File upload management
            'files-view',
            'files-upload',
            'files-edit',
            'files-delete',
            
            // Dashboard access
            'dashboard-access',
            
            // System settings
            'settings-view',
            'settings-edit',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Create roles
        $superAdmin = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
        $admin = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $editor = Role::firstOrCreate(['name' => 'editor', 'guard_name' => 'web']);

        // Assign permissions to roles
        
        // Super Admin gets all permissions
        $superAdmin->syncPermissions(Permission::all());

        // Admin gets most permissions except user role/permission management
        $adminPermissions = Permission::whereNotIn('name', [
            'users-assign-roles',
            'users-assign-permissions',
            'roles-create',
            'roles-edit',
            'roles-delete',
            'roles-assign-permissions',
            'permissions-create',
            'permissions-edit',
            'permissions-delete',
        ])->get();
        $admin->syncPermissions($adminPermissions);

        // Editor gets content management permissions
        $editorPermissions = Permission::whereIn('name', [
            'dashboard-access',
            'news-view',
            'news-create',
            'news-edit',
            'noticeboard-view',
            'noticeboard-create',
            'noticeboard-edit',
            'faculty-view',
            'faculty-edit',
            'schedule-view',
            'schedule-edit',
            'admission-view',
            'admission-edit',
            'files-view',
            'files-upload',
            'files-edit',
        ])->get();
        $editor->syncPermissions($editorPermissions);

        $this->command->info('Roles and permissions seeded successfully!');
    }
}
