import { Placeholder } from '@tiptap/extension-placeholder';
import { TextAlign } from '@tiptap/extension-text-align';
import { TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import type { Content, Editor, UseEditorOptions } from '@tiptap/react';
import { useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import * as React from 'react';

import { cn } from '@/lib/utils';
import { route, usePage } from '@inertiajs/react';
import { toast } from 'sonner';
import {
    CodeBlockLowlight,
    Color,
    FileHandler,
    HorizontalRule,
    Image,
    Link,
    ResetMarksOnEnter,
    Selection,
    UnsetAllMarks,
    Video,
} from '../extensions';
import { useThrottle } from '../hooks/use-throttle';
import { fileToBase64, getOutput, randomId } from '../utils';


const customImageUploader = (cfg: { csrf: any; draftToken: any; uploadUrl: any; deleteUrl?: (id: number) => string; }) => async (file: File) => {
  const form = new FormData()
  form.append('image', file)
  form.append('draft_token', cfg.draftToken)

  const res = await fetch(cfg.uploadUrl, {
    method: 'POST',
    headers: { 'X-CSRF-TOKEN': cfg.csrf },
    body: form,
    credentials: 'include',
  })
  if (!res.ok) throw new Error('Upload failed')
  const data = await res.json()
  // return object with id & src so editor can replace blob with final URL
  return { id: data.id, src: data.url }
}

export interface UseMinimalTiptapEditorProps extends UseEditorOptions {
    value?: Content;
    output?: 'html' | 'json' | 'text';
    placeholder?: string;
    editorClassName?: string;
    throttleDelay?: number;
    onUpdate?: (content: Content) => void;
    onBlur?: (content: Content) => void;
    uploadConfig?: {
    csrf: string
    draftToken: string
    uploadUrl: string
    deleteUrl: (id: number) => string
  }
}

const createExtensions = (placeholder: string, uploadConfig: { csrf: string; draftToken: string; uploadUrl: string; deleteUrl: (id: number) => string; } | undefined) => [
    StarterKit.configure({
        horizontalRule: false,
        codeBlock: false,
        paragraph: { HTMLAttributes: { class: 'text-node' } },
        heading: { HTMLAttributes: { class: 'heading-node' } },
        blockquote: { HTMLAttributes: { class: 'block-node' } },
        bulletList: { HTMLAttributes: { class: 'list-node' } },
        orderedList: { HTMLAttributes: { class: 'list-node' } },
        code: { HTMLAttributes: { class: 'inline', spellcheck: 'false' } },
        dropcursor: { width: 2, class: 'ProseMirror-dropcursor border' },
    }),
    Link,
    Underline,
    Image.configure({
        allowedMimeTypes: ['image/*'],
        maxFileSize: 5 * 1024 * 1024,
        allowBase64: true,
        /*
    async (file) => {
      // NOTE: This is a fake upload function. Replace this with your own upload logic.
      // This function should return the uploaded image URL.

      // wait 3s to simulate upload
      await new Promise((resolve) => setTimeout(resolve, 3000))

      const src = await fileToBase64(file)

      // either return { id: string | number, src: string } or just src
      // return src;
      return { id: randomId(), src }
    }
    */
        uploadFn: uploadConfig ? customImageUploader(uploadConfig) : undefined,
        onToggle(editor, files, pos) {
            editor.commands.insertContentAt(
                pos,
                files.map((image) => {
                    const blobUrl = URL.createObjectURL(image);
                    const id = randomId();

                    return {
                        type: 'image',
                        attrs: {
                            id,
                            src: blobUrl,
                            alt: image.name,
                            title: image.name,
                            fileName: image.name,
                        },
                    };
                }),
            );
        },
        onImageRemoved({ id, src }) {
            fetch(uploadConfig.deleteUrl(id), {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': uploadConfig.csrf,
                },
            });
        },
        onValidationError(errors) {
            errors.forEach((error) => {
                toast.error('Image validation error', {
                    position: 'bottom-right',
                    description: error.reason,
                });
            });
        },
        onActionSuccess({ action }) {
            const mapping = {
                copyImage: 'Copy Image',
                copyLink: 'Copy Link',
                download: 'Download',
            };
            toast.success(mapping[action], {
                position: 'bottom-right',
                description: 'Image action success',
            });
        },
        onActionError(error, { action }) {
            const mapping = {
                copyImage: 'Copy Image',
                copyLink: 'Copy Link',
                download: 'Download',
            };
            toast.error(`Failed to ${mapping[action]}`, {
                position: 'bottom-right',
                description: error.message,
            });
        },
    }),
    FileHandler.configure({
        allowBase64: true,
        allowedMimeTypes: ['image/*'],
        maxFileSize: 5 * 1024 * 1024,
        onDrop: (editor, files, pos) => {
            files.forEach(async (file) => {
                const src = await fileToBase64(file);
                editor.commands.insertContentAt(pos, {
                    type: 'image',
                    attrs: { src },
                });
            });
        },
        onPaste: (editor, files) => {
            files.forEach(async (file) => {
                const src = await fileToBase64(file);
                editor.commands.insertContent({
                    type: 'image',
                    attrs: { src },
                });
            });
        },
        onValidationError: (errors) => {
            errors.forEach((error) => {
                toast.error('Image validation error', {
                    position: 'bottom-right',
                    description: error.reason,
                });
            });
        },
    }),
    Color,
    TextStyle,
    TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
        defaultAlignment: 'left',
    }),
    Selection,
    Typography,
    UnsetAllMarks,
    HorizontalRule,
    ResetMarksOnEnter,
    CodeBlockLowlight,
    Video.configure({
        allowedProviders: ['youtube', 'vimeo', 'dailymotion'],
        width: 560,
        height: 315,
    }),
    Placeholder.configure({ placeholder: () => placeholder }),
];

export const useMinimalTiptapEditor = ({
    value,
    output = 'html',
    placeholder = '',
    editorClassName,
    throttleDelay = 0,
    onUpdate,
    onBlur,
    uploadConfig,
    ...props
}: UseMinimalTiptapEditorProps) => {
    const throttledSetValue = useThrottle((value: Content) => onUpdate?.(value), throttleDelay);

    const handleUpdate = React.useCallback((editor: Editor) => throttledSetValue(getOutput(editor, output)), [output, throttledSetValue]);

    const handleCreate = React.useCallback(
        (editor: Editor) => {
            if (value && editor.isEmpty) {
                editor.commands.setContent(value);
            }
        },
        [value],
    );

    const handleBlur = React.useCallback((editor: Editor) => onBlur?.(getOutput(editor, output)), [output, onBlur]);

    const editor = useEditor({
        extensions: createExtensions(placeholder, uploadConfig),
        editorProps: {
            attributes: {
                autocomplete: 'off',
                autocorrect: 'off',
                autocapitalize: 'off',
                class: cn('focus:outline-hidden', editorClassName),
            },
        },
        onUpdate: ({ editor }) => handleUpdate(editor),
        onCreate: ({ editor }) => handleCreate(editor),
        onBlur: ({ editor }) => handleBlur(editor),
        ...props,
    });

    return editor;
};

export default useMinimalTiptapEditor;
