<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FileUploadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'category' => $this->category,
            'file' => $this->file ? asset('storage/' . $this->file) : null,
            'relative_url' => $this->file ? '/storage/' . $this->file : null,
            'absolute_url' => $this->file ? url('storage/' . $this->file) : null,
            'file_name' => $this->file ? basename($this->file) : null,
            'file_extension' => $this->file ? pathinfo($this->file, PATHINFO_EXTENSION) : null,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
