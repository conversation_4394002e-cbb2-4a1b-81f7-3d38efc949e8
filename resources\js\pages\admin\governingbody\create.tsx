import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Add Governing Body',
        href: 'governingbody/create',
    },
];

export default function GoverningBodyCreate() {
    const { data, setData, post, errors, processing } = useForm<{
        name: string;
        designation: string;
        display_order: number;
        profile_URL: string;
        profile_image: File | null;
    }>({
        name: '',
        designation: '',
        display_order: 1,
        profile_URL: '',
        profile_image: null,
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        post(route('admin.governingbody.store'));
    }

    const handleOrderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        // If the input is empty, set the state to 0 or an appropriate default.
        // Using 0 is a safe bet for a numeric field.
        if (value === '') {
            setData('display_order', 0);
            return;
        }

        // Parse the value as an integer with a base of 10.
        const numericValue = parseInt(value, 10);

        // Only update the state if the parsed value is a valid number.
        if (!isNaN(numericValue)) {
            setData('display_order', numericValue);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Governing Body" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Governing Body</div>

                        <Button>
                            <Link href="/admin/governingbody" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="name">Name</Label>
                                        <Input
                                            type="text"
                                            id="name"
                                            placeholder="Name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            aria-invalid={!!errors.name}
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="designation">Designation</Label>
                                        <Input
                                            type="text"
                                            id="designation"
                                            placeholder="Designation"
                                            value={data.designation}
                                            onChange={(e) => setData('designation', e.target.value)}
                                            aria-invalid={!!errors.designation}
                                        />
                                        <InputError message={errors.designation} />
                                    </div>

                                    <div>
                                        <Label htmlFor="profile_URL">Profile/Website LINK</Label>
                                        <Input
                                            type="text"
                                            id="profile_URL"
                                            placeholder="Profile/Website LINK"
                                            value={data.profile_URL}
                                            onChange={(e) => setData('profile_URL', e.target.value)}
                                            aria-invalid={!!errors.profile_URL}
                                        />
                                        <InputError message={errors.profile_URL} />
                                    </div>

                                    <div>
                                        <Label htmlFor="display_order">Display Order</Label>
                                        <Input
                                            type="number"
                                            id="display_order"
                                            placeholder="Display Order"
                                            value={data.display_order}
                                            onChange={handleOrderChange}
                                            aria-invalid={!!errors.display_order}
                                        />
                                    </div>
                                </div>

                                <div className="mt-4">
                                    <Label htmlFor="profile_image">Select Image</Label>
                                    <Input
                                        type="file"
                                        id="profile_image"
                                        onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                                setData('profile_image', file);
                                            }
                                        }}
                                        aria-invalid={!!errors.profile_image}
                                    />
                                    <InputError message={errors.profile_image} />
                                    {data.profile_image && (
                                        <>
                                            <img
                                                src={URL.createObjectURL(data.profile_image)}
                                                alt="Preview"
                                                className="mt-2 w-32 rounded-lg object-cover"
                                            />
                                            <div className="text-center text-sm">
                                                <p>
                                                    <strong className="text-neutral-12">File Name:</strong> {data.profile_image.name}
                                                </p>
                                                <p>
                                                    <strong className="text-neutral-12">File Size:</strong> {(data.profile_image.size / 1024).toFixed(2)} KB
                                                </p>
                                            </div>
                                        </>
                                    )}
                                </div>
                

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Governing Body</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
