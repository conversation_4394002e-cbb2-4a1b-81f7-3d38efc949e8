<?php

namespace App\Http\Controllers\Noticeboard;
use App\Http\Controllers\Controller;
use App\Models\Noticeboard;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class NoticeboardDestroyController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Noticeboard $noticeboard)
    {


        // THEN, delete the record from the database


        DB::transaction(function () use ($noticeboard) {

            // First, delete the associated files from storage if they exist
            if ($noticeboard->image) {
                Storage::disk("public")->delete($noticeboard->image);
            }
            if ($noticeboard->attachment) {
                Storage::disk("public")->delete($noticeboard->attachment);
            }
            // Collect related uploads
            $uploads = $noticeboard->editorUploads()->get();

            // Detach pivot first
            $noticeboard->editorUploads()->detach();

            // Delete the notice data itself
            $noticeboard->delete();

            // Cleanup orphaned uploads
            foreach ($uploads as $upload) {
                $stillUsed = $upload->admissionPosts()->exists() || $upload->news()->exists() || $upload->facultyProfiles()->exists() || $upload->noticeboards()->exists(); // check if used by any other model
                if (!$stillUsed) {
                    if ($upload->path && Storage::disk('public')->exists($upload->path)) {
                        Storage::disk('public')->delete($upload->path);
                    }
                    $upload->delete();
                }
            }
        });



        return redirect(route('admin.noticeboard.index'))->with('success', 'Notice deleted successfully');

    }
}



