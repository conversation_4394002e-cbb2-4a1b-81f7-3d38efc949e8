import React, { useState } from 'react'
import type { Editor } from '@tiptap/react'
import type { VariantProps } from 'class-variance-authority'
import type { toggleVariants } from '@/components/ui/toggle'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { ToolbarButton } from '../toolbar-button'
import { Play } from 'lucide-react'
import { getEmbedUrl } from '../../extensions/video/video'

interface VideoEditDialogProps extends VariantProps<typeof toggleVariants> {
  editor: Editor
}

export const VideoEditDialog: React.FC<VideoEditDialogProps> = ({
  editor,
  size,
  variant,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [videoUrl, setVideoUrl] = useState('')
  const [width, setWidth] = useState(560)
  const [height, setHeight] = useState(315)
  const [title, setTitle] = useState('')

  const handleSubmit = () => {
    const embedUrl = getEmbedUrl(videoUrl)
    if (embedUrl) {
      editor
        .chain()
        .focus()
        .setVideo({
          src: embedUrl,
          width,
          height,
        })
        .run()
      
      // Reset form
      setVideoUrl('')
      setWidth(560)
      setHeight(315)
      setTitle('')
      setIsOpen(false)
    } else {
      alert('Please enter a valid YouTube, Vimeo, or Dailymotion URL')
    }
  }

  const handleCancel = () => {
    setVideoUrl('')
    setWidth(560)
    setHeight(315)
    setTitle('')
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <ToolbarButton
          tooltip="Insert video"
          aria-label="Insert video"
          size={size}
          variant={variant}
        >
          <Play className="size-5" />
        </ToolbarButton>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Insert Video</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="video-url">Video URL</Label>
            <Input
              id="video-url"
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              placeholder="https://www.youtube.com/watch?v=..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Supports YouTube, Vimeo, and Dailymotion URLs
            </p>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="video-width">Width</Label>
              <Input
                id="video-width"
                type="number"
                value={width}
                onChange={(e) => setWidth(Number(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="video-height">Height</Label>
              <Input
                id="video-height"
                type="number"
                value={height}
                onChange={(e) => setHeight(Number(e.target.value))}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="video-title">Title (optional)</Label>
            <Input
              id="video-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Video title"
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              Insert Video
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
