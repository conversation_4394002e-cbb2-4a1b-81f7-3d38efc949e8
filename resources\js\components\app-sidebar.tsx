import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { BookOpen, LayoutGrid, Newspaper, NotebookTabs, School, FileUser, UsersRound, CalendarDays, Upload, Shield, Settings } from 'lucide-react';
import AppLogo from './app-logo';



const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/admin',
        icon: LayoutGrid,
    },
    {
        title: 'Noticeboard',
        href: '/admin/noticeboard',
        icon: NotebookTabs ,
    },
    {
        title: 'News',
        href: '/admin/news',
        icon: Newspaper,
    },
    {
        title: 'Admission',
        href: '/admin/admission',
        icon: School   ,
    },
    {
        title: 'Schedule Manager',
        href: '/admin/schedule',
        icon: CalendarDays ,
    },
    {
        title: 'Faculty Profile',
        href: '/admin/faculty',
        icon: FileUser   ,
    },
    {
        title: 'Governing Body',
        href: '/admin/governingbody',
        icon: UsersRound ,
    },
    {
        title: 'File Upload',
        href: '/admin/file-upload',
        icon: Upload ,
    },
    {
        title: 'Site Settings',
        href: '/admin/site-settings',
        icon: Settings ,
    },
    {
        title: 'Access Control List',
        href: '/admin/access-control',
        icon: Shield ,
    },
    
];

const footerNavItems: NavItem[] = [
    
    {
        title: 'Documentation',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    return (
    
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/admin" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
  
       
    );
}
