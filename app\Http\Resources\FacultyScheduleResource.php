<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FacultyScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->id,
            "faculty_name" => $this->faculty_name,
            "department" => $this->department,
            "scheduleEmbedLink" => $this->scheduleEmbedLink,
        ];
    }
}
