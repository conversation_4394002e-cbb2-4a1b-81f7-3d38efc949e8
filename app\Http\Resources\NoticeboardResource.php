<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NoticeboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    public function toArray(Request $request): array
    {
       
        return [
            "id" => $this->id,
            "title" => $this->title,
            "slug" => $this->slug,
            "category" => $this->category,
            "content" => $this->content,
            // Only generate a URL if an image path exists
            "image" => $this->image ? asset('storage/'.$this->image) : null,
            // Only generate a URL if an attachment path exists
            "attachment" => $this->attachment ? asset('storage/'.$this->attachment) : null,
            "created_at" => $this->created_at?->format('d M Y') ?? '',
            "published_at" => $this->published_at?->format('d M Y') ?? '',
            "active_status" => $this->active_status,
        ];
    }
}
