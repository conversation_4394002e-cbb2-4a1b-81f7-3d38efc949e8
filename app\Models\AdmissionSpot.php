<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AdmissionSpot extends Model
{
    use HasFactory;

    protected $fillable = [
        'campaign_name',
        'session',
        'program_name',
        'application_start_date',
        'application_end_date',
        'admission_test_date',
        'session_start_date',
        'active_status',
        'date_created',
    ];

    protected $casts = [
        'application_start_date' => 'date',
        'application_end_date' => 'date',
        'admission_test_date' => 'date',
        'session_start_date' => 'date',
        'date_created' => 'date',
    ];
}
