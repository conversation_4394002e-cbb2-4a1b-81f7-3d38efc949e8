import { Placeholder } from '@tiptap/extension-placeholder';
import { TextAlign } from '@tiptap/extension-text-align';
import { TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import type { Content, Editor, UseEditorOptions } from '@tiptap/react';
import { useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import * as React from 'react';

import { cn } from '@/lib/utils';
import axios from 'axios';
import { toast } from 'sonner';
import {
    CodeBlockLowlight,
    Color,
    FileHandler,
    HorizontalRule,
    Image,
    Link,
    ResetMarksOnEnter,
    Selection,
    UnsetAllMarks,
    Video,
} from '../extensions';
import { useThrottle } from '../hooks/use-throttle';
import { getOutput } from '../utils';

const customImageUploader = (cfg: { draftToken: string; uploadUrl: string }) => async (file: File, _editor: Editor) => {
    try {
        const form = new FormData();
        form.append('image', file);
        form.append('draft_token', cfg.draftToken);

        const res = await axios.post(cfg.uploadUrl, form, {
            headers: {
                Accept: 'application/json',
            },
            withCredentials: true, // 👈 send cookies/session
        });

        const data = res.data;
        if (!data.id || !data.url) {
            throw new Error('Invalid server response');
        }

        return { id: data.id, src: data.url };
    } catch (error) {
        console.error('Image upload error:', error);
        throw error;
    }
};

export interface UseMinimalTiptapEditorProps extends UseEditorOptions {
    value?: Content;
    output?: 'html' | 'json' | 'text';
    placeholder?: string;
    editorClassName?: string;
    throttleDelay?: number;
    onUpdate?: (content: Content) => void;
    onBlur?: (content: Content) => void;
    uploadConfig?: {
        draftToken: string;
        uploadUrl: string; // e.g. route('api.editor.uploads.store')
        deleteUrl: (id: number | string) => string; // e.g. (id)=>route('api.editor.uploads.destroy', id)
    };
}

const createExtensions = (placeholder: string, uploadConfig?: UseMinimalTiptapEditorProps['uploadConfig']) => [
    StarterKit.configure({
        horizontalRule: false,
        codeBlock: false,
        paragraph: { HTMLAttributes: { class: 'text-node' } },
        heading: { HTMLAttributes: { class: 'heading-node' } },
        blockquote: { HTMLAttributes: { class: 'block-node' } },
        bulletList: { HTMLAttributes: { class: 'list-node' } },
        orderedList: { HTMLAttributes: { class: 'list-node' } },
        code: { HTMLAttributes: { class: 'inline', spellcheck: 'false' } },
        dropcursor: { width: 2, class: 'ProseMirror-dropcursor border' },
    }),
    Link,
    Underline,
    Image.configure({
        allowedMimeTypes: ['image/*'],
        maxFileSize: 5 * 1024 * 1024,
        allowBase64: false, // IMPORTANT: we want real files → upload
        uploadFn: uploadConfig ? customImageUploader(uploadConfig) : undefined,

        // We now route UI file-pick + drag/paste to setImages → handled here.
        onImageRemoved: (attrs) => {
            // attempt server delete only if we have a numeric/id-like value
            const id = attrs?.id;
            if (!uploadConfig || id == null) return;

            const url = uploadConfig.deleteUrl(id);

            axios
                .delete(url, {
                    withCredentials: true, // Equivalent to 'credentials: include'
                })
                .catch((error) => {
                    console.error('Image delete failed:', error);
                });
        },

        onValidationError(errors) {
            errors.forEach((e) => {
                const errorMessages = {
                    type: 'Invalid file type. Please upload an image file.',
                    size: 'File size too large. Maximum size is 5MB.',
                    invalidBase64: 'Invalid image data.',
                    base64NotAllowed: 'Base64 images are not allowed.',
                };
                toast.error('Image validation error', {
                    position: 'bottom-right',
                    description: errorMessages[e.reason] || e.reason,
                });
            });
        },
        onActionSuccess({ action }) {
            const mapping = { copyImage: 'Copy Image', copyLink: 'Copy Link', download: 'Download' } as const;
            toast.success(mapping[action], {
                position: 'bottom-right',
                description: 'Image action success',
            });
        },
        onActionError(error, { action }) {
            const mapping = { copyImage: 'Copy Image', copyLink: 'Copy Link', download: 'Download' } as const;
            toast.error(`Failed to ${mapping[action]}`, {
                position: 'bottom-right',
                description: (error as Error).message,
            });
        },
    }),

    // IMPORTANT: FileHandler must pass File objects to setImages (not base64)
    FileHandler.configure({
        allowBase64: false,
        allowedMimeTypes: ['image/*'],
        maxFileSize: 5 * 1024 * 1024,
        onDrop: (editor, files, pos) => {
            if (!files.length) return;
            editor.commands.insertContentAt(
                pos,
                [], // we won't insert here; we'll delegate to setImages for upload pipeline
            );
            editor
                .chain()
                .focus()
                .setImages(files.map((f) => ({ src: f, alt: f.name, title: f.name })))
                .run();
        },
        onPaste: (editor, files) => {
            if (!files.length) return;
            editor
                .chain()
                .focus()
                .setImages(files.map((f) => ({ src: f, alt: f.name, title: f.name })))
                .run();
        },
        onValidationError: (errors) => {
            errors.forEach((e) =>
                toast.error('Image validation error', {
                    position: 'bottom-right',
                    description: e.reason,
                }),
            );
        },
    }),

    Color,
    TextStyle,
    TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
        defaultAlignment: 'left',
    }),
    Selection,
    Typography,
    UnsetAllMarks,
    HorizontalRule,
    ResetMarksOnEnter,
    CodeBlockLowlight,
    Video.configure({
        allowedProviders: ['youtube', 'vimeo', 'dailymotion'],
        width: 560,
        height: 315,
    }),
    Placeholder.configure({ placeholder: () => placeholder }),
];

export const useMinimalTiptapEditor = ({
    value,
    output = 'html',
    placeholder = '',
    editorClassName,
    throttleDelay = 0,
    onUpdate,
    onBlur,
    uploadConfig,
    ...props
}: UseMinimalTiptapEditorProps) => {
    const throttledSetValue = useThrottle((v: Content) => onUpdate?.(v), throttleDelay);

    const handleUpdate = React.useCallback((editor: Editor) => throttledSetValue(getOutput(editor, output)), [output, throttledSetValue]);

    const handleCreate = React.useCallback(
        (editor: Editor) => {
            if (value && editor.isEmpty) {
                editor.commands.setContent(value);
            }
        },
        [value],
    );

    const handleBlur = React.useCallback((editor: Editor) => onBlur?.(getOutput(editor, output)), [output, onBlur]);

    const editor = useEditor({
        extensions: createExtensions(placeholder, uploadConfig),
        editorProps: {
            attributes: {
                autocomplete: 'off',
                autocorrect: 'off',
                autocapitalize: 'off',
                class: cn('focus:outline-hidden', editorClassName),
            },
        },
        onUpdate: ({ editor }) => handleUpdate(editor),
        onCreate: ({ editor }) => handleCreate(editor),
        onBlur: ({ editor }) => handleBlur(editor),
        ...props,
    });

    return editor;
};

export default useMinimalTiptapEditor;
