import { Banknote, Check, FileText, MousePointerClick } from 'lucide-react';

const steps = [
    {
        title: 'Fill the Form',
        description: `Start by filling out National University's online application form with your academic details. <a class="text-blue-600 font-bold" href="http://app55.nu.edu.bd/nu-web/application/profApplicationForm" target="_blank" rel="noopener noreferrer">Click Here to Apply on NU Website</a>.`,
        icon: FileText,
    },
    {
        title: 'Submit Documents',
        description: 'Upload required documents such as copies/information of your SSC & HSC transcripts and a recent photo.',
        icon: MousePointerClick,
    },
    {
        title: 'College and Course Selection',
        description: `Select <i class="text-green-700 font-bold"> "National Institute of Science & Technology"</i> as desired college and course through National University online portal.`,
        icon: MousePointerClick,
    },
    {
        title: 'Pay Application Fee',
        description: 'Complete the process by paying the application fee through our National University online portal.',
        icon: Banknote,
    },
    { title: 'Confirmation', description: 'Receive your admission confirmation document from NU Website.', icon: Check },
];

const ApplicationProcedure = () => (
    <section className="bg-gray-50 py-16">
        <div className="container mx-auto max-w-4xl px-4">
            <h2 className="text-center text-3xl font-bold text-gray-800">How to Apply</h2>
            <div className="relative mt-12">
                {/* The vertical line */}
                <div className="absolute left-6 h-full w-0.5 bg-gray-200" />
                {steps.map((step, index) => {
                    const Icon = step.icon;
                    return (
                        <div key={index} className="relative pb-12 pl-16">
                            <div className="absolute top-0 left-0 flex h-12 w-12 items-center justify-center rounded-full bg-green-700 text-white">
                                <Icon size={24} />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-800">{step.title}</h3>

                            <div className="mt-2 text-gray-600" dangerouslySetInnerHTML={{ __html: step.description }}></div>
                        </div>
                    );
                })}
            </div>
        </div>
    </section>
);

export default ApplicationProcedure;
