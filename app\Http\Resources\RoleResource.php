<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'label' => $this->label,
            'description' => $this->description,
            'guard_name' => $this->guard_name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
            'permission_names' => $this->when($this->relationLoaded('permissions'), function () {
                return $this->permissions->pluck('name')->toArray();
            }),
            'users_count' => $this->when(isset($this->users_count), $this->users_count),
        ];
    }
}
