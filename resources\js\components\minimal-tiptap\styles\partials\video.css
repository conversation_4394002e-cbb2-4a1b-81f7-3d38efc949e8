/* Video styles */
.minimal-tiptap-editor .video-view-block {
  @apply my-4;
}

.minimal-tiptap-editor .video-view-block iframe {
  @apply max-w-full h-auto;
}

.minimal-tiptap-editor .video-view-block .video-placeholder {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center;
}

.minimal-tiptap-editor .video-view-block .video-placeholder:hover {
  @apply border-gray-400 bg-gray-50;
}

/* Image responsive styles */
.minimal-tiptap-editor .ProseMirror img {
  @apply max-w-full h-auto;
  max-height: 400px;
  object-fit: contain;
}

/* Editor container overflow control */
.minimal-tiptap-editor .ProseMirror {
  @apply overflow-hidden;
  max-width: 100%;
}

.minimal-tiptap-editor {
  @apply overflow-hidden;
  max-width: 100%;
}

/* Video responsive styles */
.minimal-tiptap-editor .ProseMirror iframe {
  @apply max-w-full h-auto;
  max-height: 400px;
  object-fit: contain;
}

/* Alignment styles for images and videos */
.minimal-tiptap-editor .ProseMirror [data-align="left"] {
  @apply text-left;
}

.minimal-tiptap-editor .ProseMirror [data-align="center"] {
  @apply text-center;
}

.minimal-tiptap-editor .ProseMirror [data-align="right"] {
  @apply text-right;
}

/* Text alignment styles */
.minimal-tiptap-editor .ProseMirror [data-text-align="left"] {
  text-align: left;
}

.minimal-tiptap-editor .ProseMirror [data-text-align="center"] {
  text-align: center;
}

.minimal-tiptap-editor .ProseMirror [data-text-align="right"] {
  text-align: right;
}

.minimal-tiptap-editor .ProseMirror [data-text-align="justify"] {
  text-align: justify;
}

/* Ensure text alignment works with headings and paragraphs */
.minimal-tiptap-editor .ProseMirror h1[data-text-align],
.minimal-tiptap-editor .ProseMirror h2[data-text-align],
.minimal-tiptap-editor .ProseMirror h3[data-text-align],
.minimal-tiptap-editor .ProseMirror h4[data-text-align],
.minimal-tiptap-editor .ProseMirror h5[data-text-align],
.minimal-tiptap-editor .ProseMirror h6[data-text-align],
.minimal-tiptap-editor .ProseMirror p[data-text-align] {
  text-align: inherit;
}
