// resources/js/contexts/ConfirmationProvider.tsx
import React, { createContext, useContext, useState, ReactNode } from "react";
import ConfirmationModal, { type ConfirmationModalProps } from "@/components/ui/confirmation-modal";

type ConfirmationContextType = {
  showConfirmation: (
    options: Omit<ConfirmationModalProps, "isOpen" | "onClose" | "onConfirm">
  ) => Promise<boolean>;
};

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

export function ConfirmationProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<Omit<ConfirmationModalProps, "isOpen" | "onClose" | "onConfirm">>({});
  const [resolver, setResolver] = useState<((value: boolean) => void) | null>(null);
  

  const showConfirmation: ConfirmationContextType["showConfirmation"] = (options) => {
    setConfig(options);
    setIsOpen(true);

    return new Promise<boolean>((resolve) => {
      setResolver(() => resolve);
    });
  };

  const handleClose = () => {
    setIsOpen(false);
    setConfig({});
    if (resolver) {
      resolver(false); // if closed without confirm → return false
      setResolver(null);
    }
  };

  const handleConfirm = async () => {
    setIsOpen(false);
    if (resolver) {
      resolver(true); // user confirmed
      setResolver(null);
    }
  };

  return (
    <ConfirmationContext.Provider value={{ showConfirmation }}>
      {children}

      <ConfirmationModal
        isOpen={isOpen}
        onClose={handleClose}
        onConfirm={handleConfirm}
        {...config}
      />
    </ConfirmationContext.Provider>
  );
}

export const useConfirmation = () => {
  const ctx = useContext(ConfirmationContext);
  if (!ctx) throw new Error("useConfirmation must be used within ConfirmationProvider");
  return ctx;
};
