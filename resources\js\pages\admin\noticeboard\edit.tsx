import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Noticeboard, type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Loader2, CalendarIcon } from 'lucide-react';
import { ChangeEvent, FormEventHandler, useState } from 'react';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Content } from '@tiptap/react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';
import '../text-editor/RichTextStyles.css';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Noticeboard',
        href: route('admin.noticeboard.index'),
    },
    {
        title: 'Edit Noticeboard',
        href: '/noticeboard/edit',
    },
    
];

const noticeCategories = [
    { label: 'All Department', value: 'all' },
    { label: 'CSE', value: 'cse' },
    { label: 'BBA', value: 'bba' },
    { label: 'BMB', value: 'bmb' },
    { label: 'ECE', value: 'ece' },
    { label: 'Academic', value: 'academic' },
    { label: 'Official', value: 'official' },
];

export default function NoticeboardEdit({ currentNoticeboard, draftToken }: { currentNoticeboard: Noticeboard; draftToken: string }) {
    const [title, setTitle] = useState<string>(currentNoticeboard.title);
    const [category, setCategory] = useState<string>(currentNoticeboard.category);
    const [content, setContent] = useState<string>(currentNoticeboard.content);
    const [image, setImage] = useState<File | null>(null);
    const [attachment, setAttachment] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [removeImage, setRemoveImage] = useState<boolean>(false);
    const [removeAttachment, setRemoveAttachment] = useState<boolean>(false);
    const [processing, setProcessing] = useState<boolean>(false);
    const [value, setValue] = useState<Content>(null);
    const [publishedDate, setPublishedDate] = useState<Date>(new Date(currentNoticeboard.published_at));
    const { errors } = usePage().props;


    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(
            route('admin.noticeboard.update', currentNoticeboard.id),
            {
                _method: 'put',
                title,
                category,
                content,
                image,
                attachment,
                published_at: publishedDate ? format(publishedDate, 'yyyy-MM-dd') : '',
                draft_token: draftToken,
                //Send the removal flags to the backend
                remove_image: removeImage,
                remove_attachment: removeAttachment,
            },
            {
                onFinish: () => setProcessing(false),
            },
        );
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImage(file);
            setImagePreview(URL.createObjectURL(file));
            setRemoveImage(false);
        }
    };

    const handleAttachmentChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setAttachment(file);
            setRemoveAttachment(false);
        }
    };

    // ✨ CHANGED: Handlers for the new "Remove" buttons
    const handleRemoveImage = () => {
        setRemoveImage(true);
        setImage(null);
        setImagePreview(null);
    };

    const handleRemoveAttachment = () => {
        setRemoveAttachment(true);
        setAttachment(null);
    };

    function fetchFileNameFromUrl(url: string) {
        return url.split('/').pop();
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Noticeboard" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Edit Noticeboard</div>
                        <Button>
                            <Link href={route('admin.noticeboard.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form className="space-y-6" onSubmit={submit}>
                                <div className="grid gap-2">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="title">Title *</Label>
                                            <Input
                                                id="title"
                                                type="text"
                                                value={title}
                                                onChange={(e) => setTitle(e.target.value)}
                                                placeholder="Enter notice title"
                                            />
                                            <InputError message={errors.title} />
                                        </div>
                                        <div>
                                            <Label htmlFor="category">Category *</Label>
                                            <Select value={category} onValueChange={setCategory}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select category" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {noticeCategories.map((option) => (
                                                        <SelectItem key={option.value} value={option.value}>
                                                            {option.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.category} />
                                        </div>
                                    </div>
                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="content">Notice Post *</Label>
                                        <MinimalTiptapEditor
                                            value={currentNoticeboard.content}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setContent(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="min-h-[200px]"
                                            placeholder="Enter notice content..."
                                        />
                                        <InputError message={errors.content} />
                                    </div>
                                    <div className="grid gap-2">
                                        Notice Content Preview:
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (value as string) || '' }} />
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="published_at">Published Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal",
                                                        !publishedDate && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedDate ? format(publishedDate, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedDate}
                                                    onSelect={setPublishedDate}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.published_at} />
                                    </div>


                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="image">Image</Label>
                                        <Input id="image" type="file" accept="image/*" onChange={handleFileChange} />

                                        <div className="flex space-x-4">
                                            <div>
                                                <p className="mb-2 text-sm text-gray-600">Current Image:</p>
                                                {currentNoticeboard.image ? (
                                                    <img
                                                        src={currentNoticeboard.image}
                                                        alt={currentNoticeboard.title}
                                                        className="h-20 w-20 rounded object-cover"
                                                    />
                                                ) : (
                                                    <p>No Image</p>
                                                )}
                                            </div>
                                            {imagePreview && (
                                                <div>
                                                    <p className="mb-2 text-sm text-gray-600">New Image:</p>
                                                    <img src={imagePreview} alt="Preview" className="h-20 w-20 rounded object-cover" />
                                                </div>
                                            )}
                                        </div>
                                        <InputError message={errors.image} />
                                    </div>

                                    <div>
                                        <Label htmlFor="attachment">Attachment</Label>
                                        <Input id="attachment" type="file" onChange={handleAttachmentChange} />
                                        <InputError message={errors.attachment} />
                                        {/* ✨ CHANGED: Logic for viewing and removing attachment */}
                                        {currentNoticeboard.attachment && !removeAttachment && (
                                            <div className="mt-2 flex items-center justify-between">
                                                {/* Text on the left */}
                                                <p className="text-sm text-gray-600">
                                                    Current Attachment: {fetchFileNameFromUrl(currentNoticeboard.attachment)}
                                                </p>

                                                {/* Buttons on the right, grouped in a div */}
                                                <div className="flex items-center gap-2">
                                                    <a href={currentNoticeboard.attachment} target="_blank" rel="noopener noreferrer">
                                                        <Button type="button" variant="outline">
                                                            View Current Attachment
                                                        </Button>
                                                    </a>
                                                    <Button type="button" variant="destructive" size="sm" onClick={handleRemoveAttachment}>
                                                        Remove Attachment
                                                    </Button>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    </div>

                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline">
                                            <Link href={route('admin.noticeboard.index')} prefetch>
                                                Cancel
                                            </Link>
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            {processing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Update Notice
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
