<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\RoleRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Str;


class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');

        $roles = Role::query()
            ->with('permissions')
            ->when($search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        $permissions = Permission::get()->groupBy('module');

        return Inertia::render('admin/access-control/roles/RolesIndex', [
            'roles' => $roles,
            //'permissions' => $permissions,
            'filters' => [
                'search' => $search,
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RoleRequest $request)
    {
        $role = Role::create([
            'label' => $request->label,
            'name' => Str::slug($request->label),
            'description' => $request->description,
        ]);

        if ($role) {
            $role->syncPermissions($request->permissions);
            return redirect()->route('admin.access-control.index', ['tab' => 'roles'])
                ->with('success', 'Role created successfully.');

        }
        return redirect()->back()->with('error', 'Unable to create Role with permissions. Please try again!');


    }


    /**
     * Update the specified resource in storage.
     */
    public function update(RoleRequest $request, Role $role)
    {
        /* if($role->name === 'super_admin' || $role->name === 'admin' || $role->name === 'editor'){
            return redirect()->back()->with('error', 'Cannot update core system roles.');
        }*/

        if ($role) {
            $role->label = $request->label;
            $role->name = Str::slug($request->label);
            $role->description = $request->description;
            $role->save();
            //update the perssions
            $role->syncPermissions($request->permissions);
            return redirect()->route('admin.access-control.index', ['tab' => 'roles'])
                ->with('success', 'Role updated successfully.');
        }

        return redirect()->back()->with('error', 'Unable to Update Role with permissions. Please try again!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        // Prevent deletion of core roles
        /* if (in_array($role->name, ['super_admin', 'admin', 'editor'])) {
             return redirect()->route('admin.access-control.index', ['tab' => 'roles'])
                 ->with('danger', 'Cannot delete core system roles.');
         }*/
        if ($role) {
            $role->delete();

            return redirect()->route('admin.access-control.index', ['tab' => 'roles'])
                ->with('success', 'Role deleted successfully.');
        }

        return redirect()->back()->with('error', 'Unable to delete Role. Please try again!');


    }


}
