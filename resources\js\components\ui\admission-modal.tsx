import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AdmissionModalData {
  id: number;
  campaign_name: string | null;
  title: string | null;
  active_status: 'active' | 'inactive';
  published_date: string;
  image: string;
}

interface AdmissionModalProps {
  modalData: AdmissionModalData | null;
  onClose: () => void;
  onView: () => void;
}

const AdmissionModal = ({ modalData, onClose, onView }: AdmissionModalProps) => {
  const [showModal, setShowModal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (modalData) {
      // Reset states when new modal data comes
      setImageLoaded(false);
      setShowModal(false);
      setIsVisible(false);

      // Preload the image
      const img = new Image();
      img.onload = () => {
        setImageLoaded(true);
        // Show modal after image loads + additional delay
        const timer = setTimeout(() => {
          setShowModal(true);
          // Add slight delay for visibility transition
          setTimeout(() => setIsVisible(true), 50);
        }, 5000); // Increased delay to 5 seconds

        return () => clearTimeout(timer);
      };
      img.onerror = () => {
        // Even if image fails to load, show modal after delay
        const timer = setTimeout(() => {
          setImageLoaded(true);
          setShowModal(true);
          // Add slight delay for visibility transition
          setTimeout(() => setIsVisible(true), 50);
        }, 5000);

        return () => clearTimeout(timer);
      };
      img.src = modalData.image;
    }
  }, [modalData]);

  // Don't render if no data, image not loaded, or modal shouldn't show
  if (!modalData || !imageLoaded || !showModal) return null;

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      setShowModal(false);
      onClose();
    }, 300); // Wait for transition to complete
  };

  const handleView = () => {
    setIsVisible(false);
    setTimeout(() => {
      setShowModal(false);
      onView();
    }, 300); // Wait for transition to complete
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black/50 transition-opacity duration-300 ${
      isVisible ? 'opacity-100' : 'opacity-0'
    }`}>
      <div className={`relative mx-4 w-full max-w-lg overflow-hidden rounded-lg bg-white shadow-2xl transition-all duration-300 transform ${
        isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`}>
        {/* Header with Title and Close Button */}
        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
          <h3 className="text-lg font-semibold text-gray-800">
            {modalData.title || modalData.campaign_name || "Admission Notice"}
          </h3>
          <button
            onClick={handleClose}
            className="flex h-6 w-6 items-center justify-center rounded-full text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Modal Image */}
        <div className="relative p-2">
          <img
            src={modalData.image}
            alt={modalData.title || modalData.campaign_name || "Admission Notice"}
            className="h-auto w-full object-cover"
          />
        </div>

        {/* Modal Footer with smaller buttons */}
        <div className="flex justify-end gap-2 p-4">
          <Button
            onClick={handleView}
            size="sm"
            className="bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700"
          >
            Apply Now
          </Button>
          <Button
            onClick={handleClose}
            size="sm"
            variant="destructive"
            className="bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdmissionModal;


