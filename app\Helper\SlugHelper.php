<?php
namespace App\Helper;

use Illuminate\Support\Facades\DB;

class SlugHelper
{
    /**
     * Generate a clean, Bangla-safe slug with max length and DB uniqueness.
     */
    public static function makeSlug(string $title, string $table, string $column = 'slug', int $limit = 70): string
    {
        // Step 1: Remove unwanted symbols but keep Bangla/English letters + marks + numbers
        // \p{N} = any Unicode number (keeps 0–9 and ০–৯)
        $slug = preg_replace('/[^\p{L}\p{M}\p{N}\s-]/u', '', $title);

        // Step 2: Replace spaces with dashes
        $slug = preg_replace('/\s+/u', '-', trim($slug));

        // Step 3: Collapse multiple dashes into one
        $slug = preg_replace('/-+/', '-', $slug);

        // Step 4: Convert to lowercase (SEO-friendly)
        $slug = mb_strtolower($slug, 'UTF-8');

        // Step 5: Trim length (max 70 chars)
        if (mb_strlen($slug, 'UTF-8') > $limit) {
            $slug = mb_substr($slug, 0, $limit, 'UTF-8');
            $slug = rtrim($slug, '-');
        }

        // Step 6: Ensure uniqueness in DB
        $original = $slug;
        $counter = 1;

        while (DB::table($table)->where($column, $slug)->exists()) {
            $suffix = '-' . $counter++;
            $cutLength = $limit - mb_strlen($suffix, 'UTF-8');
            $slug = mb_substr($original, 0, $cutLength, 'UTF-8') . $suffix;
        }

        return $slug;
    }
}