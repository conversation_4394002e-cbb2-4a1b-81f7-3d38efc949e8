<?php

namespace App\Http\Resources;

use App\Models\GoverningBody;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GoverningbodyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public static $wrap = null; //adding static property to avoid adding "data" key in the response
    public function toArray(Request $request): array
    {
        
        return [
            "id" => $this->id,
            "name" => $this->name,
            "designation" => $this->designation,
            "display_order" => $this->display_order,
            "profile_URL" => $this->profile_URL,
            "profile_image" => asset('storage/'.$this->profile_image),
        ];
    }
}