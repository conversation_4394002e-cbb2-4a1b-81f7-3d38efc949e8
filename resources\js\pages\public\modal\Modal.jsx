import { useState, useEffect } from "react";
import "./Modal.css";


const Modal = ({ onClose, onView }) => {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowModal(false); //true for active
    }, 5000); // Show modal after 5 seconds

    return () => clearTimeout(timer); // Cleanup timer on unmount
  }, []);

  if (!showModal) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <button className="modal-close" onClick={onClose}>
          ✖
        </button>
        <img
          src="/images/nist/popup.jpg" // Replace with your actual image URL or import
          alt="Notice"
          className="modal-image"
        />
        <div className="modal-footer">
          <button className="modal-btn view-btn" onClick={onView}>
            View
          </button>
          <button className="modal-btn close-btn" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
